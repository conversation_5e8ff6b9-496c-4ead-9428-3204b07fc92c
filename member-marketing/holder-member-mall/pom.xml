<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.holderzone.member</groupId>
        <artifactId>member-marketing</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>holder-member-mall</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>holder-member-mall</name>
    <description>会员商城</description>
    <properties>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.holderzone.member</groupId>
            <artifactId>holder-member-common</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream-binder-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--Feign增强-->
        <dependency>
            <groupId>com.holderzone</groupId>
            <artifactId>feign-spring-boot-starter</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.holderzone.framework</groupId>
                    <artifactId>framework-sdk-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--阿里云oss-->
        <dependency>
            <groupId>com.holderzone.framework</groupId>
            <artifactId>framework-oss-sdk-starter</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.4.0</version>
        </dependency>
        <!--物流公司第三方jar包-->
        <dependency>
            <groupId>com.best</groupId>
            <artifactId>javaSdk</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/bestsdk.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.zto</groupId>
            <artifactId>zop</artifactId>
            <version>0.9</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/zopsdk-0.9.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.yl.jms</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/jt-openapi-sdk-1.0.0.jar</systemPath>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </repository>
        <repository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>snapshots</id>
            <name>Nexus Snapshots</name>
            <url>${custom_maven_url}/content/repositories/snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>thirdparty</id>
            <name>Nexus ThirdParty</name>
            <url>${custom_maven_url}/content/repositories/thirdparty/</url>
        </pluginRepository>
        <pluginRepository>
            <id>releases</id>
            <name>Nexus Releases</name>
            <url>${custom_maven_url}/content/repositories/releases/</url>
        </pluginRepository>
        <pluginRepository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
