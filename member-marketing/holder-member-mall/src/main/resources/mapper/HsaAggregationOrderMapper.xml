<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.order.HsaAggregationOrderMapper">


    <select id="query" resultType="com.holderzone.member.common.qo.order.AggregationOrderResponseVO">
        select
            guid,
            third_order_guid,
            third_enterprise_guid,
            order_no,
            store_name,
            order_fee,
            IFNULL(actually_pay_fee, 0) as "actually_pay_fee",
            state,
            business_type,
            checkin_time
        from
            hsa_aggregation_order
        <where>
            is_delete = 0 and oper_subject_guid = #{query.operSubjectGuid}
            and member_info_guid = #{query.memberInfoGuid}
            <choose>
                <when test="query.orderSource == 2">
                    and business_type = 'DEVICE_ORDER'
                </when>
                <otherwise>
                    and business_type = 'SCAN_CODE'
                </otherwise>
            </choose>
        </where>
        order by checkin_time desc, id asc
    </select>
</mapper>