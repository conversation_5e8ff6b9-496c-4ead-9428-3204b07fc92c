<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.order.HsaMallBaseOrderMapper">


    <select id="findMallBaseOrderPage" resultType="com.holderzone.member.common.vo.order.MallBaseOrderVO">
        select
        base.guid as guid,
        base.order_number as orderNumber,
        base.gmt_create as gmtCreate,
        base.order_condition as orderCondition,
        base.delivery_method as deliveryMethod,
        base.freight_amount as freightAmount,
        base.order_preferential_amount as orderDiscountAmount,
        base.order_paid_amount as orderPaidAmount,
        base.store_guid as storeGuid,
        base.store_name as storeName,
        base.stall_guid as stallGuid,
        base.stall_name as stallName,
        base.member_phone as memberPhone<PERSON>um,
        base.member_name as memberName,
        base.order_after_condition,
        base.sender_time as senderTime,
        hora.receiver_phone as receiverPhone,
        hora.receiver_name as receiverName,
        hora.receiver_address as receiverAddress


        from hsa_mall_base_order base
        left join hsa_order_receiver_address hora on base.receiver_guid = hora.guid
        <if test="request.keywords != null  and request.keywords != ''">
            LEFT JOIN hsa_product_order_detail product ON product.order_guid = base.guid
        </if>
        where base.oper_subject_guid= #{request.operSubjectGuid}

        <if test="request.orderNumber != null  and request.orderNumber != ''">
            AND base.order_number like CONCAT('%',#{request.orderNumber},'%')
        </if>

        <if test="request.memberName != null  and request.memberName != ''">
            AND base.member_name like CONCAT('%',#{request.memberName},'%')
        </if>

        <if test="request.memberPhone != null  and request.memberPhone != ''">
            AND base.member_phone like CONCAT('%',#{request.memberPhone},'%')
        </if>
        <if test="request.receiverPhone != null and request.receiverPhone != ''">
            AND hora.receiver_phone like CONCAT('%',#{request.receiverPhone},'%')
        </if>

        <if test="request.orderCondition != null">
            AND base.order_condition = #{request.orderCondition}
        </if>
        <if test="request.orderStartCreate  != null">
             <![CDATA[and base.gmt_create >= DATE_FORMAT(#{request.orderStartCreate},'%Y-%m-%d 00:00:00')]]>
        </if>
        <if test="request.orderEndCreate != null">
            <![CDATA[and base.gmt_create <= DATE_FORMAT(#{request.orderEndCreate},'%Y-%m-%d 23:59:59')]]>
        </if>

        <if test="request.orderAfterConditionList != null and request.orderAfterConditionList.size > 0 ">
            AND base.order_after_condition in
            <foreach collection="request.orderAfterConditionList" index="index" item="orderAfterCondition" separator="," open="(" close=")">
                #{orderAfterCondition}
            </foreach>
        </if>

        <if test="request.keywords != null  and request.keywords != ''">
            and (product.product_name like CONCAT('%',#{request.keywords},'%')
            or product.product_number like CONCAT('%',#{request.keywords},'%'))
        </if>
        <choose>
            <when test="request.orderCondition == null">
                ORDER BY base.id DESC
            </when>
            <when test="request.orderCondition != null and request.orderCondition == 1 ">
                ORDER BY base.id ASC
            </when>
            <otherwise>
                order by base.sender_time desc
            </otherwise>
        </choose>

    </select>

    <select id="findMallBaseOrderCount" resultType="java.lang.Integer">
        select
        COUNT(1)
        from hsa_mall_base_order base
        left join hsa_order_receiver_address hora on base.receiver_guid = hora.guid
        <if test="request.keywords != null  and request.keywords != ''">
            LEFT JOIN hsa_product_order_detail product ON product.order_guid = base.guid
        </if>
        where base.oper_subject_guid= #{request.operSubjectGuid}

        <if test="request.orderNumber != null  and request.orderNumber != ''">
            AND base.order_number like CONCAT('%',#{request.orderNumber},'%')
        </if>

        <if test="request.memberName != null  and request.memberName != ''">
            AND base.member_name like CONCAT('%',#{request.memberName},'%')
        </if>

        <if test="request.memberPhone != null  and request.memberPhone != ''">
            AND base.member_phone like CONCAT('%',#{request.memberPhone},'%')
        </if>

        <if test="request.receiverPhone != null and request.receiverPhone != ''">
            AND hora.receiver_phone like CONCAT('%',#{request.receiverPhone},'%')
        </if>

        <if test="request.orderCondition != null">
            AND base.order_condition = #{request.orderCondition}
        </if>

        <if test="request.orderStartCreate  != null and request.orderStartCreate!= ''">
            AND base.gmt_create &gt;= DATE_FORMAT(#{request.orderStartCreate}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="request.orderEndCreate != null and request.orderEndCreate != ''">
            AND base.gmt_create &lt;= DATE_FORMAT(#{request.orderEndCreate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="request.orderAfterConditionList != null and request.orderAfterConditionList.size > 0 ">
            AND base.order_after_condition in
            <foreach collection="request.orderAfterConditionList" index="index" item="orderAfterCondition" separator="," open="(" close=")">
                #{orderAfterCondition}
            </foreach>
        </if>

        <if test="request.keywords != null  and request.keywords != ''">
            and (product.product_name like CONCAT('%',#{request.keywords},'%')
            or product.product_number like CONCAT('%',#{request.keywords},'%'))
        </if>
    </select>

    <update id="updatePayStateByGuid">
        update hsa_mall_base_order set pay_condition = #{code}
        where guid = #{orderGuid}
    </update>

    <select id="queryPayStateByGuid" resultType="java.lang.Integer">
        select pay_condition from hsa_mall_base_order where order_condition = 0 and guid = #{orderGuid}
    </select>

    <select id="queryOrderStatusByGuid" resultType="com.holderzone.member.common.dto.mall.OrderStatusDTO">
        select
            guid,
            pay_condition AS payCondition,
            order_condition AS orderCondition,
            order_process_json AS orderProcessJson
        from hsa_mall_base_order where guid = #{orderGuid}
    </select>
    <update id="updateOrderStatusByGuid" >
        update hsa_mall_base_order
        <set>
            <if test="dto.orderCondition != null">
                order_condition = #{dto.orderCondition},
            </if>
            <if test="dto.payCondition != null">
                pay_condition = #{dto.payCondition},
            </if>
            <if test="dto.orderProcessJson != null">
                order_process_json = #{dto.orderProcessJson},
            </if>
            <if test="dto.paymentTime != null">
                payment_time = #{dto.paymentTime},
            </if>
        </set>
        where guid = #{dto.guid}
    </update>

    <update id="updateOrderCancel">
        update hsa_mall_base_order
        set cancel_time = NOW(),
            cancel_reason = #{reason},
            cancel_type = #{cancelType},
            order_condition = 4
        where guid = #{orderGuid}
    </update>

    <update id="updateRefundCondition">
        update hsa_mall_base_order set order_after_condition = #{refundCondition}
        WHERE guid in
        <foreach collection="list" index="index" item="orderGuid" separator="," open="(" close=")">
            #{orderGuid}
        </foreach>
    </update>
</mapper>