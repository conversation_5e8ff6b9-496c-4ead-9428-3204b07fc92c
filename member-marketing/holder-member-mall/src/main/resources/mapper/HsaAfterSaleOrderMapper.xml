<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.order.HsaAfterSaleOrderMapper">


    <select id="findAfterSaleOrderList" resultType="com.holderzone.member.common.vo.order.AfterSaleOrderVO">
        select
        after.guid as guid,
        after.order_guid as orderGuid,
        after.after_order_num as afterOrderNum,
        after.refund_condition as refundCondition,
        after.order_number as orderNumber,
        after.pay_order_time as payOrderTime,
        base.order_condition as orderCondition,
        after.actual_refund_amount as actualRefundAmount,
        after.refund_amount as refundAmount,
        after.cancel as cancel,
        base.store_guid as storeGuid,
        base.store_name as storeName,
        base.member_phone as memberPhoneNum,
        base.member_name as memberName,
        after.refund_way as refundWay,
        after.refund_type as refundType,
        after.deal_type as dealType,
        after.gmt_create as gmtCreate
        from hsa_after_sale_order after
        LEFT JOIN hsa_mall_base_order base ON base.guid = after.order_guid
        where base.oper_subject_guid= #{request.operSubjectGuid}

        <if test="request.guid != null  and request.guid != ''">
            AND after.guid =#{request.guid}
        </if>

        <if test="request.afterOrderNum != null  and request.afterOrderNum != ''">
            AND after.after_order_num like CONCAT('%',#{request.afterOrderNum},'%')
        </if>

        <if test="request.orderNumber != null  and request.orderNumber != ''">
            AND after.order_number like CONCAT('%',#{request.orderNumber},'%')
        </if>

        <if test="request.refundType != null">
            AND after.refund_type =#{request.refundType}
        </if>

        <if test="request.orderCondition != null">
            AND after.order_condition = #{request.orderCondition}
        </if>

        <if test="request.orderCondition != null">
            AND base.order_condition =#{request.orderCondition}
        </if>
        <if test="request.memberPhone != null and request.memberPhone !=''">
            AND base.member_phone =#{request.memberPhone}
        </if>
        <!--
           <if test="request.refundCondition != null">
                AND after.refund_condition =#{request.refundCondition}
            </if>
         -->
        <if test="request.refundConditionList != null and request.refundConditionList.size > 0 ">
            AND after.refund_condition in
            <foreach collection="request.refundConditionList" index="index" item="refundCondition" separator="," open="(" close=")">
                #{refundCondition}
            </foreach>
        </if>
        <if test="request.dealType != null">
            AND after.deal_type =#{request.dealType}
        </if>

        <if test="request.orderStartCreate  != null ">
            AND after.pay_order_time &gt;= DATE_FORMAT(#{request.orderStartCreate}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="request.orderEndCreate != null ">
            AND after.pay_order_time &lt;= DATE_FORMAT(#{request.orderEndCreate}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="request.orderRefundStartCreate != null ">
            AND after.gmt_create &gt;= DATE_FORMAT(#{request.orderRefundStartCreate}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="request.orderRefundEndCreate != null ">
            AND after.gmt_create &lt;= DATE_FORMAT(#{request.orderRefundEndCreate}, '%Y-%m-%d 23:59:59')
        </if>
        <if test="request.dealType == null or request.dealType == 1">
            ORDER BY after.id DESC
        </if>
        <if test="request.dealType == 0">
            ORDER BY after.id ASC
        </if>

    </select>

    <select id="findAfterSaleOrderCount" resultType="java.lang.Integer">
        select
        COUNT(1)
        from hsa_after_sale_order after
        LEFT JOIN hsa_mall_base_order base ON base.guid = after.order_guid
        where base.oper_subject_guid= #{request.operSubjectGuid}

        <if test="request.afterOrderNum != null  and request.afterOrderNum != ''">
            AND after.after_order_num like CONCAT('%',#{request.afterOrderNum},'%')
        </if>

        <if test="request.orderNumber != null  and request.orderNumber != ''">
            AND after.order_number like CONCAT('%',#{request.orderNumber},'%')
        </if>

        <if test="request.refundType != null">
            AND after.refund_type =#{request.refundType}
        </if>

        <if test="request.orderCondition != null">
            AND after.order_condition = #{request.orderCondition}
        </if>

        <if test="request.orderCondition != null">
            AND base.order_condition =#{request.orderCondition}
        </if>

        <if test="request.refundCondition != null">
            AND after.refund_condition =#{request.refundCondition}
        </if>

        <if test="request.dealType != null">
            AND after.deal_type =#{request.dealType}
        </if>

        <if test="request.orderStartCreate  != null and request.orderStartCreate!= ''">
            AND after.pay_order_time &gt;= DATE_FORMAT(#{request.orderStartCreate}, '%Y-%m-%d 00:00:00')
        </if>

        <if test="request.orderEndCreate != null and request.orderEndCreate != ''">
            AND after.pay_order_time &lt;= DATE_FORMAT(#{request.orderEndCreate}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="request.orderRefundStartCreate != null and request.orderRefundStartCreate != ''">
            AND after.gmt_create &lt;= DATE_FORMAT(#{request.orderRefundStartCreate}, '%Y-%m-%d 23:59:59')
        </if>

        <if test="request.orderRefundEndCreate != null and request.orderRefundEndCreate != ''">
            AND after.gmt_create &lt;= DATE_FORMAT(#{request.orderRefundEndCreate}, '%Y-%m-%d 23:59:59')
        </if>
    </select>

    <update id="batchRefuseRefund">
        update hsa_after_sale_order
        <set>
            refund_condition = #{refuseRefund.cancelType},
            deal_type = 1
            <if test="refuseRefund.refuseReason != null">
                ,refuse_reason = #{refuseRefund.refuseReason}
            </if>
        </set>
        WHERE guid in
        <foreach collection="refuseRefund.afterOrderGuidList" index="index" item="orderGuid" separator="," open="(" close=")">
            #{orderGuid}
        </foreach>
        and refund_condition = 0
    </update>
</mapper>