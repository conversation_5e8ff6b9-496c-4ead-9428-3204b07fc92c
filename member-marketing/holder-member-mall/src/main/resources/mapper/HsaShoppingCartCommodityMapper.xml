<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.shoppingcart.HsaShoppingCartCommodityMapper">

    <update id="changeNum">
        UPDATE hsa_shopping_cart_commodity SET
        <if test="type == 1">
            num = NULLIF(num,0) + #{changeNum}
        </if>
        <if test="type == 2">
            num = num - #{changeNum}
        </if>
        <if test="type == 3">
            num = #{changeNum}
        </if>
        <if test="updateTime != null">
            ,update_time = NOW()
        </if>
        WHERE guid = #{shoppingCartGuid}
        AND num > 0
        <!-- 不能减为0 -->
        <if test="type == 2">
            and num > #{changeNum}
        </if>

    </update>

    <select id="listStoreId" resultType="java.lang.Integer">
        SELECT
            store_id
        FROM
            `hsa_shopping_cart_commodity`
        WHERE
            is_delete = 0
            AND oper_subject_guid = #{operSubjectGuid}
            AND member_info_guid = #{memberInfoGuid}
        GROUP BY store_id
        ORDER BY MAX( update_time ) DESC
    </select>

    <select id="listShoppingCartCommodity"
            resultType="com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity">
        SELECT
            guid,
            store_id,
            oper_subject_guid,
            member_info_guid,
            commodity_id,
            num,
            sku,
            update_time,
            attr,
            commodity_name,
            commodity_img,
            base_price,
            group_type,
            commodity_code
        FROM
            `hsa_shopping_cart_commodity`
        WHERE
        guid IN
        <foreach collection="shoppingCartGuidList" index="index" item="shoppingCartGuid" separator="," open="(" close=")">
            #{shoppingCartGuid}
        </foreach>
    </select>
</mapper>
