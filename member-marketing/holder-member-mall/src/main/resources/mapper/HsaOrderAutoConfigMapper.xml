<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.order.HsaOrderAutoConfigMapper">

    <update id="deleteRefundConfirmTime">
        update hsa_order_auto_config
        <set>
            order_refund_confirm_time = null
        </set>
        WHERE order_guid in
        <foreach collection="listOrderList" index="index" item="orderGuid" separator="," open="(" close=")">
            #{orderGuid}
        </foreach>
        and refund_confirm_state = 0
    </update>
</mapper>