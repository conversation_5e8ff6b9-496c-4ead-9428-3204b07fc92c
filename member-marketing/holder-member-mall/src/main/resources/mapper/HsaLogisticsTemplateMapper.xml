<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.holderzone.member.mall.mapper.logistics.HsaLogisticsTemplateMapper">


    <select id="list" resultType="com.holderzone.member.common.vo.logistics.LogisticsTemplateVO">
        select
            lt.guid,
            lt.template_name,
            lt.default_flag,
            lt.charge_type,
            lt.freight_amount,
            lt.charges
        from
            hsa_logistics_template lt
        where
            lt.oper_subject_guid = #{operSubjectGuid} and lt.is_delete = 0
        order by lt.id desc
    </select>


    <select id="guidList" resultType="com.holderzone.member.common.vo.logistics.LogisticsTemplateVO">
        select
            guid,
            template_name
        from
            hsa_logistics_template
        where
            oper_subject_guid = #{operSubjectGuid} and is_delete = 0 and guid != #{excludeTemplateGuid}
        order by id desc
    </select>

    <select id="queryDefaultTemplateGuid" resultType="java.lang.String">
        select
            guid
        from
            hsa_logistics_template
        where
            oper_subject_guid = #{operSubjectGuid} and is_delete = 0
        limit 1
    </select>


</mapper>