spring:
  cloud:
    nacos:
      discovery:
        server-addr: holder-member-nacos
        namespace: 057aa3fe-df7a-451a-bb20-305648853e30
#        group: DEVELOP_GROUP
      config:
        server-addr: holder-member-nacos
        namespace: 057aa3fe-df7a-451a-bb20-305648853e30
#        group: DEVELOP_GROUP
        file-extension: yaml
        shared-configs:
          - data-id: datasource-mall.yaml # 配置文件名-Data Id
            #            group: DEVELOP_GROUP   # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
          - data-id: datasource-sharding.yaml # 配置文件名-Data Id
            #            group: DEVELOP_GROUP   # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
          - data-id: mall-logistics.yaml
            refresh: true
          - data-id: common-feign.yaml
            refresh: true