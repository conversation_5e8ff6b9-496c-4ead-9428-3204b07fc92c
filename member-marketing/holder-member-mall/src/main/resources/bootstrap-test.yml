spring:
  cloud:
    nacos:
      discovery:
        server-addr: test-holder-member-nacos
        namespace: b60ab46b-a21a-4def-9fa0-f6cb3a890577
#        group: DEVELOP_GROUP
      config:
        server-addr: test-holder-member-nacos
        namespace: b60ab46b-a21a-4def-9fa0-f6cb3a890577
#        group: DEVELOP_GROUP
        file-extension: yaml
        shared-configs:
          - data-id: datasource-mall.yaml # 配置文件名-Data Id
#            group: DEVELOP_GROUP   # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
          - data-id: datasource-sharding.yaml # 配置文件名-Data Id
#            group: DEVELOP_GROUP   # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
          - data-id: mall-logistics.yaml
            refresh: true
          - data-id: common-feign.yaml
            refresh: true