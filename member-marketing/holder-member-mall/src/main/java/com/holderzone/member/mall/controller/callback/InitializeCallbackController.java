package com.holderzone.member.mall.controller.callback;

import com.holderzone.member.mall.service.callback.InitializeCallbackService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 初始化回调接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/initialize")
@Slf4j
public class InitializeCallbackController {

    @Resource
    private InitializeCallbackService initializeCallbackService;

    /**
     * 初始化运营主体相关数据
     * @param subjectInfoList 运营主体
     */
    @ApiOperation("初始化运营主体相关数据")
    @PostMapping(value = "/init_subject_data")
    public void initializeSubjectData(@RequestBody List<String> subjectInfoList) {
        log.info("初始化运营主体相关数据传入参数=====>" + subjectInfoList);
        initializeCallbackService.initializeSubjectData(subjectInfoList);
    }
}
