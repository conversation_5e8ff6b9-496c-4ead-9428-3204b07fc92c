package com.holderzone.member.mall.provider.logistics.zto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse {

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 快件所在国家
     */
    private String country;

    /**
     * 签收人
     */
    private String signMan;

    /**
     * 操作人
     */
    private String operateUser;

    /**
     * 操作人联系方式
     */
    private String operateUserPhone;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 扫描类型:收件 、发件、 到件、 派件、 签收、退件、问题件、ARRIVAL （派件入三方自提柜等、SIGNED（派件出三方自提柜等）
     */
    private String scanType;

    /**
     * 操作人Code
     */
    private String operateUserCode;

    /**
     * 上、下一站网点信息（scanType为发件，表示下一站；scanType为到件，表示上一站）
     */
    private TraceSiteInfoOutput preOrNextSite;

    /**
     * 扫描时间
     */
    private LocalDateTime scanDate;

    /**
     * 扫描网点
     */
    private TraceSiteInfoOutput scanSite;

    /**
     * 轨迹描述
     */
    private String desc;

    public static LogisticsOrderTrackResponse buildCommonResponse(String json){
        ZtoBaseRsp<OrderTrackResponse> rsp = JSONObject.parseObject(json, new TypeReference<ZtoBaseRsp<OrderTrackResponse>>() {
        }.getType());

        if(rsp == null || rsp.getResult() == null) {
            return null;
        }
        //TODO
        return LogisticsOrderTrackResponse.builder().build();

    }
}
