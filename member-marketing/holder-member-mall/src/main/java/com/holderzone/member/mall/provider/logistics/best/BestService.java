package com.holderzone.member.mall.provider.logistics.best;

import com.alibaba.fastjson.JSONObject;
import com.best.javaSdk.Client;
import com.best.javaSdk.kyCancelOrderNotify.request.KyCancelOrderNotifyReq;
import com.best.javaSdk.kyCancelOrderNotify.response.KyCancelOrderNotifyRsp;
import com.best.javaSdk.kyCreateOrderNotify.request.KyCreateOrderNotifyReq;
import com.best.javaSdk.kyCreateOrderNotify.response.KyCreateOrderNotifyRsp;
import com.best.javaSdk.kyPriceAgingSearch.request.KyPriceAgingSearchReq;
import com.best.javaSdk.kyPriceAgingSearch.response.KyPriceAgingSearchRsp;
import com.best.javaSdk.kyTraceQuery.request.KyTraceQueryReq;
import com.best.javaSdk.kyTraceQuery.response.KyTraceQueryRsp;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.best.config.BestRequestConfig;
import com.holderzone.member.mall.provider.logistics.converter.BestConverter;
import com.holderzone.member.mall.provider.logistics.converter.BestRspConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 百世快递服务(<a href="https://open.800best.com/"/>)
 * 需要在官网下载最新sdk包 打包成jar放在项目 在快递平台进行联调测试
 * <AUTHOR>
 */

@Slf4j
@Service
public class BestService extends AbstractLogisticsService {

    @Resource
    private BestRequestConfig bestRequestConfig;

    private Client client;

    @PostConstruct
    void initClient(){
        client = new Client(bestRequestConfig.getUrl(), bestRequestConfig.getPartnerID()
                , bestRequestConfig.getPartnerKey(), "JSON");
    }

    public ExpressTypeEnum type() {
        return ExpressTypeEnum.BEST;
    }

    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        KyCreateOrderNotifyReq kyCreateOrderNotifyReq = BestConverter.orderCreateConvertDo(orderCreateDTO);
        log.info("百世快递创建订单请求参数：{}", JSONObject.toJSONString(kyCreateOrderNotifyReq));
        KyCreateOrderNotifyRsp rsp = client.executed(kyCreateOrderNotifyReq);
        log.info("百世快递创建订单返回参数：{}", JSONObject.toJSONString(rsp));
        return BestRspConverter.orderCreateConvertRsp(rsp);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        KyCancelOrderNotifyReq kyCancelOrderNotifyReq = BestConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        log.info("百世快递取消订单请求参数：{}", JSONObject.toJSONString(kyCancelOrderNotifyReq));
        KyCancelOrderNotifyRsp rsp = client.executed(kyCancelOrderNotifyReq);
        log.info("百世快递取消订单返回参数：{}", JSONObject.toJSONString(rsp));
        return BestRspConverter.orderCancelConvertRsp(rsp);
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        KyTraceQueryReq kyTraceQueryReq = BestConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        log.info("百世快递查询订单轨迹请求参数：{}", JSONObject.toJSONString(kyTraceQueryReq));
        KyTraceQueryRsp rsp = client.executed(kyTraceQueryReq);
        log.info("百世快递查询订单轨迹返回参数：{}", JSONObject.toJSONString(rsp));
        return BestRspConverter.orderTrackConvertRsp(rsp);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        KyPriceAgingSearchReq req = BestConverter.orderChargeConvertDo(orderChargeDTO);
        log.info("百世快递查询订单运价请求参数：{}", JSONObject.toJSONString(req));
        KyPriceAgingSearchRsp rsp = client.executed(req);
        log.info("百世快递查询订单运价返回参数：{}", JSONObject.toJSONString(rsp));
        return BestRspConverter.orderChargeConvertRsp(rsp);
    }

}
