package com.holderzone.member.mall.provider.logistics.yunda.domain;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class YundaOrderCreate {


    /**
     * 必填
     * 订单唯一序列号 由字母、数字、下划线组成，必须保证唯一，请对特殊符号进行过滤
     */
    private String order_serial_no;

    /**
     * 必填
     * 大客户系统订单的订单号可与订单唯一序列号相同
     */
    private String khddh;

    /**
     * 必填
     * 寄件人信息
     */
    private PersonDto sender;

    /**
     * 必填
     * 收件人信息
     */
    private PersonDto receiver;

    /**
     * 物品重量
     */
    private double weight;

    /**
     * 货物大小（米），用半角的逗号来分隔长宽高
     */
    private String size;

    /**
     * 货物金额
     */
    private double value;

    /**
     * 揽件网点编码
     */
    private String collect_branch;

    /**
     * 代收货款金额 目前仅用于cod订单，cod订单必填
     */
    private double collection_value;

    /**
     * 其他费用
     */
    private double other_charges;

    /**
     *商品信息集合
     */
    private List<ItemDto> items;

    /**
     * 商品类型(见商品类型字段)SpecialEnum
     */
    private String special;

    /**
     *备注
     */
    private String remark;

    /**
     * 必填
     *运单类型参数值对照order_type字典表可固定为common
     */
    private String order_type;

    /**
     * 必填
     *	350（默认）
     */
    private String node_id;
}
