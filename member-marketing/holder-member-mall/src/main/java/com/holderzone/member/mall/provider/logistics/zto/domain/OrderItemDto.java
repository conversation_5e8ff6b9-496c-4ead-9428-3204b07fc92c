package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderItemDto {

    /**
     * 货品名称
     */
    private String name;

    /**
     * 商品分类
     */
    private String category;

    /**
     * 商品材质
     */
    private String material;

    /**
     * 大小（长,宽,高）(单位：厘米), 用半角的逗号来分隔长宽高
     */
    private String size;

    /**
     * 重量（单位：克)
     */
    private long weight;

    /**
     * 单价(单位:元)
     */
    private Integer unitprice;

    /**
     * 货品数量
     */
    private Integer quantity;

    /**
     * 货品备注
     */
    private String remark;
}
