package com.holderzone.member.mall.provider.logistics.yunda;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.converter.YundaConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.logistics.yunda.client.YundaClient;
import com.holderzone.member.mall.provider.logistics.yunda.config.YundaRequestConfig;
import com.holderzone.member.mall.provider.logistics.yunda.domain.*;
import com.holderzone.member.mall.provider.logistics.yunda.domain.response.OrderChargeResponse;
import com.holderzone.member.mall.provider.logistics.yunda.domain.response.OrderResponse;
import com.holderzone.member.mall.provider.logistics.yunda.domain.response.OrderTrackResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 韵达快递服务(<a href="http://open.yundaex.com/api/apiDoc"/>)
 * TODO 注意：申请使用物流轨迹查询接口，网点客户请联系****************齐明/**********@qq.com丁兰兰（90193096），发送邮件申请接口权限，或请合作网点协助通过钉钉联系大客户营销公司齐明/丁兰兰，申请接口权限；
 * 第三方软件/电商平台 请联系韵达产品中心（联系方式：<EMAIL> 或 <EMAIL>）建立合作，完成合作确认后获取接口使用权限（邮件需说明合作内容与公司的说明）；
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class YundaService extends AbstractLogisticsService {

    private final YundaRequestConfig yundaRequestConfig;

    public ExpressTypeEnum type() {
        return ExpressTypeEnum.YUNDA;
    }

    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        OrderBaseRequest<YundaOrderCreate> yundaOrderCreate = YundaConverter.orderCreateConvertDo(orderCreateDTO, yundaRequestConfig);
        String requestData = JSONObject.toJSONString(yundaOrderCreate);
        log.info("韵达创建订单请求参数：{}",requestData);
        String rsp = remoteRequest(yundaRequestConfig.getCreateUrl(), requestData);
        log.info("韵达创建订单返回参数：{}",rsp);
        //构建返回数据
        return OrderResponse.buildCreateResponse(rsp);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        OrderBaseRequest<YundaOrderCancel> yundaOrderCancel = YundaConverter.orderCancelConvertDo(logisticsOrderCancelDTO, yundaRequestConfig);
        String requestData = JSONObject.toJSONString(yundaOrderCancel);
        log.info("韵达取消订单请求参数：{}",requestData);
        String rsp = remoteRequest(yundaRequestConfig.getCancelUrl(), requestData);
        log.info("韵达取消订单返回参数：{}",rsp);
        //构建返回数据
        return OrderResponse.buildCancelResponse(rsp);
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        YundaOrderTrack yundaOrderTrack = YundaConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        String requestData = JSONObject.toJSONString(yundaOrderTrack);
        log.info("韵达查询订单轨迹请求参数：{}",requestData);
        String rsp = remoteRequest(yundaRequestConfig.getTrackUrl(), requestData);
        log.info("韵达查询订单轨迹返回参数：{}",rsp);
        //构建返回数据
        return OrderTrackResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        YundaOrderCharge yundaOrderCharge = YundaConverter.orderChargeConvertDo(orderChargeDTO);
        String requestData = JSONObject.toJSONString(yundaOrderCharge);
        log.info("韵达查询订单运价请求参数：{}",requestData);
        String rsp = remoteRequest(yundaRequestConfig.getChargeUrl(), requestData);
        log.info("韵达查询订单运价返回参数：{}",rsp);
        //构建返回数据
        return OrderChargeResponse.buildCommonResponse(rsp);
    }

    private String remoteRequest(String url,String requestData){
        return YundaClient.doPostJson(url,requestData,yundaRequestConfig.getAppKey(),yundaRequestConfig.getAppSecret());
    }
}
