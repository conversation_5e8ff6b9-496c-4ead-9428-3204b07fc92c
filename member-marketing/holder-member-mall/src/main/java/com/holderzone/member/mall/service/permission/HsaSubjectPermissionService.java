package com.holderzone.member.mall.service.permission;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.entity.permission.HsaSubjectPermission;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface HsaSubjectPermissionService extends IHolderBaseService<HsaSubjectPermission> {

    /**
     * 获取当前企业下岗位的运营主体权限信息
     * @param request 请求参数
     * @return 运营主体权限信息
     */
    List<HsaOperSubjectPermissionVO> getSubjectPermissionList(OperSubjectPermissionQO request);

    /**
     * 修改运营主体权限信息
     * @param request 请求参数
     * @return 是否修改成功
     */
    boolean updateSubjectPermission(HsaOperSubjectPermissionQO request);

    /**
     * 获取当前用户操作权限信息
     * @param identification 系统类别
     * @see com.holderzone.member.common.enums.SystemPermissionEnum
     * @return 操作权限信息
     */
    MemberSystemPermissionVO getAccountPermission(String identification);

    /**
     * 获取运营主体
     * @return 运营主体信息
     */
    OperSubjectInfoVO getOperatingSubject();

}
