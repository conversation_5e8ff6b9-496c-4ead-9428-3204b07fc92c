package com.holderzone.member.mall.service.shoppingcart;

import com.baomidou.mybatisplus.extension.service.IService;
import com.holderzone.member.common.dto.mall.shoppingcart.CartAddNumDTO;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;

import java.util.List;

/**
 * <p>
 * 购物车商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
public interface IHsaShoppingCartCommodityService extends IService<HsaShoppingCartCommodity> {

    /**
     * 改变购物车商品数量
     *
     * @param cartAddNumDTO 改变数量实体
     */
    void changeNum(CartAddNumDTO cartAddNumDTO);

    /**
     * 删除主体下所有商品
     *
     * @param operSubjectGuid 运营主体
     */
    void removeAll(String operSubjectGuid);

    List<HsaShoppingCartCommodity> listShoppingCartCommodity(String memberInfoGuid, String operSubjectGuid);

    List<HsaShoppingCartCommodity> listShoppingCartCommodity(List<String> shoppingCartGuidList);

    List<Integer> listStoreId(String memberInfoGuid, String operSubjectGuid);

    Integer queryShoppingCartCommodityNum(String memberInfoGuid, String operSubjectGuid);

    void removeByShoppingCartGuidList(List<String> shoppingCartGuidList);

    HsaShoppingCartCommodity queryRepeatCommodity(String operSubjectGuid, String skuJson, String attrJson,
                                                  String memberInfoGuid, Long commodityId);

    HsaShoppingCartCommodity queryByGuid(String shoppingCartGuid);

    List<HsaShoppingCartCommodity> listByGuid(List<String> shoppingCartGuidList);

    Integer countLimit(String memberInfoGuid);
}
