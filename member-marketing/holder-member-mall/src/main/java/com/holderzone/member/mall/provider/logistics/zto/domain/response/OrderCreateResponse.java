package com.holderzone.member.mall.provider.logistics.zto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderCreateResponse {

    /**
     * 大头笔信息
     */
    private BigMarkInfoOutput bigMarkInfo;

    /**
     * 2小时取件码（当vasType增值信息中有twoHour的时候返回）
     */
    private String verifyCode;

    /**
     * 单号所属网点code（预约件）
     */
    private String siteCode;

    /**
     * 单号所属网点名称（预约件）
     */
    private String siteName;
    /**
     * 签单返回信息
     */
    private SignBillInfo signBillInfo;
    /**
     * 订单号
     */
    private String orderCode;
    /**
     * 运单号
     */
    private String billCode;

    /**
     * 合作商订单号
     */
    private String partnerOrderCode;

    public static LogisticsOrderCreateResponse buildCommonResponse(String json){
        ZtoBaseRsp<OrderCreateResponse> rsp = JSONObject.parseObject(json, new TypeReference<ZtoBaseRsp<OrderCreateResponse>>() {
        }.getType());

        if(rsp == null || rsp.getResult() == null) {
            return null;
        }
        //TODO
        return LogisticsOrderCreateResponse.builder().courierNumber(rsp.getResult().getBillCode()).build();

    }
}
