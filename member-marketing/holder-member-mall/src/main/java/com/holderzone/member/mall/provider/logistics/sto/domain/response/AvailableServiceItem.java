package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AvailableServiceItem {

    /**
     * 服务可选时间
     */
    private TdTime tdTimeSelect;

    /**
     * 寄件服务类型code
     */
    private String code;

    /**
     * 价格模板实体
     */
    private FeeModel feeModel;

    /**
     * 寄件服务类型名称
     */
    private String title;

    /**
     * 寄件服务类型版本号
     */
    private String version;
}
