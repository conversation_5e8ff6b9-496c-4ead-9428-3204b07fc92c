package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RlsDetail {

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 原寄地中转场
     */
    private String sourceTransferCode;

    /**
     * 原寄地城市代码
     */
    private String sourceCityCode;

    /**
     * 原寄地网点代码
     */
    private String sourceDeptCode;

    /**
     * 原寄地单元区域
     */
    private String sourceTeamCode;

    /**
     * 目的地城市代码,eg:755
     */
    private String destCityCode;

    /**
     * 目的地网点代码,eg:755AQ
     */
    private String destDeptCode;

    /**
     * 目的地网点代码映射码
     */
    private String destDeptCodeMapping;

    /**
     * 目的地单元区域,eg:001
     */
    private String destTeamCode;

    /**
     * 目的地单元区域映射码
     */
    private String destTeamCodeMapping;

    /**
     * 目的地中转场
     */
    private String destTransferCode;

    /**
     * 打单时的路由标签信息
     */
    private String destRouteLabel;

    /**
     * 产品名称 对应RLS:pro_name
     */
    private String proName;

    /**
     * 快件内容： 如：C816、SP601
     */
    private String cargoTypeCode;

    /**
     * 时效代码, 如：T4
     */
    private String limitTypeCode;

    /**
     * 产品类型,如：B1
     */
    private String expressTypeCode;

    /**
     * 入港映射码 eg:S10
     */
    private String codingMapping;

    /**
     * 出港映射码
     */
    private String codingMappingOut;

    /**
     * 外发标签
     */
    private String forwardFlag;

    /**
     * 打印标志 返回值总共有 9 位，每一位只有 0和 1 两种，
     * 0 表示按顺丰运单默认的规则，1 表示显示
     */
    private String printFlag;

    /**
     * 二维码
     */
    private String twoDimensionCode;

    /**
     * 打印图标 根据托寄物判断需要打印的图标 (重货,蟹类,生鲜,易碎，Z 标)返回值有 8 位，每一位只有 0 和 1两种，0 表示按运单默认的规则，1 表示显示。后面两位默认 0 备用。
     * 顺序如下：重货,蟹类,生鲜,易碎, 医药类,Z 标,0,0
     * 如：00000000 表示不需要打印重货，蟹类，生鲜，易碎,医药,Z 标,备用,备用
     */
    private String printIcon;

    /**
     * 快运标签：目前有两位，第一位备用。第二位：送装标识（0:不打印;1:送装分离icon;2:送装一体icon）
     */
    private String fopIcon;

    /**
     * AB标
     */
    private String abFlag;

    /**
     * 查询出现异常时返回信息。 返回代码： 0 系统异常 1 未找到运单
     */
    private String errMsg;

    /**
     * 件数
     */
    private String goodsNumber;

    /**
     * 提/送货方式:
     * 自提
     * 送货上门（无电梯）
     * 送货上门（有电梯）
     * 送货（不含上楼）
     */
    private String pickUpWay;

    /**
     * 路由数组,示例:”755G:D3,769BG:A2,020BF:C2,021BG:AF,010FG:D2”
     */
    private String routeArray;

    /**
     * 是否打印logo
     * 1：打印
     * 其他：不打印
     */
    private int printLogo;

}
