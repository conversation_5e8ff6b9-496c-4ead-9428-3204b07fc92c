package com.holderzone.member.mall.support;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.HolderFeign;
import com.holderzone.member.common.util.number.RandomNumUtil;
import com.holderzone.member.common.vo.feign.FeignModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class UserValidateSupport {

    // TODO holder改造(已改)
    private final ExternalSupport externalSupport;

    private final StringRedisTemplate redisTemplate;

    private final RedissonClient redissonClient;

    private final static String PASSWORD_VALIDATE_CODE = "PASSWORD_VALIDATE_CODE:";

    private final static String PASSWORD_VALIDATE = "PASSWORD_VALIDATE:";

    public String validateUserPassword(String tel,String password){
        Boolean data = externalSupport.baseServer(ThreadLocalCache.getSystem()).validateUser(tel, password);
        if (!data) {
            throw new MallBaseException("请输入正确的密码");
        }
        String successCode = RandomNumUtil.getCode(8);
        redisTemplate.opsForValue().set(PASSWORD_VALIDATE_CODE + successCode,tel,1, TimeUnit.HOURS);
        return successCode;
    }

    public void validatePasswordCode(String code){
        RLock lock = redissonClient.getLock(PASSWORD_VALIDATE + code);
        try {
            if(lock.isLocked()){
                throw new MallBaseException("系统繁忙稍后再试");
            }
            lock.lock();
            String value = redisTemplate.opsForValue().get(PASSWORD_VALIDATE_CODE + code);
            if(ObjectUtil.isNull(value)){
                throw new MallBaseException("请输入正确的密码");
            }
            redisTemplate.delete(PASSWORD_VALIDATE_CODE + code);
        }finally {
            if(lock.isLocked()){
                lock.unlock();
            }
        }
    }
}
