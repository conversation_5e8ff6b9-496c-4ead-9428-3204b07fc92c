package com.holderzone.member.mall.provider.logistics.sf.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ThirdSignBackInfo {

    /**
     * 必填
     * 第三方签回单收件省
     */
    private String signBackProvince;

    /**
     * 必填
     * 第三方签回单收件市
     */
    private String signBackCity;
    /**
     * 必填
     * 第三方签回单收件区
     */
    private String signBackCounty;
    /**
     * 必填
     * 第三方签回单详细地址
     */
    private String signBackAddress;
    /**
     * 必填
     * 第三方签回单联系人名
     */
    private String signBackContact;
    /**
     * 必填
     * 第三方签回单手机号
     */
    private String signBackMobile;
    /**
     * 第三方签回单联系电话
     */
    private String signBackTel;
    /**
     * 第三方签回单公司名
     */
    private String signBackCompany;

}
