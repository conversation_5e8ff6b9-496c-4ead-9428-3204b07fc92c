package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderCreateResponse implements Serializable {

    /**
     * 客户订单号
     */
    private String orderId;

    /**
     * 顺丰运单号(母单号)。一个订单只能生成一个母单号。
     */
    private String waybillNo;

    /**
     * 子单号，如果有多个子单号会以半角逗号分隔。如“755123456789,001123456789”
     */
    private String subWaybillNos;

    /**
     * 目的地区域代码
     */
    private String destCode;

    /**
     * 筛单结果：
     * 1：人工确认,客服会在1小时内给下单手机致电反馈是否可达；
     * 2：可收派
     * 3：不可以收派
     */
    private int filterResult;

    /**
     * 不可以收派的原因代码:
     * 1：收方超范围
     * 2：派方超范围
     * 3：其它原因
     */
    private String filterRemark;

    /**
     * 地址映射码
     */
    private String mappingMark;

    /**
     * 第三方支付运费的URL
     */
    private String paymentLink;
    /**
     * 路由信息
     */
    private RlsInfo rlsInfo;




    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCreateResponse buildCommonResponse(String rsp){
        SfBaseResponse sfBaseResponse = JSONObject.parseObject(rsp, SfBaseResponse.class);
        if(sfBaseResponse == null || sfBaseResponse.getApiResultData() == null){
            return null;
        }
        OrderCreateResponse orderCreateResponse = JSONObject.parseObject(sfBaseResponse.getApiResultData(), OrderCreateResponse.class);
        if(orderCreateResponse == null){
            return null;
        }
        return LogisticsOrderCreateResponse.builder().build();
    }

}
