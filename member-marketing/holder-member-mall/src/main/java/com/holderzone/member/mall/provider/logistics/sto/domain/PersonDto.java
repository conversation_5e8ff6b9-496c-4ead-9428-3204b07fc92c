package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PersonDto {

    /**
     * 必填
     * 姓名
     */
    private String name;

    /**
     * 固定电话
     */
    private String tel;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 必填
     * 省
     */
    private String province;

    /**
     * 必填
     * 市
     */
    private String city;

    /**
     * 必填
     * 区
     */
    private String area;

    /**
     * 必填
     * 镇
     */
    private String town;

    /**
     * 必填
     * 详细地址
     */
    private String address;

    /**
     * 安全号码（收件人）
     */
    private String safeNo;

}
