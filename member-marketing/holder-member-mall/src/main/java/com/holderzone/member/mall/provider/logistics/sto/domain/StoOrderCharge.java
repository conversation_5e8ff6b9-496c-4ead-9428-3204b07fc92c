package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class StoOrderCharge {

    /**
     * 必填
     * 发件人姓名
     */
    private String sendName;

    /**
     * 必填
     * 发件人手机号
     */
    private String sendMobile;

    /**
     * 必填
     * 发件人省份
     */
    private String sendProv;

    /**
     * 必填
     * 发件人城市
     */
    private String sendCity;

    /**
     * 必填
     * 发件人区县
     */
    private String sendArea;

    /**
     * 必填
     * 发件人详细地址
     */
    private String sendAddress;

    /**
     * 必填
     *收件人姓名
     */
    private String recName;

    /**
     * 必填
     * 收件人手机号
     */
    private String recMobile;

    /**
     * 必填
     * 收件人省份
     */
    private String recProv;

    /**
     * 必填
     * 收件人城市
     */
    private String recCity;

    /**
     * 必填
     * 收件人区县
     */
    private String recArea;

    /**
     * 必填
     * 收件人详细地址
     */
    private String recAddress;

    /**
     * 必填
     * 下单用户唯一标识
     */
    private String openId;

    /**
     *物品重量（kg）
     */
    private String weight;

    /**
     * 接单业务员编号
     */
    private String takeUserCode;

    /**
     * 接单网点编号
     */
    private String takeCompanyCode;

    /**
     *包裹长度（单位厘米）
     */
    private String length;

    /**
     *包裹宽度（单位厘米）
     */
    private String width;

    /**
     *包裹宽度（单位厘米）
     */
    private String height;

    /**
     *包裹高度（单位厘米
     */
    private String addOrderPageUrl;

    /**
     *调用方系统编号
     */
    private String systemCode;
}
