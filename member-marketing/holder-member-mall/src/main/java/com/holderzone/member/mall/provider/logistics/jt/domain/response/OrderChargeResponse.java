package com.holderzone.member.mall.provider.logistics.jt.domain.response;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderChargeResponse implements Serializable {

    /**
     * 运费总价（单位：元），保留两位小数
     */
    private String totalPrice;

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderChargeResponse buildCommonResponse(String rsp){
        try {
            JtBaseResponse<OrderChargeResponse> jtBaseResponse = JSONObject.parseObject(rsp, new TypeReference<JtBaseResponse<OrderChargeResponse>>() {
            }.getType());
            if(jtBaseResponse == null || jtBaseResponse.getData() == null){
                return null;
            }
            return LogisticsOrderChargeResponse.builder().build();
        }catch (Exception e){
           log.error("解析极兔创建订单返回参数异常",e);
           return null;
        }

    }

}
