package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse implements Serializable {

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 路由信息Route的集合
     */
    private List<Route> routeList;




    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderTrackResponse buildCommonResponse(String rsp){
        SfBaseResponse sfBaseResponse = JSONObject.parseObject(rsp, SfBaseResponse.class);
        if(sfBaseResponse == null || sfBaseResponse.getApiResultData() == null){
            return null;
        }
        OrderTrackResponse orderTrackResponse = JSONObject.parseObject(sfBaseResponse.getApiResultData(), OrderTrackResponse.class);
        if(orderTrackResponse == null){
            return null;
        }
        return LogisticsOrderTrackResponse.builder().build();
    }

}
