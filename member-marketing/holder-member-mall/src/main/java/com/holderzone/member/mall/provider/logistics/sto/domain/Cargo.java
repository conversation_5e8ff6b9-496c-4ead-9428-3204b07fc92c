package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Cargo {

    /**
     * 必填
     * 带电标识 （10/未知 20/带电 30/不带电）
     */
    private String battery;

    /**
     * 必填
     * 物品类型（大件、小件、扁平件\文件）
     */
    private String goodsType;

    /**
     * 必填
     * 物品名称
     */
    private String goodsName;

    /**
     * 物品数量
     */
    private Integer goodsCount;

    /**
     * 长（cm）
     */
    private Double spaceX;

    /**
     * 宽(cm)
     */
    private Double spaceY;

    /**
     * 高(cm)
     */
    private Double spaceZ;
    /**
     * 重量(kg)
     */
    private Double weight;
    /**
     * 商品金额
     */
    private String goodsAmount;
    /**
     * 小包信息（国际业务专用，其他业务不需要填写）
     */
    private List<CargoItem> cargoItemList;

}
