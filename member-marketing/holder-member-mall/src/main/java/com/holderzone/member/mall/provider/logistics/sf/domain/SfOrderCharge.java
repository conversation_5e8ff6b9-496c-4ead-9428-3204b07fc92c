package com.holderzone.member.mall.provider.logistics.sf.domain;

import com.holderzone.member.mall.provider.logistics.yunda.domain.ItemDto;
import com.holderzone.member.mall.provider.logistics.yunda.domain.PersonDto;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class SfOrderCharge {

    /**
     * 必填
     * 合作商app-key
     */
    private String appid;

    /**
     * 必填
     * 合作商订单号
     */
    private String orderid;

    /**
     * 运单回传地址url
     */
    private String backurl;

    /**
     * 必填
     * 寄件人信息
     */
    private PersonDto sender;

    /**
     * 必填
     * 收件人信息
     */
    private PersonDto receiver;

    /**
     * 必填
     * 取件开始时间(yyyy-MM-dd HH:mm:ss )
     */
    private String sendstarttime;

    /**
     *取件结束时间
     */
    private String sendendtime;

    /**
     * 物品重量
     */
    private double weight;

    /**
     * 货物大小（米），用半角的逗号来分隔长宽高
     */
    private String size;

    /**
     * 货物金额
     */
    private double value;

    /**
     * 运费
     */
    private double freight;

    /**
     * 必填
     * 合作商app-key
     */
    private double premium;

    /**
     * 其他费用
     */
    private double other_charges;

    /**
     *商品信息集合
     */
    private List<ItemDto> items;

    /**
     * 原样返回字段
     */
    private String backparam;

    /**
     * 商品类型(见商品类型字段)SpecialEnum
     */
    private String special;

    /**
     *备注
     */
    private String remark;
}
