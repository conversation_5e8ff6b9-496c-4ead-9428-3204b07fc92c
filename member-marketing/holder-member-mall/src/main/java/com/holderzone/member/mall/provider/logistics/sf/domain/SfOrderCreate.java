package com.holderzone.member.mall.provider.logistics.sf.domain;

import com.holderzone.member.mall.provider.logistics.yunda.domain.PersonDto;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class SfOrderCreate {

    /**
     * 必填
     * 客户订单号。CP须保证订单号唯一。
     */
    private String orderId;

    /**
     * 使用预留的运单号时传入。只接受传入母单号。
     */
    private String waybillNo;

    /**
     * 必填
     * 是否生成运单号， 0为不生成，使用传入的运单号。默认为1
     */
    private Integer isGenBillNo;

    /**
     * 使用预留的运单号时，此字段传入子单号。
     */
    private String subWaybills;

    /**
     * 寄件方公司名称
     */
    private PersonDto sendCompany;

    /**
     * 必填
     * 寄件方联系人
     */
    private String sendContact;

    /**
     *寄件方联系手机
     */
    private String sendMobile;

    /**
     *寄件方联系电话（和上面的节点二选一）
     */
    private String sendTel;

    /**
     * 必填
     * 寄件方所在省级行政区名称，必须是标准的省级行政区名称
     * 如：北京、广东省、广西壮族自治区等；
     */
    private String sendProvince;

    /**
     * 必填
     * 寄件方所在地级行政区名称，必须是标准的城市称谓，
     * 如：北京市、深圳市、大理白族自治州等；
     */
    private String sendCity;

    /**
     * 必填
     * 寄件人所在县/区级行政区名称必须是标准的县/区称谓，
     * 如：福田区，南涧彝族自治县、准格尔旗等。
     */
    private String sendCounty;

    /**
     * 必填
     * 寄件方详细地址（请勿包含省市区），
     * 如：新洲十一街万基商务大厦10楼。
     */
    private String sendAddress;

    /**
     * 顺丰月结卡号, 月结卡号必须符合使用规范，否则可能会导致订单揽收不了
     */
    private String customId;

    /**
     * 取件方式 1. 客户自送 2 上门接货。默认为2上门接货。
     */
    private Integer pickUpMode;

    /**
     *是否下call，0-不下call，不会通知小哥上门，适用于大客户自行打印面单，小哥固定时间揽收的场景；
     * 1-下call，通知小哥，默认2小时内上门，适用于散件，单量较小，需小哥上门打单的场景。
     */
    private String isDoCall;

    /**
     * 希望上门取件时间。格式：yyyy-MM-dd HH:mm:ss。
     * isDocall=1时有效，其中，预约时间超过晚8点，会自动约成第二天
     */
    private String expectedPickUpTime;

    /**
     * 到件方公司名称
     */
    private String deliveryCompany;

    /**
     * 必填
     *到件方联系人
     */
    private String deliveryContact;

    /**
     * 必填
     *到件方所在省级行政区名称，必须是标准的省级行政区名称
     * 如：北京、广东省、广西壮族自治区等；
     */
    private String deliveryProvince;

    /**
     * 必填
     *到件方所在地级行政区名称，必须是标准的城市称谓
     */
    private String deliveryCity;

    /**
     * 必填
     *到件方所在县/区级行政区名称，必须是标准的县/区称谓，
     * 如：福田区，南涧彝族自治县、准格尔旗等
     */
    private String deliveryCounty;

    /**
     * 必填
     *	到件方详细地址（请勿包含省市区），
     * 如:新洲十一街万基商务大厦10楼。
     */
    private String deliveryAddress;

    /**
     * 到件方联系手机
     */
    private String deliveryMobile;

    /**
     * 到件方联系电话（和上面的节点二选一）
     */
    private String deliveryTel;

    /**
     * 到件方邮箱地址
     */
    private String deliveryEmail;

    /**
     * 必填
     *付款方式(邮费)：
     * 1.寄方付
     * 2.收方付
     * 3.第三方付
     */
    private Integer payMethod;

    /**
     * 包裹数，一个包裹对应一个运单号；
     * 若包裹数大于1，则返回1个母运单号和N-1个子运单号。
     */
    private Integer parcelQuantity;

    /**
     * 客户订单货物总长，单位厘米，精确到小数点后3位，包含子母件。
     */
    private BigDecimal cargoLength;

    /**
     * 客户订单货物总宽，单位厘米，精确到小数点后3位，包含子母件。
     */
    private BigDecimal cargoWidth;

    /**
     * 客户订单货物总高，单位厘米，精确到小数点后3位，包含子母件。
     */
    private BigDecimal cargoHeight;

    /**
     * 订单货物总体积，单位立方厘米，精确到小数点后3位，会用于计抛（是否计抛具体商务沟通中双方约定）。
     */
    private BigDecimal volume;

    /**
     * 订单货物总重量，包含子母件，单位千克，精确到小数点后3位。使用送装增值服务时必填。
     */
    private BigDecimal cargoTotalWeight;

    /**
     * 是否返回签回单的运单号（只有当需要纸质签回单时才需要传递）。支持以下值：
     * 1:返回
     * 其他：不要求
     */
    private Integer needReturnTrackingNo;

    /**
     * 特殊派送类型代码 1:身份验证（要求验证身份证）
     */
    private String specialDeliveryTypeCode;

    /**
     * 特殊派件具体表述 证件类型:证件后8位 如：1:09296231（1表示身份证，暂不支持其他证件）
     */
    private String specialDeliveryValue;

    /**
     * 派送方式： 1. 派送 2.自提 （不传默认为1）
     */
    private Integer deliveryMode;

    /**
     * 是否有电梯（派送端) 1：有 其他：未知
     */
    private Integer hasElevator;

    /**
     * 派送预约上门时间类型：1.工作日、双休均可送货
     * 2.仅工作日送货3.仅休息日送货 4.仅周六周日收货 5.仅周一至周六收货
     * 6.仅周一至周五收货
     */
    private Integer deliveryResType;

    /**
     * 货物分类， 如家电、家俱等。
     */
    private String cargoType;

    /**
     * 货物名称 如小天鹅洗衣机
     */
    private String cargoName;

    /**
     * 声明价值的货币类型，默认CNY,
     */
    private String currencyCode;
    /**
     * SF快运产品类型，参见 快运产品列表。
     */
    private String productCode;
    /**
     * 电商原始订单号
     */
    private String originalNumber;

    /**
     * 客户订单来源（taobao, tmall,jd,pdd等）
     */
    private String orderSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 必填
     * 货物明细
     */
    private List<Cargo> cargoList;

    /**
     * 包裹信息（指定包裹件数、重量、体积等，大件产品建议填写）
     */
    private List<Package> packageList;

    /**
     * 增值服务
     */
    private List<AdditionService> additionServices;

    /**
     * 扩展字段，key和value接口
     */
    private List<Map<String,String>> extraMap;

    /**
     * 第三方签回单信息，签回单转寄到第三方地址（即非寄件地址）情况填写
     */
    private ThirdSignBackInfo thirdSignBackInfo;
}
