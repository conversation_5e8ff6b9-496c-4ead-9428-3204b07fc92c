package com.holderzone.member.mall.controller.store;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.mall.support.StoreSupport;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/store")
@Slf4j
public class StoreController {

    @Resource
    private StoreSupport storeSupport;

    @ApiOperation("根据门店id查询门店信息")
    @GetMapping("/get/{storeGuid}")
    public Result<StoreDataInfoVO> saveOrUpdate(@PathVariable String storeGuid) {
        return Result.success(storeSupport.getStoreById(storeGuid));
    }
}
