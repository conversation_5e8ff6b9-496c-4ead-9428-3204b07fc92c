package com.holderzone.member.mall.service.logistics.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.qo.logistics.LogisticsTemplateQO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import com.holderzone.member.mall.mapper.logistics.HsaLogisticsTemplateMapper;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class HsaLogisticsTemplateServiceImpl extends HolderBaseServiceImpl<HsaLogisticsTemplateMapper, HsaLogisticsTemplate> implements HsaLogisticsTemplateService {

    @Override
    public List<LogisticsTemplateVO> list(LogisticsTemplateQO query) {
        return baseMapper.list(query);
    }

    @Override
    public List<LogisticsTemplateVO> guidList(LogisticsTemplateQO query) {
        return baseMapper.guidList(query);
    }

    @Override
    public String queryDefaultTemplateGuid(String operSubjectGuid) {
        return baseMapper.queryDefaultTemplateGuid(operSubjectGuid);
    }

    @Override
    public void clearDefaultFlag() {
        UpdateWrapper<HsaLogisticsTemplate> uw = new UpdateWrapper<>();
        uw.lambda().eq(HsaLogisticsTemplate::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid());
        update(new HsaLogisticsTemplate().setDefaultFlag(false), uw);
    }
}
