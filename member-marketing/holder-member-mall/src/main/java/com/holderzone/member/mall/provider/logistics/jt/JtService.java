package com.holderzone.member.mall.provider.logistics.jt;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.converter.JtConverter;
import com.holderzone.member.mall.provider.logistics.jt.client.JtClient;
import com.holderzone.member.mall.provider.logistics.jt.config.JtRequestConfig;
import com.holderzone.member.mall.provider.logistics.jt.domain.JtOrderCancel;
import com.holderzone.member.mall.provider.logistics.jt.domain.JtOrderCharge;
import com.holderzone.member.mall.provider.logistics.jt.domain.JtOrderCreate;
import com.holderzone.member.mall.provider.logistics.jt.domain.JtOrderTrack;
import com.holderzone.member.mall.provider.logistics.jt.domain.response.OrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.jt.domain.response.OrderChargeResponse;
import com.holderzone.member.mall.provider.logistics.jt.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.jt.domain.response.OrderTrackResponse;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 极兔快递服务(<a href="https://open.jtexpress.com.cn/#/apiDoc/index"/>)
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class JtService extends AbstractLogisticsService {

    private final JtClient jtClient;

    private final JtRequestConfig jtRequestConfig;

    @Override
    public ExpressTypeEnum type() {
        return ExpressTypeEnum.JT;
    }

    @Override
    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        JtOrderCreate jtOrderCreate = JtConverter.orderCreateConvertDo(orderCreateDTO);
        String requestData = JSONObject.toJSONString(jtOrderCreate);
        log.info("极兔创建订单请求参数：{}",requestData);
        String rsp = jtClient.executor(requestData, jtRequestConfig.getCreateUrl());
        log.info("极兔创建订单返回参数：{}",rsp);
        //构建返回参数
        return OrderCreateResponse.buildCommonResponse(rsp);
    }

    @Override
    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        JtOrderCancel jtOrderCancel = JtConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        String requestData = JSONObject.toJSONString(jtOrderCancel);
        log.info("极兔取消订单请求参数：{}",requestData);
        String rsp = jtClient.executor(requestData, jtRequestConfig.getCancelUrl());
        log.info("极兔取消订单返回参数：{}",rsp);
        //构建返回参数
        return OrderCancelResponse.buildCommonResponse(rsp);
    }

    @Override
    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        JtOrderTrack jtOrderTrack = JtConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        String requestData = JSONObject.toJSONString(jtOrderTrack);
        log.info("极兔查询订单轨迹请求参数：{}",requestData);
        String rsp = jtClient.executor(requestData, jtRequestConfig.getTrackUrl());
        log.info("极兔查询订单轨迹返回参数：{}",rsp);
        //构建返回参数
        return OrderTrackResponse.buildCommonResponse(rsp);
    }

    @Override
    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        JtOrderCharge jtOrderCharge = JtConverter.orderChargeConvertDo(orderChargeDTO);
        String requestData = JSONObject.toJSONString(jtOrderCharge);
        log.info("极兔查询订单运费请求参数：{}",requestData);
        String rsp = jtClient.executor(requestData, jtRequestConfig.getChargeUrl());
        log.info("极兔查询订单运费返回参数：{}",rsp);
        //构建返回参数
        return OrderChargeResponse.buildCommonResponse(rsp);
    }
}
