package com.holderzone.member.mall.provider.logistics.yto;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.client.LogisticsRestClient;
import com.holderzone.member.mall.provider.logistics.converter.YtoConverter;
import com.holderzone.member.mall.provider.logistics.yto.domain.*;
import com.holderzone.member.mall.provider.logistics.yto.domain.response.OrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.yto.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.yto.domain.response.OrderTrackResponse;
import com.holderzone.member.mall.provider.logistics.yto.config.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 圆通快递服务(<a href="https://open.yto.net.cn/home"/>)
 * TODO 注意：创建订单接口和取消订单接口需要联系当地网点申请申请生产环境
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@Service
public class YtoService extends AbstractLogisticsService {

    private final YtoOrderCreateConfig ytoOrderCreateConfig;

    private final YtoOrderCancelConfig ytoOrderCancelConfig;

    private final YtoOrderTrackConfig ytoOrderTrackConfig;

    private final YtoOrderChargeConfig ytoOrderChargeConfig;

    private final LogisticsRestClient restClient;

    public ExpressTypeEnum type() {
        return ExpressTypeEnum.YTO;
    }


    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        //构建圆通请求参数 TODO
        YtoOrderCreate ytoOrderCreate = YtoConverter.orderCreateConvertDo(orderCreateDTO);
        //参数校验
        ytoOrderCreate.checkParams();
        String createString = JSONObject.toJSONString(ytoOrderCreate);
        //请求远程接口
        String ytoRequest = YtoRequest.buildRequestData(createSign(createString, ytoOrderCreateConfig), createString);
        log.info("请求圆通订单创建接口请求数据：{}",ytoRequest);
        String response = restClient.post(ytoRequest, ytoOrderCreateConfig.getUrl());
        log.info("请求圆通订单创建接口返回数据：{}",response);
        //构建返回参数 TODO
        return OrderCreateResponse.buildCommonResponse(response);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        YtoOrderCancel ytoOrderCancel = YtoConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        ytoOrderCancel.checkParams();
        String createString = JSONObject.toJSONString(ytoOrderCancel);
        //请求远程接口
        String ytoRequest = YtoRequest.buildRequestData(createSign(createString, ytoOrderCancelConfig), createString);
        log.info("请求圆通订单取消接口请求数据：{}",ytoRequest);
        String response = restClient.post(ytoRequest, ytoOrderCancelConfig.getUrl());
        log.info("请求圆通订单取消接口返回数据：{}",response);
        //构建返回参数 TODO
        return OrderCancelResponse.buildCommonResponse(response);
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        YtoOrderTrack ytoOrderTrack = YtoConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        ytoOrderTrack.checkParams();
        String createString = JSONObject.toJSONString(ytoOrderTrack);
        //请求远程接口
        String ytoRequest = YtoRequest.buildRequestData(createSign(createString, ytoOrderTrackConfig), createString);
        log.info("请求圆通查询订单轨迹接口请求数据：{}",ytoRequest);
        String response = restClient.post(ytoRequest, ytoOrderTrackConfig.getUrl());
        log.info("请求圆通查询订单轨迹接口返回数据：{}",response);
        //构建返回参数 TODO
        return OrderTrackResponse.buildCommonResponse(response);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        YtoOrderCharge ytoOrderCharge = YtoConverter.orderChargeConvertDo(orderChargeDTO);
        ytoOrderCharge.checkParams();
        String createString = JSONObject.toJSONString(ytoOrderCharge);
        //请求远程接口
        String ytoRequest = YtoRequest.buildRequestData(createSign(createString, ytoOrderChargeConfig), createString);
        log.info("请求圆通查询运价接口请求数据：{}",ytoRequest);
        String response = restClient.post(ytoRequest, ytoOrderChargeConfig.getUrl());
        log.info("请求圆通查询运价接口返回数据：{}",response);
        //构建返回参数 TODO
        LogisticsOrderChargeResponse.LogisticsOrderChargeResponseBuilder builder = LogisticsOrderChargeResponse.builder();
        builder.charge(BigDecimal.valueOf(Long.parseLong(response)));
        return builder.build();
    }

    private String createSign(String data, YtoBaseConfig ytoBaseConfig){
        String sign = null;
        try {
            byte[] signByte = DigestUtils.md5(data + ytoBaseConfig.getMethodName() 
                    + ytoBaseConfig.getVersion() + ytoBaseConfig.getSecretKey());
            sign = org.apache.commons.codec.binary.Base64.encodeBase64String(signByte);
            log.info("请求圆通快递签名sign==>{}",sign);
        } catch (Exception e) {
            log.error("签名失败", e);
        }
        return sign;
    }
}
