package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class JtOrderCharge {

    /**
     * 必填
     * 客户编码（联系出货网点提供）
     */
    private String customerCode;

    /**
     * 必填
     * 签名，Base64(Md5(客户编号+密文+privateKey))，其中密文：MD5(明文密码+jadada236t2) 后大写
     */
    private String digest;

    /**
     * 必填
     * 寄件信息对象
     */
    private PersonDto sender;

    /**
     * 必填
     * 收件信息对象
     */
    private PersonDto receiver;

    /**
     * 长，cm编号
     */
    private int length;

    /**
     * 宽，cm
     */
    private int width;

    /**
     *高，cm
     */
    private int height;

    /**
     * 必填
     *重量（正数），单位kg，范围0.01-30，为空默认0.02
     */
    private String weight;

}
