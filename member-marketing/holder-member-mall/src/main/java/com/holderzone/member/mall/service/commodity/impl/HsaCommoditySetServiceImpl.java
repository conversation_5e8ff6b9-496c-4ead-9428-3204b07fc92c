package com.holderzone.member.mall.service.commodity.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.commodity.ShareFormatEnum;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.mall.entity.commodity.HsaCommoditySet;
import com.holderzone.member.mall.mapper.commodity.HsaCommoditySetMapper;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: impl
 */
@Deprecated
@Service
public class HsaCommoditySetServiceImpl extends HolderBaseServiceImpl<HsaCommoditySetMapper, HsaCommoditySet> implements HsaCommoditySetService {

    @Resource
    private HsaCommoditySetMapper hsaCommoditySetMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Override
    public CommodityShareFormatVO getShareFormat(String operSubjectGuid) {
        HsaCommoditySet hsaCommoditySet = hsaCommoditySetMapper.selectOne(
                new LambdaQueryWrapper<HsaCommoditySet>()
                        .eq(HsaCommoditySet::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
        CommodityShareFormatVO commodityShareFormatVO = new CommodityShareFormatVO();
        if (Objects.isNull(hsaCommoditySet)) {
            //默认样式1
            commodityShareFormatVO.setStyle(1);
            return commodityShareFormatVO;
        }
        commodityShareFormatVO.setStyle(hsaCommoditySet.getStyle());
        commodityShareFormatVO.setGuid(hsaCommoditySet.getGuid());
        commodityShareFormatVO.setShareFormat(hsaCommoditySet.getShareFormat());
        return commodityShareFormatVO;
    }

    @Override
    public Boolean updateShareFormat(CommodityShareFormatVO shareFormat) {
        HsaCommoditySet hsaCommoditySet = hsaCommoditySetMapper.queryByGuid(shareFormat.getGuid());
        if (Objects.isNull(hsaCommoditySet)) {
            return Boolean.FALSE;
        }
        hsaCommoditySet.setStyle(shareFormat.getStyle());
        hsaCommoditySet.setShareFormat(shareFormat.getShareFormat());
        hsaCommoditySet.setOperatorName(ThreadLocalCache.getOperatorTelName());
        return hsaCommoditySetMapper.updateByGuid(hsaCommoditySet);
    }

    @RedissonLock(lockName = "INIT_SHARE_FORMAT", tryLock = true, leaseTime = 10)
    @Override
    public void initShareFormat(List<String> operSubjectGuids) {
        if (CollUtil.isEmpty(operSubjectGuids)) {
            return;
        }
        List<HsaCommoditySet> hsaCommoditySets = hsaCommoditySetMapper.selectList(
                new LambdaQueryWrapper<HsaCommoditySet>()
                        .in(HsaCommoditySet::getOperSubjectGuid, operSubjectGuids));
        if (CollUtil.isEmpty(hsaCommoditySets)) {
            saveBatchShareFormat(operSubjectGuids);
            return;
        }
        List<String> collect = hsaCommoditySets.stream()
                .map(HsaCommoditySet::getOperSubjectGuid).collect(Collectors.toList());
        operSubjectGuids = operSubjectGuids.stream().filter(x -> !collect.contains(x)).collect(Collectors.toList());
        saveBatchShareFormat(operSubjectGuids);
    }

    public void saveBatchShareFormat(List<String> operSubjectGuids) {
        List<HsaCommoditySet> initShareFormats = Lists.newArrayList();
        for (String operSubjectGuid : operSubjectGuids) {
            HsaCommoditySet hsaCommoditySet = new HsaCommoditySet();

            hsaCommoditySet.setGuid(guidGeneratorUtil.getStringGuid(HsaCommoditySet.class.getSimpleName()));
            hsaCommoditySet.setShareFormat(ShareFormatEnum.PRODUCT_NAME_PRICE.getCode());
            hsaCommoditySet.setOperatorName(StringConstant.ADMIN);
            hsaCommoditySet.setOperSubjectGuid(operSubjectGuid);

            initShareFormats.add(hsaCommoditySet);
        }
        this.saveBatch(initShareFormats);
    }
}
