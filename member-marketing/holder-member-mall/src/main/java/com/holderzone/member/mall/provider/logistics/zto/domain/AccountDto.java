package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountDto {

    /**
     * 电子面单账号（partnerType为2，orderType传1,2,4时必传）
     */
    private String accountId;

    /**
     * 客户编码（partnerType传1时必传）
     */
    private String customerId;

    /**
     * 单号类型:1.普通电子面单；74.星联电子面单；默认是1
     */
    private short type;

    /**
     * 电子面单密码（测试环境传ZTO123）
     */
    private String accountPassword;

    public AccountDto(String accountId, String customerId, short type) {
        this.accountId = accountId;
        this.customerId = customerId;
        this.type = type;
    }
}
