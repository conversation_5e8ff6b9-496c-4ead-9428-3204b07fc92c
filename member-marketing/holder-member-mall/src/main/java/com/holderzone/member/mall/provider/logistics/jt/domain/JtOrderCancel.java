package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class JtOrderCancel {

    /**
     * 客户编码（订单类型传2时，必填）
     */
    private String customerCode;

    /**
     * 签名，Base64(Md5(客户编号+密文+privateKey))，其中密文：MD5(明文密码+jadada236t2) 后大写
     */
    private String digest;

    /**
     * 必填
     * 订单类型 1（散客），2（协议客户）
     */
    private String orderType;

    /**
     * 客户订单号    传客户自己系统的订单号
     */
    private String txlogisticId;

    /**
     * 取消原因
     */
    private String reason;

}
