package com.holderzone.member.mall.provider.logistics.zto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import com.holderzone.member.common.exception.MallBaseException;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderCancelResponse {

    private OrderCancelResponse(){
        throw new MallBaseException();
    }


    public static LogisticsOrderCancelResponse buildCommonResponse(String json){
        ZtoBaseRsp<Object> rsp = JSONObject.parseObject(json, new TypeReference<ZtoBaseRsp<Object>>() {
        }.getType());

        if(rsp == null) {
            return null;
        }
        //TODO
        return LogisticsOrderCancelResponse.builder().build();

    }
}
