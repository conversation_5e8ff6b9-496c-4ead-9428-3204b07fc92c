package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderVasDto {

    /**
     * 代收账号（有代收货款增值时必填）
     */
    private String accountNo;

    /**
     * 增值类型 （COD：代收； vip：尊享； insured：保价； receiveReturnService：签单返回； twoHour：两小时；standardExpress：标快）
     */
    private String vasType;

    /**
     * 增值价格，如果增值类型涉及金额会校验，vasType为COD、insured时不能为空，单位：分
     */
    private long vasAmount;

    /**
     * 增值价格（暂时不用）
     */
    private long vasPrice;

    /**
     * 增值详情
     */
    private String vasDetail;
}
