package com.holderzone.member.mall.provider.logistics.yunda.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderBaseRequest<T> {

    /**
     * 必填
     * app_key
     */
    private String appid;

    /**
     * 必填
     * 韵达白马账号（合作网点提供）
     */
    private String partner_id;

    /**
     * 必填
     * 韵达白马账号的联调密码（合作网点提供）
     */
    private String secret;

    /**
     * 必填
     * 订单详情
     */
    private List<T> orders;

    public OrderBaseRequest(String appid, String partnerId, String secret) {
        this.appid = appid;
        this.partner_id = partnerId;
        this.secret = secret;
    }
}
