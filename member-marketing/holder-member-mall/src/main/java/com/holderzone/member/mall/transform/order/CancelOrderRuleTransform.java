package com.holderzone.member.mall.transform.order;

import com.holderzone.member.common.vo.order.OrderReasonVO;
import com.holderzone.member.mall.entity.order.HsaCancelOrderRule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CancelOrderRuleTransform {

    CancelOrderRuleTransform INSTANCE = Mappers.getMapper(CancelOrderRuleTransform.class);

    List<OrderReasonVO> DO2VOList(List<HsaCancelOrderRule> list);
}
