package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import com.alibaba.fastjson.JSONArray;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SfBaseResponse implements Serializable {

    /**
     * API平台结果代码
     */
    private String apiResultCode;

    /**
     * API平台异常信息
     */
    private String apiErrorMsg;

    /**
     * API响应唯一号UUID
     */
    private String apiResponseID;

    /**
     * 业务处理详细结果
     */
    private String apiResultData;

}
