package com.holderzone.member.mall.provider.logistics.converter;

import com.alibaba.fastjson.JSON;
import com.best.javaSdk.kyCancelOrderNotify.response.KyCancelOrderNotifyRsp;
import com.best.javaSdk.kyCreateOrderNotify.response.KyCreateOrderNotifyRsp;
import com.best.javaSdk.kyPriceAgingSearch.response.KyPriceAgingSearchRsp;
import com.best.javaSdk.kyTraceQuery.response.KyTraceQueryRsp;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.exception.MemberBaseException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class BestRspConverter {

    private BestRspConverter(){
        throw new MemberBaseException();
    }

    public static LogisticsOrderCreateResponse orderCreateConvertRsp(KyCreateOrderNotifyRsp rsp){
        LogisticsOrderCreateResponse.LogisticsOrderCreateResponseBuilder builder = LogisticsOrderCreateResponse.builder();
        log.info(JSON.toJSONString(rsp));
        //构建请求实体
        return builder.build();
    }

    public static LogisticsOrderCancelResponse orderCancelConvertRsp(KyCancelOrderNotifyRsp rsp){
        LogisticsOrderCancelResponse.LogisticsOrderCancelResponseBuilder builder = LogisticsOrderCancelResponse.builder();
        log.info(JSON.toJSONString(rsp));
        //构建请求实体
        return builder.build();
    }

    public static LogisticsOrderChargeResponse orderChargeConvertRsp(KyPriceAgingSearchRsp rsp){
        log.info(JSON.toJSONString(rsp));
        LogisticsOrderChargeResponse.LogisticsOrderChargeResponseBuilder builder = LogisticsOrderChargeResponse.builder();
        //构建请求实体
        return builder.build();
    }

    public static LogisticsOrderTrackResponse orderTrackConvertRsp(KyTraceQueryRsp rsp){
        log.info(JSON.toJSONString(rsp));
        LogisticsOrderTrackResponse.LogisticsOrderTrackResponseBuilder builder = LogisticsOrderTrackResponse.builder();
        //构建请求实体
        return builder.build();
    }

}
