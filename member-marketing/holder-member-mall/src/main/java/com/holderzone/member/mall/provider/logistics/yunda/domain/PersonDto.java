package com.holderzone.member.mall.provider.logistics.yunda.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PersonDto {

    /**
     * 必填
     * 寄件人姓名
     */
    private String name;

    /**
     * 寄件人公司
     */
    private String company;

    /**
     * 必填
     * 寄件人省
     */
    private String province;

    /**
     * 必填
     * 寄件人市
     */
    private String city;

    /**
     * 必填
     * 寄件人区县
     */
    private String county;

    /**
     * 必填
     * 详细地址
     */
    private String address;

    /**
     * 邮编
     */
    private String postcode;

    /**
     * 固定电话(手机号固话必填一项)
     */
    private String phone;

    /**
     * 手机号(手机号固话必填一项)
     */
    private String mobile;
}
