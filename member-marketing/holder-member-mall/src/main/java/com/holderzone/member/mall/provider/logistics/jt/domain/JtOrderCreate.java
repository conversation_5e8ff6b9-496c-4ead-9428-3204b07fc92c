package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class JtOrderCreate {

    /**
     * 必填
     * 客户编码（联系出货网点提供）
     */
    private String customerCode;

    /**
     * 签名，Base64(Md5(客户编号+密文+privateKey))，其中密文：MD5(明文密码+jadada236t2) 后大写
     */
    private String digest;

    /**
     * 合作网点编码（没有则不传）
     */
    private String network;

    /**
     * 必填
     * 客户订单号（传客户自己系统的订单号）
     */
    private String txlogisticId;

    /**
     * 运单编号
     */
    private String billCode;

    /**
     * 必填
     * 快件类型：EZ(标准快递)
     */
    private String expressType;

    /**
     * 必填
     *订单类型（有客户编号为月结）1、 散客；2、月结；
     */
    private String orderType;

    /**
     * 必填
     *服务类型 ：02 门店寄件 ； 01 上门取件
     */
    private String serviceType;

    /**
     * 必填
     * 派送类型： 06 代收点自提 05 快递柜自提 04 站点自提 03 派送上门
     */
    private String deliveryType;

    /**
     * 必填
     * 支付方式：PP_PM("寄付月结"), CC_CASH("到付现结");；
     */
    private String payType;

    /**
     * 必填
     * 寄件信息对象
     */
    private PersonDto sender;

    /**
     * 必填
     * 收件信息对象
     */
    private PersonDto receiver;

    /**
     * 物流公司上门取货开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String sendStartTime;

    /**
     * 客户物流公司上门取货结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String sendEndTime;

    /**
     * 必填
     *物品类型（对应订单主表物品类型）:
     * bm000001 文件
     * bm000002 数码产品
     * bm000003 生活用品
     * bm000004 食品
     * bm000005 服饰
     * bm000006 其他
     * bm000007 生鲜类
     * bm000008 易碎品
     * bm000009 液体
     */
    private String goodsType;

    /**
     * 是否实名
     */
    private boolean isRealName;

    /**
     * 长，cm编号
     */
    private int length;

    /**
     * 宽，cm
     */
    private int width;

    /**
     * 高，cm；
     */
    private int height;

    /**
     * 必填
     *重量，单位kg，范围0.01-30
     */
    private String weight;

    /**
     * 包裹总票数(必须为1)
     */
    private int totalQuantity;

    /**
     * 代收货款金额 (数值型)
     */
    private String itemsValue;

    /**
     * 代收货款币别（默认本国币别，如：RMB）
     */
    private String priceCurrency;

    /**
     * 保价金额(数值型)，单位：元
     */
    private String offerFee;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品信息列表
     */
    private List<ItemDto> items;

    /**
     * 客户信息。
     */
    private CustomsInfoDto customsInfo;

    /**
     * 驿站编码
     */
    private String postSiteCode;

    /**
     * 驿站名称
     */
    private String postSiteName;

    /**
     * 驿站地址
     */
    private String postSiteAddress;

}
