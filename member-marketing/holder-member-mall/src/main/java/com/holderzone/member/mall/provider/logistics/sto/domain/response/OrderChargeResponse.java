package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderChargeResponse implements Serializable {

    /**
     * 寄件服务报价、预约信息来源编号
     */
    private String sourceCode;

    /**
     * 寄件服务类型详情
     */
    private List<AvailableServiceItem> availableServiceItemList;

    /**
     * 时效（单位小时）
     */
    private String ageing;

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderChargeResponse buildCommonResponse(String rsp){
        StoBaseResponse<OrderChargeResponse> stoBaseResponse = JSONObject.parseObject(rsp, new TypeReference<StoBaseResponse<OrderChargeResponse>>(){}.getType());

        if(stoBaseResponse == null || stoBaseResponse.getData() == null){
            return null;
        }
        return LogisticsOrderChargeResponse.builder().build();
    }

}
