package com.holderzone.member.mall.provider.logistics.yto.domain.response;

import com.alibaba.fastjson.JSONArray;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse implements Serializable {

    /**
     * 运单号
     */
    private String waybill_No;

    /**
     * 走件产生时间 yyyy-MM-dd HH:mm:ss
     */
    private String upload_Time;

    /**
     * 物流状态，固定为：GOT 已收件;ARRIVAL 已收入;DEPARTURE 已发出;PACKAGE 已打包;SENT_SCAN 派件;
     * INBOUND 自提柜入柜;SIGNED 签收成功;FAILED 签收失败;FORWARDING 转寄;TMS_RETURN 退回;
     */
    private String infoContent;

    /**
     * 物流信息
     */
    private String processInfo;

    /**
     * 当前操作城市
     */
    private String city;

    /**
     * 当前操作区或者县
     */
    private String district;


    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderTrackResponse buildCommonResponse(String json){
        try {
            List<OrderTrackResponse> orderTrackResponse = JSONArray.parseArray(json, OrderTrackResponse.class);
            if(orderTrackResponse == null){
                return null;
            }
            //TODO
            return LogisticsOrderTrackResponse.builder().build();
        }catch (Exception e){
            return null;
        }
    }

}
