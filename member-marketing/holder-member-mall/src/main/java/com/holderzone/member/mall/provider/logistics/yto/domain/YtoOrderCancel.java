package com.holderzone.member.mall.provider.logistics.yto.domain;

import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.mall.provider.constant.ParamsCheckConstant;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 圆通订单取消
 */
@Data
@Builder
public class YtoOrderCancel {
    /**
     * 必填
     * 	物流单号，打印拉取运单号前，物流单号和渠道唯一确定一笔快递物流订单。
     */
    private String logisticsNo;

    /**
     * 必填
     * 	取消原因。
     */
    private String cancelDesc;

    public void checkParams() {
        if(StringUtils.isEmpty(logisticsNo)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"logisticsNo"));
        }
        if(StringUtils.isEmpty(cancelDesc)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"cancelDesc"));
        }
    }
}
