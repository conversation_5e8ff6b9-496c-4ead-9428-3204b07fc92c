package com.holderzone.member.mall.provider.logistics.tt.config;

import com.holderzone.member.mall.provider.logistics.tt.client.Api;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "tt")
public class TtRequestConfig {

    private String apiKey;

    @Bean
    public Api initApi(){
        return new Api(apiKey);
    }
}
