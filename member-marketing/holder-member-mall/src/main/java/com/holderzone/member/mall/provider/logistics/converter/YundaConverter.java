package com.holderzone.member.mall.provider.logistics.converter;

import com.google.common.collect.Lists;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.yunda.config.YundaRequestConfig;
import com.holderzone.member.mall.provider.logistics.yunda.domain.*;

/**
 * <AUTHOR>
 */
public class YundaConverter {

    private YundaConverter(){
        throw  new MemberBaseException();
    }

    public static OrderBaseRequest<YundaOrderCreate> orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO
    , YundaRequestConfig yundaRequestConfig){
        OrderBaseRequest<YundaOrderCreate> request = new OrderBaseRequest<>(yundaRequestConfig.getAppKey(), yundaRequestConfig.getPartnerId()
                , yundaRequestConfig.getPartnerSecret());
        YundaOrderCreate.YundaOrderCreateBuilder builder = YundaOrderCreate.builder();
        builder.order_type("common");
        builder.node_id(orderCreateDTO.getLogisticsNo());
        request.setOrders(Lists.newArrayList(builder.build()));
        //构建请求实体
        return request;
    }

    public static OrderBaseRequest<YundaOrderCancel> orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO
            ,YundaRequestConfig yundaRequestConfig){
        OrderBaseRequest<YundaOrderCancel> request = new OrderBaseRequest<>(yundaRequestConfig.getAppKey(), yundaRequestConfig.getPartnerId()
                , yundaRequestConfig.getPartnerSecret());
        YundaOrderCancel.YundaOrderCancelBuilder builder = YundaOrderCancel.builder();
        builder.order_serial_no(orderCancelDTO.getOrderNo());
        request.setOrders(Lists.newArrayList(builder.build()));
        //构建请求实体
        return request;
    }

    public static YundaOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        YundaOrderTrack.YundaOrderTrackBuilder builder = YundaOrderTrack.builder();
        builder.mailno(orderTrackDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static YundaOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        YundaOrderCharge.YundaOrderChargeBuilder builder = YundaOrderCharge.builder();
        //构建请求实体
        builder.weight(orderChargeDTO.getOrderNo());
        return builder.build();
    }
}
