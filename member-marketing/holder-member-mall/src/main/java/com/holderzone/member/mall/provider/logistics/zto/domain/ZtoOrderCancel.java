package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ZtoOrderCancel {
    /**
     * 必填
     * 取消类型 1不想寄了,2下错单,3重复下单,4运费太贵,5无人联系,6取件太慢,7态度差
     */
    private String cancelType;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 运单编号
     */
    private String billCode;

}
