package com.holderzone.member.mall;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.common.*"),
        @ComponentScan("com.holderzone.member.mall.*"),
        @ComponentScan("com.holderzone.member.common.client"),
        @ComponentScan("com.holderzone.member.commodity.*"),
})
@EnableAsync
@EnableFeignClients(basePackages="com.holderzone.member.common.feign")
public class HolderMemberMallApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderMemberMallApplication.class, args);
    }

}
