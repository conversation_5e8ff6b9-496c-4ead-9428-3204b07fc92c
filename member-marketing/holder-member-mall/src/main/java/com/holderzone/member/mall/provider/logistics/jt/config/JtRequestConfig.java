package com.holderzone.member.mall.provider.logistics.jt.config;

import com.holderzone.member.mall.provider.logistics.jt.client.JtClient;
import com.yl.jms.sdk.auth.ClientConfiguration;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "jt")
public class JtRequestConfig {

    private String apiAccount;

    private String privateKey;

    private String createUrl;

    private String cancelUrl;

    private String chargeUrl;

    private String trackUrl;

    @Bean
    public JtClient jtClient(){
        //创建授权用户实体
        ClientConfiguration clientConfiguration = new ClientConfiguration(apiAccount,privateKey);
        return new JtClient(clientConfiguration);
    }
}
