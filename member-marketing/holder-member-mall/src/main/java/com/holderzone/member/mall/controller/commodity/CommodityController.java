package com.holderzone.member.mall.controller.commodity;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.commodity.CategoryCommodityQO;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.mall.CommodityPageQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.tool.CategoryCommodityVo;
import com.holderzone.member.common.vo.tool.CategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MallCommodityController
 */
@RestController
@RequestMapping("/commodity")
@ApiModel(description = "商城商品")
public class CommodityController {

    @Resource
    private MemberCommodityFeign memberCommodityFeign;

    @Resource
    private HsaCommoditySetService hsaCommoditySetService;

    @Resource
    private LogisticsCacheSupport logisticsCacheSupport;

    /**
     * 查询运营主体下分类
     */
    @PostMapping(value = "/get/commodity/category", produces = "application/json;charset=utf-8")
    public Result<List<CategoryVO>> getCommodityCategory() {
        return Result.success(memberCommodityFeign.getCommodityCategory());
    }

    /**
     * 查询商品详情
     */
    @PostMapping(value = "/get/commodity_details", produces = "application/json;charset=utf-8")
    public Result<CommodityDetailsVO> getCommodityDetails(@RequestBody CommodityDetailsQO commodityDetailsQO) {
        CommodityDetailsVO commodityDetailsVO = memberCommodityFeign.getCommodityDetails(commodityDetailsQO);
        // 运费
        if (Objects.nonNull(commodityDetailsVO)) {
            commodityDetailsVO.setFreight(logisticsCacheSupport.getFreightAmount(commodityDetailsVO.getCommodity_code()));
        }
        return Result.success(commodityDetailsVO);
    }

    @ApiOperation("商品分页列表")
    @PostMapping("/page_commodity")
    public Result<PageResult> pageCommodity(@RequestBody CommodityConditionQO request) {
        request.setStrategyStatus(2);
        request.setCommodityState(2);
        request.setChannel("会员商城");
        request.setIsDistinct(1);
        request.setType(2);
        return Result.success(memberCommodityFeign.pageCommodity(request));
    }

    @ApiOperation("主体下所有分类")
    @PostMapping("/list_category")
    public Result<List<LinkUniteVO>> listCategory(@RequestBody CommodityConditionQO conditionQO) {
        return Result.success(memberCommodityFeign.listCategory(conditionQO));
    }

    @ApiOperation("门店分类查询")
    @PostMapping("/list_category_pure")
    public Result<List<CategoryCommodityVo>> listCategoryPure(@RequestBody CategoryCommodityQO conditionQO) {
        return Result.success(memberCommodityFeign.listCategoryPure(conditionQO));
    }

    @ApiOperation("主体下所有分类")
    @PostMapping("/list_category_commodity")
    public Result<List<CategoryCommodityVo>> listCategoryCommodity(@RequestBody CategoryCommodityQO conditionQO) {
        return Result.success(memberCommodityFeign.listCategoryCommodity(conditionQO));
    }

    @PostMapping(value = "/get/commodity", produces = "application/json;charset=utf-8")
    public Result findMallBaseProductPage(@RequestBody CommodityPageQO commodityPageQO) {
        return Result.success(memberCommodityFeign.findMallBaseProductPage(commodityPageQO));
    }

    /**
     * 通过运营主体获取商品分享标签格式
     *
     * @return 商品分享格式
     */
    @GetMapping("/get_share_format")
    public Result<CommodityShareFormatVO> getShareFormat() {
        return Result.success(hsaCommoditySetService.getShareFormat(ThreadLocalCache.getOperSubjectGuid()));
    }
}
