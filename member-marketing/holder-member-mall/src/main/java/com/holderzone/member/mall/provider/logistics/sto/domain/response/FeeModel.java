package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FeeModel {
    /**
     * 首重价格（单位分）
     */
    private Long startPrice;

    /**
     * 续重重量（单位克），重量调整粒度
     */
    private Long continuedHeavy;

    /**
     * 首重重量（单位克）
     */
    private Long startWeight;

    /**
     * 续重费用（单位分），每增加一份续重重量需要增加的价格
     */
    private Long continuedHeavyPrice;

    /**
     * 预估运费（单位分）
     */
    private String totalPrice;
}
