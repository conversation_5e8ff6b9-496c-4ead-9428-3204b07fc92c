package com.holderzone.member.mall.controller.applet;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.qo.mall.QueryMallProductQO;
import com.holderzone.member.common.qo.mall.order.OrderReceiverAddressQO;
import com.holderzone.member.common.util.secret.SecretUtil;
import com.holderzone.member.mall.service.order.HsaOrderReceiverAddressService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @program: member-marketing
 * @description: 买家收货地址
 * @author: pan tao
 * @create: 2022-08-11 17:40
 */
@RestController
@RequestMapping("/applets")
@Slf4j
public class AppletMallController {

    @Resource
    private HsaOrderReceiverAddressService hsaOrderReceiverAddressService;

    @PostMapping("/saveReceiverAddress")
    public Result<String> saveOrUpdate(@RequestBody OrderReceiverAddressQO request){
        return Result.success(hsaOrderReceiverAddressService.saveOrUpdate(request));
    }

    @GetMapping("/getReceiverAddress")
    public Result<String> getByMemberGuid(@RequestParam(value = "memberGuid") String memberGuid){
        return Result.success(SecretUtil.encrypt(JacksonUtils.writeValueAsString(hsaOrderReceiverAddressService.getByMemberGuid(memberGuid))));
    }

    @GetMapping("/deleteByGuid")
    public Result<Boolean> deleteByGuid(@RequestParam(value = "guid") String guid){
        return Result.success(hsaOrderReceiverAddressService.deleteByGuid(guid));
    }
}
