package com.holderzone.member.mall.service.permission.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.FunctionModelDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.OperationPermissionQO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.enums.permission.SubjectPermissionTypeEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.HolderFeign;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.entity.permission.HsaOperSubjectPermissionType;
import com.holderzone.member.mall.entity.permission.HsaSubjectPermission;
import com.holderzone.member.mall.mapper.permission.HsaMallOperSubjectPermissionTypeMapper;
import com.holderzone.member.mall.mapper.permission.HsaSubjectPermissionMapper;
import com.holderzone.member.mall.service.permission.HsaSubjectPermissionService;
import com.holderzone.member.mall.transform.system.MemberPermissionTransform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class HsaSubjectPermissionServiceImpl extends HolderBaseServiceImpl<HsaSubjectPermissionMapper, HsaSubjectPermission> implements HsaSubjectPermissionService {

    @Resource
    private HsaSubjectPermissionMapper hsaSubjectPermissionMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private HsaMallOperSubjectPermissionTypeMapper hsaMallOperSubjectPermissionTypeMapper;

    // TODO holder改造(已改)
    @Lazy
    @Resource
    private ExternalSupport externalSupport;

    private static final String SUBJECT_FUNCTION_GROUP = "运营主体";

    private static final String SUBJECT_FUNCTION = "运营主体管理范围";

    @Override
    public List<HsaOperSubjectPermissionVO> getSubjectPermissionList(OperSubjectPermissionQO request) {
        List<HsaSubjectPermission> hsaOperSubjectPermissions = hsaSubjectPermissionMapper.selectList(
                new LambdaQueryWrapper<HsaSubjectPermission>()
                        .eq(HsaSubjectPermission::getEnterpriseGuid, request.getTeamId())
                        .eq(HsaSubjectPermission::getPositionGuid, request.getRoleId())
                        .eq(HsaSubjectPermission::getSourceType, request.getSourceType()));
        List<OperSubjectInfo> operSubjectInfos = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubjectByTeam(request.getTeamId());
        if (CollUtil.isNotEmpty(hsaOperSubjectPermissions)) {
            //判断当前数据库中的运营主体是否需要修改
            if (!isUpdateSubject(hsaOperSubjectPermissions, operSubjectInfos)) {
                return MemberPermissionTransform.INSTANCE.subjectPermissionListTOVO(hsaOperSubjectPermissions);
            }
            return this.updateSubjectHandler(hsaOperSubjectPermissions, request, operSubjectInfos);
        }
        //如果当前岗位没有配置运营主体权限，则默认为拥有所有运营主体权限
        //查询出当前企业下所有的运营主体信息
        if (Objects.isNull(operSubjectInfos)) {
            return new ArrayList<>();
        }
        hsaOperSubjectPermissions = setDataHandler(operSubjectInfos, request);
        this.saveBatch(hsaOperSubjectPermissions);

        return MemberPermissionTransform.INSTANCE.subjectPermissionListTOVO(hsaOperSubjectPermissions);
    }

    /**
     * 如果运营主体有删除或者新增特殊处理
     *
     * @param hsaSubjectPermissions 本地运营主体数据
     * @param request               请求参数
     * @param operSubjectInfos      实时的运营主体数据
     * @return 运营主体数据
     */
    public List<HsaOperSubjectPermissionVO> updateSubjectHandler(List<HsaSubjectPermission> hsaSubjectPermissions,
                                                                 OperSubjectPermissionQO request,
                                                                 List<OperSubjectInfo> operSubjectInfos) {
        //当前数据库运营主体guid
        Map<String, HsaSubjectPermission> operSubjectMap = hsaSubjectPermissions.stream()
                .collect(Collectors.toMap(HsaSubjectPermission::getOperSubjectGuid, Function.identity(), (obj1, obj2) -> obj1));

        //数据库中没有存到的运营主体集合
        List<OperSubjectInfo> operSubjectInfoList = operSubjectInfos.stream().filter(x -> !operSubjectMap.containsKey(x.getOperSubjectGuid())).collect(Collectors.toList());
        //数据库中已经有的运营主体集合
        List<HsaSubjectPermission> hsaOperSubjectPermissionList = new ArrayList<>();
        for (OperSubjectInfo operSubjectInfo : operSubjectInfos) {
            String operSubjectGuid = operSubjectInfo.getOperSubjectGuid();
            if (operSubjectMap.containsKey(operSubjectGuid)) {
                HsaSubjectPermission hsaOperSubjectPermission = operSubjectMap.get(operSubjectGuid);
                hsaOperSubjectPermission.setMultiMemberName(operSubjectInfo.getMultiMemberName());
                hsaOperSubjectPermissionList.add(hsaOperSubjectPermission);
            }
        }
        //合并数据
        hsaOperSubjectPermissionList.addAll(setDataHandler(operSubjectInfoList, request));
        transactionTemplate.execute(transactionStatus -> {
            //删除当前岗位下旧数据
            this.remove(new LambdaQueryWrapper<HsaSubjectPermission>()
                    .eq(HsaSubjectPermission::getEnterpriseGuid, request.getTeamId())
                    .eq(HsaSubjectPermission::getPositionGuid, request.getRoleId())
                    .eq(HsaSubjectPermission::getIsRole, request.getIsRole())
                    .eq(HsaSubjectPermission::getSourceType, request.getSourceType()));
            //新增新数据
            this.saveBatch(hsaOperSubjectPermissionList);
            return Boolean.TRUE;
        });
        return MemberPermissionTransform.INSTANCE.subjectPermissionListTOVO(hsaOperSubjectPermissionList);
    }

    /**
     * 判断当前数据库中的运营主体是否需要修改
     *
     * @param hsaOperSubjectPermissions 本地数据库中的运营主体
     * @param operSubjectInfos          实时查询出来的运营主体
     * @return 是否需要更新本地库中的运营主体
     */
    public boolean isUpdateSubject(List<HsaSubjectPermission> hsaOperSubjectPermissions, List<OperSubjectInfo> operSubjectInfos) {
        //实时的运营主体数量
        int size = operSubjectInfos.size();
        if (size != hsaOperSubjectPermissions.size()) {
            return true;
        }
        Map<String, HsaSubjectPermission> operSubjectMap = hsaOperSubjectPermissions.stream()
                .collect(Collectors.toMap(HsaSubjectPermission::getOperSubjectGuid, Function.identity(), (obj1, obj2) -> obj1));
        //主要用于判断本地数据库中的运营主体名称是否被修改了
        for (OperSubjectInfo operSubjectInfo : operSubjectInfos) {
            String operSubjectGuid = operSubjectInfo.getOperSubjectGuid();
            String multiMemberName = operSubjectInfo.getMultiMemberName();
            if (!operSubjectMap.containsKey(operSubjectGuid)) {
                return true;
            }
            //本地库运营主体
            HsaSubjectPermission hsaOperSubjectPermission = operSubjectMap.get(operSubjectGuid);
            String subjectGuid = hsaOperSubjectPermission.getOperSubjectGuid();
            String subjectName = hsaOperSubjectPermission.getMultiMemberName();
            if (!Objects.equals(operSubjectGuid, subjectGuid) || !Objects.equals(multiMemberName, subjectName)) {
                return true;
            }
        }
        return false;
    }

    public List<HsaSubjectPermission> setDataHandler(List<OperSubjectInfo> operSubjectInfos, OperSubjectPermissionQO request) {
        List<HsaSubjectPermission> hsaOperSubjectPermissionList = new ArrayList<>();
        if (CollUtil.isEmpty(operSubjectInfos)) {
            return hsaOperSubjectPermissionList;
        }
        for (OperSubjectInfo operSubjectInfo : operSubjectInfos) {
            HsaSubjectPermission hsaOperSubjectPermission = new HsaSubjectPermission();
            hsaOperSubjectPermission.setOperSubjectGuid(operSubjectInfo.getOperSubjectGuid());
            hsaOperSubjectPermission.setMultiMemberName(operSubjectInfo.getMultiMemberName());
            hsaOperSubjectPermission.setMultiMemberStatus(operSubjectInfo.getMultiMemberStatus());
            hsaOperSubjectPermission.setPositionGuid(request.getRoleId());
            hsaOperSubjectPermission.setIsRole(request.getIsRole());
            hsaOperSubjectPermission.setEnterpriseGuid(request.getTeamId());
            hsaOperSubjectPermission.setSourceType(request.getSourceType());
            hsaOperSubjectPermission.setGuid(guidGeneratorUtil.getStringGuid(HsaSubjectPermission.class.getSimpleName()));
            hsaOperSubjectPermission.setIsChecked(BooleanEnum.FALSE.getCode());
            hsaOperSubjectPermissionList.add(hsaOperSubjectPermission);
        }
        return hsaOperSubjectPermissionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSubjectPermission(HsaOperSubjectPermissionQO request) {
        //运营主体信息
        List<HsaOperSubjectPermissionVO> hsaSubjectPermissionVOList = request.getHolderPermission();
        //运营主体类型
        Integer isAll = request.getIsAll();
        if (Objects.isNull(isAll)) {
            return false;
        }
        //更新运营主体类型状态
        boolean updateSubjectType = updateSubjectType(request);
        if (CollUtil.isEmpty(hsaSubjectPermissionVOList)) {
            return updateSubjectType;
        }
        //更新运营主体权限信息是否选中状态
        return updateSubjectInfo(hsaSubjectPermissionVOList);
    }

    @Override
    public MemberSystemPermissionVO getAccountPermission(String identification) {
        MemberSystemPermissionVO memberSystemPermissionVO = new MemberSystemPermissionVO();
        if (ThreadLocalCache.getSystem() == SystemEnum.RETAIL.getCode()) {
            memberSystemPermissionVO.setIsAll(BooleanEnum.TRUE.getCode());
        }
        HeaderUserInfo headerUserInfo = ThreadLocalCache.getHeaderUserInfo();
        String userGuid = Optional.ofNullable(headerUserInfo).map(HeaderUserInfo::getUserGuid).orElse(null);
        //获取应用系统操作权限
        List<MemberSystemPermissionDTO> systemPermission = getSystemPermission(identification, userGuid);
        memberSystemPermissionVO.setMemberSystemPermissionDTOs(systemPermission);
        if (SystemPermissionEnum.MEMBER_MALL.getDes().equals(identification)) {
            RolePermissionMapModel rolePermissionMapModel = this.getJobAndRoleInfo(identification, userGuid);
            //配置当前用户运营主体权限
            int sourceType = SystemPermissionEnum.MEMBER_MALL.getCodeByIdent(identification);
            List<PermissionModelDTO> operSubjectPermission = getOperSubjectPermission(systemPermission, sourceType, rolePermissionMapModel);
            memberSystemPermissionVO.setPermissionModelDTOS(operSubjectPermission);
        }
        return memberSystemPermissionVO;
    }

    @Override
    public OperSubjectInfoVO getOperatingSubject() {
        OperSubjectInfoVO subjectInfoVO = new OperSubjectInfoVO();
        List<OperSubjectInfo> subjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid());
        HeaderUserInfo userInfo = queryUserInformation();
        subjectInfoVO.setOperSubjectInfos(subjectInfoList);
        BeanUtils.copyProperties(userInfo, subjectInfoVO);
        return subjectInfoVO;
    }

    public HeaderUserInfo queryUserInformation() {
        HeaderUserInfo headerUserInfo = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid());
        return Optional.ofNullable(headerUserInfo).orElse(new HeaderUserInfo());
    }

    public RolePermissionMapModel getJobAndRoleInfo(String identification, String userGuid) {
        return executeRequest(identification, userGuid);
    }

    public RolePermissionMapModel executeRequest(String identification, String userGuid) {
        RolePermissionMapModel userRoleMapPermission = new RolePermissionMapModel();
        if (StringUtils.isEmpty(userGuid)) {
            HeaderUserInfo userInfo = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid());
            userGuid = userInfo.getUserGuid();
        }
        OperationPermissionQO operationPermissionQO = new OperationPermissionQO();
        operationPermissionQO.setIdentifications(Collections.singletonList(identification));
        operationPermissionQO.setUserId(userGuid);
        operationPermissionQO.setTeamId(ThreadLocalCache.getEnterpriseGuid());
        try {
            log.info("获取系统权限列表请求参数operationPermissionQO:{}", JSON.toJSONString(operationPermissionQO));
            userRoleMapPermission = externalSupport.baseServer(ThreadLocalCache.getSystem()).findUserRoleMapPermission(operationPermissionQO);
        } catch (Exception e) {
            log.error("Method:getJobAndRoleInfo Get user permission error:{}", e.getMessage());
        }
        return userRoleMapPermission;
    }

    /**
     * 获取运营主体权限信息
     * 运营主体权限存在我们这边数据库中，所有需要手动查询出来
     *
     * @return 当前用户运营主体权限
     */
    public List<PermissionModelDTO> getOperSubjectPermission(List<MemberSystemPermissionDTO> memberSystemPermissions, Integer sourceType
            , RolePermissionMapModel rolePermissionMapModel) {
        if (CollUtil.isEmpty(memberSystemPermissions)) {
            return new ArrayList<>();
        }
        //用户岗位id集合
        List<String> jobsByAccount = jobAndRoleList(rolePermissionMapModel.getRoleMap(), SUBJECT_FUNCTION_GROUP, SUBJECT_FUNCTION);
        //用户角色id集合
        List<String> rolesByAccount = jobAndRoleList(rolePermissionMapModel.getEntRoleMap(), SUBJECT_FUNCTION_GROUP, SUBJECT_FUNCTION);
        if (CollUtil.isEmpty(jobsByAccount) && CollUtil.isEmpty(rolesByAccount)) {
            return new ArrayList<>();
        }
        List<HsaOperSubjectPermissionVO> operSubjectPermissionList;
        //查询出当前用户，所有岗位的权限信息
        operSubjectPermissionList = subjectPermissionHandler(jobsByAccount, rolesByAccount, sourceType);

        //运营主体权限信息去重
        ArrayList<HsaOperSubjectPermissionVO> collect = operSubjectPermissionList.stream().filter(x -> Objects.equals(x.getIs_checked(), 1)).collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(x -> x.getName() + ";" + x.getOperSubjectGuid()))), ArrayList::new));

        return setPermissionModelDTO(collect);
    }

    /**
     * 设置运营主体权限信息
     *
     * @param hsaSubjectPermissionVOS 运营主体权限信息
     * @return VO数据
     */
    public List<PermissionModelDTO> setPermissionModelDTO(List<HsaOperSubjectPermissionVO> hsaSubjectPermissionVOS) {
        List<PermissionModelDTO> permissionModelDTOS = new ArrayList<>();
        if (CollUtil.isEmpty(hsaSubjectPermissionVOS)) {
            return permissionModelDTOS;
        }
        for (HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO : hsaSubjectPermissionVOS) {
            PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
            permissionModelDTO.setId(hsaOperSubjectPermissionVO.getOperSubjectGuid());
            permissionModelDTO.setPermissionName(hsaOperSubjectPermissionVO.getName());
            permissionModelDTO.setIsChecked(hsaOperSubjectPermissionVO.getIs_checked());
            permissionModelDTOS.add(permissionModelDTO);
        }
        return permissionModelDTOS;
    }

    /**
     * 获取用户角色和岗位下所有的运营主体权限信息
     *
     * @param jobsByAccount  岗位id集合
     * @param rolesByAccount 角色id集合
     * @param sourceType     数据来源
     * @return 当前用户所有的运营主体权限信息
     */
    private List<HsaOperSubjectPermissionVO> subjectPermissionHandler(List<String> jobsByAccount, List<String> rolesByAccount, Integer sourceType) {
        List<String> jobAndRoleGuids = new ArrayList<>();
        //获取当前账户岗位下所有运营主体权限信息
        if (CollUtil.isNotEmpty(jobsByAccount)) {
            jobAndRoleGuids.addAll(jobsByAccount);
        }
        //获取当前账户角色下所有运营主体权限信息
        if (CollUtil.isNotEmpty(rolesByAccount)) {
            jobAndRoleGuids.addAll(rolesByAccount);
        }
        return getSubjectPermissionVOList(jobAndRoleGuids, sourceType);
    }

    /**
     * 返回当前用户所在系统模块中的运营主体权限
     *
     * @param jobAndRoleGuids 岗位id和角色id集合
     * @param sourceType      系统模块类型
     * @return 运营主体权限信息
     */
    private List<HsaOperSubjectPermissionVO> getSubjectPermissionVOList(List<String> jobAndRoleGuids, Integer sourceType) {
        String enterpriseGuid = ThreadLocalCache.getEnterpriseGuid();
        List<HsaOperSubjectPermissionVO> hsaOperSubjectPermissionVOList = new ArrayList<>();
        //查询当前用户运营主体权限类型是否拥有所有权限
        List<HsaOperSubjectPermissionType> hsaOperSubjectPermissionTypes = hsaMallOperSubjectPermissionTypeMapper.selectList(
                new LambdaQueryWrapper<HsaOperSubjectPermissionType>()
                        .eq(HsaOperSubjectPermissionType::getEnterpriseGuid, enterpriseGuid)
                        .eq(HsaOperSubjectPermissionType::getSourceType, sourceType)
                        .in(HsaOperSubjectPermissionType::getPositionGuid, jobAndRoleGuids));
        if (CollUtil.isEmpty(hsaOperSubjectPermissionTypes)) {
            log.info("当前用户获取的岗位或者角色的运营主体类型未初始化");
            return hsaOperSubjectPermissionVOList;
        }
        Optional<HsaOperSubjectPermissionType> first = hsaOperSubjectPermissionTypes.stream()
                .filter(x -> Objects.nonNull(x.getIsAll()) &&
                        SubjectPermissionTypeEnum.ALL_PERMISSION.getCode() == x.getIsAll()).findFirst();
        //判断用户是否拥有全部的运营主体
        //拥有查询所有运营主体信息
        if (first.isPresent()) {
            log.info("当前用户拥有全部运营主体权限");
            List<OperSubjectInfo> operSubjectInfoList = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryOperatingSubject(ThreadLocalCache.getEnterpriseGuid());
            hsaOperSubjectPermissionVOList = MemberPermissionTransform.INSTANCE
                    .subjectPermissionListToVo(operSubjectInfoList);
            //拥有部分,返回对应运营主体信息
        } else {
            log.info("当前用户拥有部分运营主体权限");
            List<HsaSubjectPermission> hsaOperSubjectPermissions = hsaSubjectPermissionMapper.selectList(
                    new LambdaQueryWrapper<HsaSubjectPermission>()
                            .eq(HsaSubjectPermission::getEnterpriseGuid, enterpriseGuid)
                            .eq(HsaSubjectPermission::getSourceType, sourceType)
                            .in(HsaSubjectPermission::getPositionGuid, jobAndRoleGuids));
            hsaOperSubjectPermissionVOList = MemberPermissionTransform.INSTANCE
                    .subjectPermissionListTOVO(hsaOperSubjectPermissions);
        }
        return hsaOperSubjectPermissionVOList;
    }

    /**
     * 查询出拥有指定权限的角色id或者岗位id集合
     *
     * @param permissionMap 角色或者岗位所拥有的所有权限  key:角色id或者岗位id   value:权限集合
     * @param functionGroup 要查询的权限分组名称
     * @param function      要查询的权限名称
     * @return 拥有查询权限的角色和岗位id集合
     */
    public List<String> jobAndRoleList(Map<String, List<MemberSystemPermissionDTO>> permissionMap, String functionGroup, String function) {
        List<String> userList = new ArrayList<>();
        if (CollUtil.isEmpty(permissionMap)) {
            return userList;
        }
        Set<String> keyList = permissionMap.keySet();
        for (String key : keyList) {
            List<MemberSystemPermissionDTO> memberSystemPermissionDTOS = permissionMap.get(key);
            Boolean jobPermission = isPermission(memberSystemPermissionDTOS, functionGroup, function);
            if (Boolean.TRUE.equals(jobPermission)) {
                userList.add(key);
            }
        }
        return userList;
    }

    /**
     * 通过权限分组和权限信息，查询当前权限集合中是否存在
     *
     * @param systemPermissionDTOList 权限集合
     * @param functionGroup           权限分组
     * @param function                权限
     * @return 是否存在查询权限
     */
    public Boolean isPermission(List<MemberSystemPermissionDTO> systemPermissionDTOList, String functionGroup, String function) {
        if (CollUtil.isEmpty(systemPermissionDTOList)) {
            return false;
        }
        String info = JSON.toJSONString(systemPermissionDTOList);
        List<MemberSystemPermissionDTO> systemPermissionDTOListVo = JSON.parseArray(info, MemberSystemPermissionDTO.class);

        Optional<MemberSystemPermissionDTO> first = systemPermissionDTOListVo.stream().filter(x -> Objects.equals(x.getName(), functionGroup)).findFirst();
        if (first.isPresent()) {
            MemberSystemPermissionDTO memberSystemPermissionDTO = first.get();
            Optional<FunctionModelDTO> functionModelDTO = memberSystemPermissionDTO.getFunctionModels().stream().findFirst();
            if (functionModelDTO.isPresent()) {
                FunctionModelDTO functionModelDTO1 = functionModelDTO.get();
                Optional<PermissionModelDTO> permissionModelDTO = functionModelDTO1.getPermissionModels().stream()
                        .filter(x -> Objects.equals(x.getPermissionName(), function)).findFirst();
                return permissionModelDTO.isPresent();
            }
        }
        return false;
    }

    /**
     * 获取应用系统操作权限
     *
     * @param identification 标识符
     * @param userGuid       用户guid
     * @return 当前用户拥有的应用权限
     * @see SystemPermissionEnum
     */
    private List<MemberSystemPermissionDTO> getSystemPermission(String identification, String userGuid) {
        List<MemberSystemPermissionDTO> systemPermissionList = new ArrayList<>();
        log.info("getSystemPermission userGuid value is :{}", userGuid);
        if (StringUtils.isEmpty(userGuid)) {
            HeaderUserInfo userInfo = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryUserInformation(ThreadLocalCache.getEnterpriseGuid());
            userGuid = userInfo.getUserGuid();
        }
        try {
            OperationPermissionQO operationPermissionRequest = new OperationPermissionQO();
            operationPermissionRequest.setIdentifications(Collections.singletonList(identification));
            operationPermissionRequest.setUserId(userGuid);
            operationPermissionRequest.setTeamId(ThreadLocalCache.getEnterpriseGuid());
            log.info("请求参数值operationPermissionRequest:{}", JSON.toJSONString(operationPermissionRequest));
            systemPermissionList = externalSupport.baseServer(ThreadLocalCache.getSystem()).getSystemPermissionList(operationPermissionRequest);

        } catch (Exception e) {
            log.error("Get system permission error :{}", e.getMessage());
        }
        return systemPermissionList;
    }

    /**
     * 更新运营主体选中状态信息状态
     *
     * @param hsaOperSubjectPermissionVOList 更新的运营主体状态信息
     * @return 操作结果
     */
    private boolean updateSubjectInfo(List<HsaOperSubjectPermissionVO> hsaOperSubjectPermissionVOList) {
        Set<String> guidList = hsaOperSubjectPermissionVOList.stream().map(HsaOperSubjectPermissionVO::getId).collect(Collectors.toSet());
        List<HsaSubjectPermission> hsaOperSubjectPermissions = hsaSubjectPermissionMapper.selectList(
                new LambdaQueryWrapper<HsaSubjectPermission>()
                        .in(HsaSubjectPermission::getGuid, guidList));
        if (CollUtil.isEmpty(hsaOperSubjectPermissions)) {
            return false;
        }
        //运营住信息
        Map<String, HsaSubjectPermission> operSubjectPermissionMap = hsaOperSubjectPermissions.stream()
                .collect(Collectors.toMap(HsaSubjectPermission::getGuid, Function.identity(), (entity1, entity2) -> entity1));
        List<String> collect = hsaOperSubjectPermissions.stream().map(HsaSubjectPermission::getGuid).collect(Collectors.toList());
        this.removeByGuids(collect);

        List<HsaSubjectPermission> hsaOperSubjectPermissionList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (HsaOperSubjectPermissionVO operSubjectPermissionVO : hsaOperSubjectPermissionVOList) {
            Integer isChecked = operSubjectPermissionVO.getIs_checked();
            String guid = operSubjectPermissionVO.getId();
            if (operSubjectPermissionMap.containsKey(guid)) {
                HsaSubjectPermission hsaOperSubjectPermission = new HsaSubjectPermission();
                HsaSubjectPermission subjectPermission = operSubjectPermissionMap.get(guid);
                BeanUtils.copyProperties(subjectPermission, hsaOperSubjectPermission);
                hsaOperSubjectPermission.setGmtModified(now);
                hsaOperSubjectPermission.setIsChecked(isChecked);
                hsaOperSubjectPermissionList.add(hsaOperSubjectPermission);
            }
        }
        return this.saveBatch(hsaOperSubjectPermissionList);
    }

    /**
     * 更新运营主体类型状态
     *
     * @param request 请求参数
     * @return 操作结果
     */
    private boolean updateSubjectType(HsaOperSubjectPermissionQO request) {
        HsaOperSubjectPermissionType hsaOperSubjectPermissionType = hsaMallOperSubjectPermissionTypeMapper.selectOne(
                new LambdaQueryWrapper<HsaOperSubjectPermissionType>()
                        .eq(HsaOperSubjectPermissionType::getEnterpriseGuid, request.getTeamId())
                        .eq(HsaOperSubjectPermissionType::getSourceType, request.getSourceType())
                        .eq(HsaOperSubjectPermissionType::getPositionGuid, request.getRoleId())
                        .eq(HsaOperSubjectPermissionType::getIsRole, request.getIsRole()));
        if (Objects.isNull(hsaOperSubjectPermissionType)) {
            return savaSubjectPermissionType(request);
        }
        hsaOperSubjectPermissionType.setIsAll(request.getIsAll());
        hsaOperSubjectPermissionType.setGmtModified(LocalDateTime.now());
        return hsaMallOperSubjectPermissionTypeMapper.updateByGuid(hsaOperSubjectPermissionType);
    }

    /**
     * 保存运营主体权限类型
     *
     * @param request 请求参数
     * @return 操作结果
     */
    private boolean savaSubjectPermissionType(HsaOperSubjectPermissionQO request) {
        HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        String guid = guidGeneratorUtil.getStringGuid(HsaOperSubjectPermissionType.class.getName());
        BeanUtils.copyProperties(request, hsaOperSubjectPermissionType);
        hsaOperSubjectPermissionType.setEnterpriseGuid(String.valueOf(request.getTeamId()));
        hsaOperSubjectPermissionType.setPositionGuid(String.valueOf(request.getRoleId()));
        hsaOperSubjectPermissionType.setGuid(guid);
        int insert = hsaMallOperSubjectPermissionTypeMapper.insert(hsaOperSubjectPermissionType);
        return insert > 0;
    }
}
