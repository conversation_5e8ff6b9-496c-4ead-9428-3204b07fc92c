package com.holderzone.member.mall.controller.pay;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.mall.service.trade.HsaAggTradeService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description 支付服务
 */
@RestController
@RequestMapping("/pay")
@Slf4j
@AllArgsConstructor
public class PayController {

    private final HsaAggTradeService payService;

    @ApiOperation("聚合支付回调")
    @PostMapping(value = "/agg/callback")
    public void aggCallback(@RequestBody AggPayCallbackRspDTO aggPayCallbackDTO) {
        payService.aggPayCallback(aggPayCallbackDTO);
    }

    @ApiOperation("聚合退款")
    @PostMapping(value = "/agg/refund")
    public Result<AggRefundResult> aggRefund(@RequestBody AggRefundDTO refundDTO) {
        return Result.success(payService.aggRefund(refundDTO));
    }

    @ApiOperation("聚合退款回调")
    @PostMapping(value = "/agg/refund/callback")
    public void aggRefundCallback(@RequestBody AggRefundCallbackRespDTO refundRsp) {
        payService.aggRefundCallback(refundRsp);
    }

    @ApiOperation("聚合支付轮询")
    @GetMapping(value = "/agg/polling")
    public Result<AggPayPollingResultDTO> aggPolling(String orderGuid) {
        return Result.success(payService.aggPayPolling(orderGuid));
    }
}
