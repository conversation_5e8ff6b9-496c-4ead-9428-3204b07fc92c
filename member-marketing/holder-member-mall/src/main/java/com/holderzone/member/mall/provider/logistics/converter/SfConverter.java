package com.holderzone.member.mall.provider.logistics.converter;

import com.google.common.collect.Lists;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.sf.domain.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class SfConverter {

    private SfConverter(){
        throw new MemberBaseException();
    }

    public static SfOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        SfOrderCreate.SfOrderCreateBuilder builder = SfOrderCreate.builder();
        builder.orderId(orderCreateDTO.getLogisticsNo());
        //构建请求实体
        return builder.build();
    }

    public static SfOrderCancel orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO){
        SfOrderCancel.SfOrderCancelBuilder builder = SfOrderCancel.builder();
        builder.orderId(orderCancelDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static SfOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        SfOrderTrack.SfOrderTrackBuilder builder = SfOrderTrack.builder();
        builder.trackingNumber(orderTrackDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static SfOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        SfOrderCharge.SfOrderChargeBuilder builder = SfOrderCharge.builder();
        builder.orderid(orderChargeDTO.getOrderNo());
        return builder.build();
    }
}
