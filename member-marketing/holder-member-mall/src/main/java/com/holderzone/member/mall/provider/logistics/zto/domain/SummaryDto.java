package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class SummaryDto {

    /**
     * 到达收取币种
     */
    private String collectMoneyType;

    /**
     * 订单包裹内货物总数量
     */
    private Integer quantity;

    /**
     * 险费（单位：元）
     */
    private BigDecimal premium;

    /**
     * 订单包裹大小（单位：厘米、格式：”长，宽，高”，用半角的逗号来分隔）
     */
    private String size;

    /**
     * 商品总价值（单位：元）
     */
    private BigDecimal price;

    /**
     * 其他费用(单位:元)
     */
    private BigDecimal otherCharges;

    /**
     * 运输费（单位：元）
     */
    private BigDecimal freight;

    /**
     * 包装费(单位:元)
     */
    private BigDecimal packCharges;

    /**
     * 取件开始时间
     */
    private String startTime;

    /**
     * 取件截止时间
     */
    private String endTime;

    /**
     * 保险费
     */
    private BigDecimal orderSum;
}
