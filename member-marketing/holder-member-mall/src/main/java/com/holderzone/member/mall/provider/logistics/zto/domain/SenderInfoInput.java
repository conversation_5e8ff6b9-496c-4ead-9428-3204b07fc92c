package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SenderInfoInput {

    /**
     * 发件人座机（与senderMobile二者不能同时为空）
     */
    private String senderPhone;

    /**
     * 必填
     * 发件人姓名
     */
    private String senderName;

    /**
     * 必填
     * 发件人详细地址
     */
    private String senderAddress;

    /**
     * 必填
     * 发件人区
     */
    private String senderDistrict;
    /**
     * 发件人手机号（与senderPhone二者不能同时为空）
     */
    private String senderMobile;
    /**
     * 必填
     * 发件人省
     */
    private String senderProvince;
    /**
     * 必填
     * 发件人市
     */
    private String senderCity;

}
