package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ItemDto {

    /**
     *物品类型:
     * bm000001 文件
     * bm000002 数码产品
     * bm000003 生活用品
     * bm000004 食品
     * bm000005 服饰
     * bm000006 其他
     * bm000007 生鲜类
     * bm000008 易碎品
     * bm000009 液体
     */
    private String itemType;

    /**
     * 物品名称
     */
    private String itemName;

    /**
     * 物品中文名称
     */
    private String chineseName;

    /**
     * 物品英文名称
     */
    private String englishName;

    /**
     * 件数，≤1
     */
    private int number;

    /**
     * 申报价值(数值型)
     */
    private String itemValue;

    /**
     * 申报货款币别（默认本国币别，如：RMB）
     */
    private String priceCurrency;

    /**
     * 物品描述
     */
    private String desc;

    /**
     * 商品URL
     */
    private String itemUrl;

}
