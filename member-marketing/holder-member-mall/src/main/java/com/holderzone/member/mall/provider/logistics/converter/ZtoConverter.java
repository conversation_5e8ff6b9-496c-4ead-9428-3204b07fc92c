package com.holderzone.member.mall.provider.logistics.converter;

import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.zto.domain.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class ZtoConverter {

    private ZtoConverter(){
        throw new MemberBaseException();
    }

    public static ZtoOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        ZtoOrderCreate.ZtoOrderCreateBuilder builder = ZtoOrderCreate.builder();
        builder.partnerType("2");
        builder.orderType("1");
        builder.partnerOrderCode(orderCreateDTO.getLogisticsNo());
        builder.accountInfo(new AccountDto("test","GPG1576724269",(short) 1));
        SenderInfoInput senderInfoInput = new SenderInfoInput();
        senderInfoInput.setSenderPhone("");
        senderInfoInput.setSenderName(orderCreateDTO.getSenderName());
        senderInfoInput.setSenderAddress(orderCreateDTO.getSenderAddress());
        senderInfoInput.setSenderDistrict(orderCreateDTO.getSenderCountyName());
        senderInfoInput.setSenderMobile(orderCreateDTO.getSenderMobile());
        senderInfoInput.setSenderProvince(orderCreateDTO.getSenderProvinceName());
        senderInfoInput.setSenderCity(orderCreateDTO.getSenderCityName());
        builder.senderInfo(senderInfoInput);
        ReceiveInfoInput receiveInfoInput = new ReceiveInfoInput();
        receiveInfoInput.setReceiverCity(orderCreateDTO.getRecipientCityName());
        receiveInfoInput.setReceiverAddress(orderCreateDTO.getRecipientAddress());
        receiveInfoInput.setReceiverPhone("");
        receiveInfoInput.setReceiverName(orderCreateDTO.getRecipientName());
        receiveInfoInput.setReceiverDistrict(orderCreateDTO.getRecipientCountyName());
        receiveInfoInput.setReceiverMobile(orderCreateDTO.getRecipientMobile());
        receiveInfoInput.setReceiverProvince(orderCreateDTO.getRecipientProvinceName());
        builder.receiveInfo(receiveInfoInput);
        builder.hallCode("");
        /*SummaryDto summaryDto = new SummaryDto();
        summaryDto.setQuantity(3);
        summaryDto.setPremium(BigDecimal.valueOf(10));
        summaryDto.setPrice(BigDecimal.valueOf(30));
        summaryDto.setOtherCharges(BigDecimal.valueOf(20));
        summaryDto.setFreight(BigDecimal.valueOf(20));
        summaryDto.setPackCharges(BigDecimal.valueOf(10));
        summaryDto.setStartTime("2020-12-10 12:00:00");
        summaryDto.setEndTime("2020-12-10 14:00:00");
        summaryDto.setOrderSum(BigDecimal.valueOf(23));
        builder.summaryInfo(summaryDto);*/
        builder.siteCode("02100");
        builder.siteName("上海");
        builder.remark("小吉下单");
        //构建请求实体
        return builder.build();
    }

    public static ZtoOrderCancel orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO){
        ZtoOrderCancel.ZtoOrderCancelBuilder builder = ZtoOrderCancel.builder();
        builder.billCode("73100039415086");
        builder.cancelType("6");
        builder.orderCode(orderCancelDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static ZtoOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        ZtoOrderTrack.ZtoOrderTrackBuilder builder = ZtoOrderTrack.builder();
        builder.billCode(orderTrackDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static ZtoOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        ZtoOrderCharge.ZtoOrderChargeBuilder builder = ZtoOrderCharge.builder();
        //构建请求实体
        builder.transportType(0);
        builder.sender(new AddressInfo(orderChargeDTO.getOrderNo(),"上海","上海市","青浦区"));
        builder.addresser(new AddressInfo("华志路1685号","上海","上海市","青浦区"));
        builder.weight(1.00);
        return builder.build();
    }
}
