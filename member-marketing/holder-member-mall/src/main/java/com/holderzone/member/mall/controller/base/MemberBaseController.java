package com.holderzone.member.mall.controller.base;


import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.StoreBaseInfo;
import com.holderzone.member.common.feign.MemberBaseFeign;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Deprecated
public class MemberBaseController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 获取门店基础数据
     *
     * @param keyword request model
     * @return Result
     */
    @ApiOperation("获取门店基础数据")
    @GetMapping(value = "/hsa-base/getQueryStoreInfo", produces = "application/json;charset=utf-8")
    public Result<List<StoreBaseInfo>> queryStoreInfo(@RequestParam(value = "keyword") String keyword) {
        return memberBaseFeign.queryStoreInfo(keyword);
    }


}
