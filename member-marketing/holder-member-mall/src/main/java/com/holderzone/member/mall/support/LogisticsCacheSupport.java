package com.holderzone.member.mall.support;

import com.google.common.collect.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.logistics.ChargeTypeEnum;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.mall.order.GetFreightAmount;
import com.holderzone.member.common.qo.mall.order.OrderProductAmountQO;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.order.MallOrderFreightAmountVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import com.holderzone.member.mall.service.commodity.HsaDeliverySetService;
import com.holderzone.member.mall.service.logistics.HsaLogisticsChargeService;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import com.holderzone.member.mall.transform.logistics.LogisticsTemplateTransform;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.factory.Mappers;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 维护两个缓存
 * 1.商品缓存
 * map结构
 * key：commodity_code, value:模板guid
 * 2.运费模板缓存
 * string结构
 * key: 模板guid, value：模板详情
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsCacheSupport {

    public static final String LOGISTICS_TEMPLATE_KEY = "LOGISTICS_TEMPLATE:%s:%s";

    private final RedisTemplate<String, String> redisTemplateString;

    private final HsaLogisticsTemplateService logisticsTemplateService;

    private final HsaLogisticsChargeService logisticsChargeService;

    private final MemberCommodityFeign memberCommodityFeign;

    private final HsaDeliverySetService deliverySetService;


    /**
     * 当前地址暂不支持配送，可更换其他地址试试
     */
    public static final BigDecimal UNABLE_LOGISTICS = new BigDecimal("-1");


    /**
     * 商品详情获取运费
     *
     * @param commodityCode 商品编码
     */
    public BigDecimal getFreightAmount(String commodityCode) {
        if (StringUtils.isEmpty(commodityCode)) {
            log.error("commodityCode为空");
            return BigDecimal.ZERO;
        }
        String templateGuid = memberCommodityFeign.getTemplateGuidByCommodityCodeByCache(commodityCode);
        if (StringUtils.isEmpty(templateGuid)) {
            log.error("该商品无运费模板,commodityCode:{}", commodityCode);
            return BigDecimal.ZERO;
        }
        LogisticsTemplateDetailsVO template = getTemplate(templateGuid);
        if (Objects.isNull(template)) {
            log.error("该商品无运费模板,templateGuid:{}", templateGuid);
            return BigDecimal.ZERO;
        }
        ChargeTypeEnum chargeTypeEnum = ChargeTypeEnum.getEnumByCode(template.getChargeType());
        switch (chargeTypeEnum) {
            case UNIFICATION:
                return template.getFreightAmount();
            case NUMBER:
                return byNumber(template.getCharges());
            default:
                log.error("模板计费方式有误,commodityCode:{},template:{}", commodityCode, JacksonUtils.writeValueAsString(template));
                return BigDecimal.ZERO;
        }
    }


    /**
     * 获取下单运费
     * V1 使用
     *
     * @param commodityCode 商品编码
     * @param productNum    商品数量
     * @param province      收货地址 省份
     * @param city          收获地址 市
     * @return 运费  -1：当前地址暂不支持配送，可更换其他地址试试
     */
    public BigDecimal getFreightAmount(String commodityCode, Integer productNum, String province, String city) {
        if (StringUtils.isEmpty(commodityCode)) {
            log.error("commodityCode为空");
            return UNABLE_LOGISTICS;
        }
        if (StringUtils.isEmpty(province) || StringUtils.isEmpty(city)) {
            log.error("收货地址为空,province:{},city:{}", province, city);
            return UNABLE_LOGISTICS;
        }
        String templateGuid = memberCommodityFeign.getTemplateGuidByCommodityCodeByCache(commodityCode);
        if (StringUtils.isEmpty(templateGuid)) {
            log.error("该商品无运费模板,commodityCode:{}", commodityCode);
            return UNABLE_LOGISTICS;
        }
        LogisticsTemplateDetailsVO template = getTemplate(templateGuid);
        if (Objects.isNull(template)) {
            log.error("该商品无运费模板,templateGuid:{}", templateGuid);
            return BigDecimal.ZERO;
        }
        ChargeTypeEnum chargeTypeEnum = ChargeTypeEnum.getEnumByCode(template.getChargeType());
        switch (chargeTypeEnum) {
            case FREE_SHIPPING:
                return BigDecimal.ZERO;
            case UNIFICATION:
                return template.getFreightAmount();
            case NUMBER:
                return byNumber(productNum, province, city, template.getCharges());
            default:
                log.error("模板计费方式有误,template:{}", JacksonUtils.writeValueAsString(template));
                return UNABLE_LOGISTICS;
        }
    }

    /**
     * 获取下单运费 V2
     */
    public MallOrderFreightAmountVO getOrderFreightAmount(GetFreightAmount orderInfo) {
        String province = orderInfo.getProvince();
        String city = orderInfo.getCity();
        List<OrderProductAmountQO> products = orderInfo.getOrderProductAmountQOS();
        if (CollectionUtils.isEmpty(products)) {
            log.error("商品明细为空,orderInfo:{}", JacksonUtils.writeValueAsString(orderInfo));
            return new MallOrderFreightAmountVO().setFreightAmount(UNABLE_LOGISTICS);
        }
        if (StringUtils.isEmpty(province) || StringUtils.isEmpty(city)) {
            log.error("收货地址为空,province:{},city:{}", province, city);
            return new MallOrderFreightAmountVO().setFreightAmount(UNABLE_LOGISTICS);
        }
        List<String> commodityCodes = products.stream()
                .map(OrderProductAmountQO::getProductCode)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> templateGuidMap = memberCommodityFeign.getTemplateGuidsByCommodityCodesByCache(commodityCodes);
        return batchGetFreightAmount(orderInfo, templateGuidMap);
    }

    /**
     * 多菜品 批量获取运费价格
     */
    private MallOrderFreightAmountVO batchGetFreightAmount(GetFreightAmount orderInfo,
                                                           Map<String, String> templateGuidMap) {
        String province = orderInfo.getProvince();
        String city = orderInfo.getCity();
        List<OrderProductAmountQO> products = orderInfo.getOrderProductAmountQOS();
        if (MapUtils.isEmpty(templateGuidMap)) {
            log.error("模板计费方式有误,orderInfo:{}", JacksonUtils.writeValueAsString(orderInfo));
            return new MallOrderFreightAmountVO().setFreightAmount(UNABLE_LOGISTICS);
        }
        // 获取运费计算配置
        DeliverySetVO deliverySet = deliverySetService.getDeliverySet(ThreadLocalCache.getOperSubjectGuid());
        Integer freightMode = Optional.ofNullable(deliverySet.getFreightMode()).orElse(0);
        List<String> unCommodityCodes = Lists.newArrayList();
        BigDecimal freightAmount = BigDecimal.ZERO;
        for (OrderProductAmountQO product : products) {
            String templateGuid = templateGuidMap.get(product.getProductCode());
            LogisticsTemplateDetailsVO template = getTemplate(templateGuid);
            if (Objects.isNull(template)) {
                log.error("该商品无运费模板,templateGuid:{},product:{}", templateGuid, JacksonUtils.writeValueAsString(product));
                unCommodityCodes.add(product.getProductCode());
                continue;
            }
            ChargeTypeEnum chargeTypeEnum = ChargeTypeEnum.getEnumByCode(template.getChargeType());
            switch (chargeTypeEnum) {
                case FREE_SHIPPING:
                    break;
                case UNIFICATION:
                    freightAmount = getFreightAmountByMode(freightMode, freightAmount, template.getFreightAmount());
                    break;
                case NUMBER:
                    BigDecimal byNumberAmount = byNumber(product.getProductNum(), province, city, template.getCharges());
                    if (byNumberAmount.compareTo(UNABLE_LOGISTICS) == 0) {
                        unCommodityCodes.add(product.getProductCode());
                    } else {
                        freightAmount = getFreightAmountByMode(freightMode, freightAmount, byNumberAmount);
                    }
                    break;
                default:
                    unCommodityCodes.add(product.getProductCode());
            }
        }
        if (CollectionUtils.isNotEmpty(unCommodityCodes)) {
            freightAmount = UNABLE_LOGISTICS;
        }
        return new MallOrderFreightAmountVO()
                .setFreightAmount(freightAmount)
                .setUnCommodityCodes(unCommodityCodes.stream().distinct().collect(Collectors.toList()));
    }

    private BigDecimal getFreightAmountByMode(Integer freightMode, BigDecimal freightAmount,
                                              BigDecimal existFreightAmount) {
        if (freightMode == 0) {
            // 最大
            return existFreightAmount.compareTo(freightAmount) > 0 ? existFreightAmount : freightAmount;
        } else {
            // 累加
            return existFreightAmount.add(freightAmount);
        }
    }


    /**
     * 按件数
     * 直接返回第一条计费规则的运费
     */
    private BigDecimal byNumber(List<LogisticsTemplateDetailsVO.InnerCharge> charges) {
        if (CollectionUtils.isEmpty(charges)) {
            return BigDecimal.ZERO;
        }
        return charges.get(0).getPrice();
    }

    /**
     * 按件数
     * 根据下单地址 下单件数 计算运费
     */
    private BigDecimal byNumber(Integer productNum, String province, String city,
                                List<LogisticsTemplateDetailsVO.InnerCharge> charges) {
        if (CollectionUtils.isEmpty(charges)) {
            return UNABLE_LOGISTICS;
        }
        for (LogisticsTemplateDetailsVO.InnerCharge charge : charges) {
            // regions = '四川省:自贡市,攀枝花市,泸州市-甘肃省'
            String regions = charge.getRegion();
            if (isExistRegion(regions, province, city)) {
                return calFreightAmount(charge, productNum);
            }
        }
        return UNABLE_LOGISTICS;
    }

    /**
     * 判斷下單地址是否在可配送范围内
     *
     * @param regions  配置的可配送范围
     * @param province 省
     * @param city     市
     * @return 1是 0否
     */
    private boolean isExistRegion(String regions, String province, String city) {
        String[] regionsArr = regions.split("-");
        for (String region : regionsArr) {
            // region = '四川省:自贡市,攀枝花市,泸州市'
            String[] regionArr = region.split(":");
            // regionArr[0] = '四川省' , regionArr[1] = '自贡市,攀枝花市,泸州市'
            if (!province.equals(regionArr[0])) {
                continue;
            }
            if (regionArr.length == 1) {
                return true;
            }
            List<String> cities = Arrays.stream(regionArr[1].split(",")).collect(Collectors.toList());
            if (cities.contains(city)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算运费
     * 适配的运费模板明细 + 商品数量 计算得出所需运费
     */
    private BigDecimal calFreightAmount(LogisticsTemplateDetailsVO.InnerCharge charge, Integer productNum) {
        BigDecimal freightAmount = charge.getPrice();
        BigDecimal number = new BigDecimal(productNum);
        if (number.compareTo(charge.getWeight()) > 0) {
            BigDecimal secondNumber = number.subtract(charge.getWeight());
            return freightAmount.add(secondNumber.divide(charge.getSecondWeight(), 0, RoundingMode.UP)
                    .multiply(charge.getSecondPrice()));
        }
        return freightAmount;
    }


    /**
     * 根据模板guid 获取模板信息
     */
    private LogisticsTemplateDetailsVO getTemplate(String templateGuid) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String redisKey = String.format(LOGISTICS_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        if (Boolean.TRUE.equals(redisTemplateString.hasKey(redisKey))) {
            String templateString = redisTemplateString.opsForValue().get(redisKey);
            if (!StringUtils.isEmpty(templateString)) {
                return JacksonUtils.toObject(LogisticsTemplateDetailsVO.class, templateString);
            }
            log.error("运费模板缓存有误,redisKey:{},", redisKey);
            return null;
        }
        // 查询db
        LogisticsTemplateDetailsVO detailsVO = new LogisticsTemplateDetailsVO();
        HsaLogisticsTemplate template = logisticsTemplateService.getById(templateGuid);
        if (Objects.isNull(template)) {
            return null;
        }
        detailsVO.setGuid(template.getGuid());
        detailsVO.setTemplateName(template.getTemplateName());
        detailsVO.setDefaultFlag(template.getDefaultFlag());
        detailsVO.setFreightAmount(template.getFreightAmount());
        detailsVO.setChargeType(template.getChargeType());
        if (detailsVO.getChargeType() > ChargeTypeEnum.FREE_SHIPPING.getCode()) {
            // 查询计费明细
            List<HsaLogisticsCharge> byTemplateGuid = logisticsChargeService.findByTemplateGuid(templateGuid);
            if (CollectionUtils.isNotEmpty(byTemplateGuid)) {
                detailsVO.setCharges(Mappers.getMapper(LogisticsTemplateTransform.class).chargeDOList2DetailsVOList(byTemplateGuid));
                detailsVO.getCharges().forEach(e -> e.setRegions(Arrays.stream(e.getRegion().split("-")).collect(Collectors.toList())));
            }
        }
        setTemplate(detailsVO);
        return detailsVO;
    }

    /**
     * 更新模板
     */
    public void setTemplate(LogisticsTemplateDetailsVO detailsVO) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String templateGuid = detailsVO.getGuid();
        String redisKey = String.format(LOGISTICS_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        redisTemplateString.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(detailsVO), 1, TimeUnit.DAYS);
    }

    /**
     * 删除模板
     */
    public void removeTemplate(String templateGuid) {
        String operSubjectGuid = ThreadLocalCache.getOperSubjectGuid();
        String redisKey = String.format(LOGISTICS_TEMPLATE_KEY, operSubjectGuid, templateGuid);
        redisTemplateString.delete(redisKey);
    }
}
