package com.holderzone.member.mall.transform.logistics;

import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper
public interface LogisticsTemplateTransform {

    LogisticsTemplateDetailsVO.InnerCharge chargeDO2DetailsVO(HsaLogisticsCharge charge);

    List<LogisticsTemplateDetailsVO.InnerCharge> chargeDOList2DetailsVOList(List<HsaLogisticsCharge> charges);

    LogisticsTemplateVO templateDO2TemplateVO(HsaLogisticsTemplate template);

    List<LogisticsTemplateVO> templateDOList2TemplateVOList(List<HsaLogisticsTemplate> templates);
}
