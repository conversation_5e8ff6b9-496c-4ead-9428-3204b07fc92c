package com.holderzone.member.mall.service.commodity.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.label.LabelAreaJson;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.qo.mall.DistributionSetDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.util.page.PageUtil;
import com.holderzone.member.mall.entity.commodity.HsaDistributionSet;
import com.holderzone.member.mall.mapper.commodity.HsaDistributionSetMapper;
import com.holderzone.member.mall.service.commodity.HsaDistributionSetService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-08-30 18:00
 */
@Service
public class HsaDistributionSetServiceImpl extends HolderBaseServiceImpl<HsaDistributionSetMapper, HsaDistributionSet> implements HsaDistributionSetService {

    @Resource
    private HsaDistributionSetMapper hsaDistributionSetMapper;

    @Resource
    private GuidGeneratorUtil guidGeneratorUtil;

    /**
     * 英文逗号
     */
    private static final String COMMA = ",";

    @Override
    public boolean saveOrUpdateDistributionSet(DistributionSetDTO request) {
        HsaDistributionSet hsaDistributionSet;
        if (!StringUtils.isEmpty(request.getGuid())) {
            hsaDistributionSet = hsaDistributionSetMapper.queryByGuid(request.getGuid());
        } else {
            hsaDistributionSet = new HsaDistributionSet();
            hsaDistributionSet.setGuid(guidGeneratorUtil.getStringGuid(HsaDistributionSet.class.getSimpleName()));
            hsaDistributionSet.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
            hsaDistributionSet.setGmtCreate(LocalDateTime.now());
        }
        hsaDistributionSet.setLinkman(request.getLinkman());
        hsaDistributionSet.setLinkmanPhone(request.getLinkmanPhone());
        hsaDistributionSet.setAreaJson(JSONObject.toJSONString(request.getAreaJson()));
        hsaDistributionSet.setDetailAddress(request.getDetailAddress());
        hsaDistributionSet.setSendGoodsChoose(request.getSendGoodsChoose());
        hsaDistributionSet.setSendGoodsDefault(request.getSendGoodsDefault());
        hsaDistributionSet.setReceiveGoodsChoose(request.getReceiveGoodsChoose());
        hsaDistributionSet.setReceiveGoodsDefault(request.getReceiveGoodsDefault());
        hsaDistributionSet.setGmtModified(LocalDateTime.now());
        //如果设置默认发货地址，将其他发货地址设为非默认
        if (Objects.nonNull(request.getSendGoodsDefault()) && request.getSendGoodsDefault() == BooleanEnum.TRUE.getCode()) {
            hsaDistributionSetMapper.updateSendGoodsDefaultStatus(ThreadLocalCache.getOperSubjectGuid());
        }
        //如果设置默认收货地址，将其他收获地址设为非默认
        if (Objects.nonNull(request.getReceiveGoodsDefault()) && request.getReceiveGoodsDefault() == BooleanEnum.TRUE.getCode()) {
            hsaDistributionSetMapper.updateReceiveGoodsDefaultStatus(ThreadLocalCache.getOperSubjectGuid());
        }
        this.saveOrUpdate(hsaDistributionSet);
        return true;
    }

    @Override
    public DistributionSetDTO queryDistributionSetDetail(String guid) {
        if (StringUtils.isEmpty(guid)) {
            throw new MallBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        HsaDistributionSet hsaDistributionSet = hsaDistributionSetMapper.queryByGuid(guid);

        if (Objects.isNull(hsaDistributionSet)) {
            throw new MallBaseException(MemberAccountExceptionEnum.DISTRIBUTION_SET_NOT_EXIST);
        }
        return toDistributionSetDTO(hsaDistributionSet);
    }

    @Override
    public PageResult queryDistributionSetList(PageDTO request) {
        PageHelper.startPage(request.getCurrentPage(), request.getPageSize());
        List<HsaDistributionSet> list = hsaDistributionSetMapper.selectList(new LambdaQueryWrapper<HsaDistributionSet>()
                .eq(HsaDistributionSet::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .orderByDesc(HsaDistributionSet::getSendGoodsDefault));
        if (CollectionUtil.isEmpty(list)) {
            return PageUtil.getPageResult(new PageInfo<>(list));
        }
        List<DistributionSetDTO> distributionSetDTOS = new ArrayList<>();
        for (HsaDistributionSet hsaDistributionSet : list) {
            distributionSetDTOS.add(toDistributionSetDTO(hsaDistributionSet));
        }
        return PageUtil.getPageResult(new PageInfo<>(distributionSetDTOS));
    }

    @Override
    public boolean deleteDistributionSet(String guid) {

        if (StringUtils.isEmpty(guid)) {
            throw new MallBaseException(MemberAccountExceptionEnum.ERROR_VERIFY);
        }
        hsaDistributionSetMapper.delete(new LambdaQueryWrapper<HsaDistributionSet>()
                .eq(HsaDistributionSet::getGuid, guid));
        return true;
    }

    private DistributionSetDTO toDistributionSetDTO(HsaDistributionSet hsaDistributionSet) {
        DistributionSetDTO distributionSetDTO = new DistributionSetDTO();
        distributionSetDTO.setGuid(hsaDistributionSet.getGuid());
        distributionSetDTO.setLinkman(hsaDistributionSet.getLinkman());
        distributionSetDTO.setLinkmanPhone(hsaDistributionSet.getLinkmanPhone());
        distributionSetDTO.setAreaJson(JSONObject.parseObject(hsaDistributionSet.getAreaJson(), LabelAreaJson.class));
        distributionSetDTO.setDetailAddress(hsaDistributionSet.getDetailAddress());
        distributionSetDTO.setSendGoodsChoose(hsaDistributionSet.getSendGoodsChoose());
        distributionSetDTO.setSendGoodsDefault(hsaDistributionSet.getSendGoodsDefault());
        distributionSetDTO.setReceiveGoodsChoose(hsaDistributionSet.getReceiveGoodsChoose());
        distributionSetDTO.setReceiveGoodsDefault(hsaDistributionSet.getReceiveGoodsDefault());
        return distributionSetDTO;
    }


}
