package com.holderzone.member.mall.provider.logistics.jt.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TrackDetailRsp {

    /**
     * 扫描时间
     */
    private String scanTime;

    /**
     * 轨迹描述
     */
    private String desc;

    /**
     * 扫描类型
     * 1、快件揽收
     * 2、入仓扫描（停用）
     * 3、发件扫描
     * 4、到件扫描
     * 5、出仓扫描
     * 6、入库扫描
     * 7、代理点收入扫描
     * 8、快件取出扫描
     * 9、出库扫描
     * 10、快件签收
     * 11、问题件扫描
     */
    private String scanType;

    /**
     * A1、客户取消寄件-网点A2、客户拒收A3、更改派送地址A4、退回件-网点A5、疫情退回-网点A6、疫情延迟
     * A7、疫情延迟A8、包裹异常-网点A9、收件人联系不上A10、收件人联系不上 A11、多次派件失败 A12、收件人信息错误
     * A13、更改派送时间A14、客户拒收A15、收件地址不详A16、收件地址错误A17、包裹存放至网点 A18、包裹存放至网点
     * A19、收件地址禁止 A20、包裹异常A21、包裹暂存网点A22、疫情退回-中心A23、包裹延迟-节假日A24、包裹异常-中心
     * A25、退回件-中心 A26、客户取消寄件-中心
     */
    private String problemType;

    /**
     * 扫描网点名称
     */
    private String scanNetworkName;

    /**
     * 扫描网点ID
     */
    private String scanNetworkId;

    /**
     * 业务员姓名
     */
    private String staffName;

    /**
     * 业务员联系方式
     */
    private String staffContact;

    /**
     * 扫描网点联系方式
     */
    private String scanNetworkContact;

    /**
     * 扫描网点省份
     */
    private String scanNetworkProvince;

    /**
     * 扫描网点城市
     */
    private String scanNetworkCity;

    /**
     * 扫描网点区/县
     */
    private String scanNetworkArea;

    /**
     * 上一站(到件)或下一站名称(发件)
     */
    private String nextStopName;

    /**
     * 下一站省份（发件扫描类型时提供）
     */
    private String nextNetworkProvinceName;

    /**
     * 下一站城市（发件扫描类型时提供）
     */
    private String nextNetworkCityName;

    /**
     * 下一站区/县（发件扫描类型时提供）
     */
    private String nextNetworkAreaName;
}
