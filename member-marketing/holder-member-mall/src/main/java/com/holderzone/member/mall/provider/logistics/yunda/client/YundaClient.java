package com.holderzone.member.mall.provider.logistics.yunda.client;

import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.exception.MemberBaseException;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
public class YundaClient {

    private YundaClient(){
        throw new MemberBaseException();
    }

    public static String doPostJson(String apiUrl, String jsonParams, String appKey, String appSecret) {
        StringBuilder sbResult = new StringBuilder();

        try {
            URL url = new URL(apiUrl);
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("Charset",StandardCharsets.UTF_8.toString());
            byte[] data = jsonParams.getBytes(StandardCharsets.UTF_8);
            conn.setRequestProperty("Content-Length", String.valueOf(data.length));
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("app-key", appKey);
            conn.setRequestProperty("req-time", String.valueOf(System.currentTimeMillis()));
            conn.setRequestProperty("sign", MD5(jsonParams + "_" + appSecret, StandardCharsets.UTF_8.toString()));
            conn.connect();
            OutputStream out = new DataOutputStream(conn.getOutputStream());
            out.write(data);
            out.flush();
            out.close();
            if (200 == conn.getResponseCode()) {
                InputStream inputStream = conn.getInputStream();

                String readLine;
                BufferedReader responseReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                while((readLine = responseReader.readLine()) != null) {
                    sbResult.append(readLine).append("\n");
                }
                responseReader.close();

            }
        } catch (Exception var13) {
            var13.printStackTrace();
        }

        return sbResult.toString();
    }

    private static String MD5(String str, String charset) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(str.getBytes(charset));
            byte[] result = md.digest();
            StringBuilder sb = new StringBuilder(32);

            for (byte b : result) {
                int val = b & 255;
                if (val <= 15) {
                    sb.append("0");
                }

                sb.append(Integer.toHexString(val));
            }

            return sb.toString().toLowerCase();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
            throw new MallBaseException();
        }

    }
}
