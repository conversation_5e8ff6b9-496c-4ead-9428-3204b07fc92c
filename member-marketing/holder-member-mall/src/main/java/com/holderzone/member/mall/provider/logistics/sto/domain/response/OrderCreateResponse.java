package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderCreateResponse implements Serializable {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 大字/三段码
     */
    private String bigWord;

    /**
     * 集包地
     */
    private String packagePlace;

    /**
     * 客户订单号（调度订单时返回客户订单号，非调度订单不返回该值）
     */
    private String sourceOrderId;

    /**
     * 安全号码
     */
    private String safeNo;


    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCreateResponse buildCommonResponse(String rsp){
        StoBaseResponse<OrderCreateResponse> stoBaseResponse = JSONObject.parseObject(rsp, new TypeReference<StoBaseResponse<OrderCreateResponse>>(){}.getType());

        if(stoBaseResponse == null || stoBaseResponse.getData() == null){
            return null;
        }
        OrderCreateResponse orderCreateResponse = stoBaseResponse.getData();
        //TODO
        return LogisticsOrderCreateResponse.builder().courierNumber(orderCreateResponse.getWaybillNo()).build();
    }

}
