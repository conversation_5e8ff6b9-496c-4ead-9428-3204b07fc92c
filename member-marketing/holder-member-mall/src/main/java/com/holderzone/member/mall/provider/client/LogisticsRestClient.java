package com.holderzone.member.mall.provider.client;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.common.enums.mall.MemberMallExceptionEnum;
import com.holderzone.member.common.exception.MallBaseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * 请求物流公司接口远程调用
 */
@Component
@Slf4j
@AllArgsConstructor
public class LogisticsRestClient {

    @Qualifier("remoteRestTemplate")
    private final RestTemplate restTemplate;

    public String post(String jsonData,String url){
        HttpHeaders headers = new HttpHeaders();
        MediaType mediaType = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(mediaType);
        HttpEntity<String> entity = new HttpEntity<>(jsonData, headers);
        ResponseEntity<String> stringResponseEntity = null;
        try {
            stringResponseEntity = restTemplate.postForEntity(url, entity, String.class);
        }catch (HttpClientErrorException e){
            log.info("物流请求请求异常：{}",e.getResponseBodyAsString());
        }
        log.info("物流返回参数信息：{}",stringResponseEntity);
        if(ObjectUtil.isNull(stringResponseEntity)){
            throw new MallBaseException(MemberMallExceptionEnum.REQUEST_REMOTE_ERROR);
        }
        //响应状态
        HttpStatus statusCode = stringResponseEntity.getStatusCode();
        if(statusCode != HttpStatus.OK){
            throw new MallBaseException(MemberMallExceptionEnum.REQUEST_REMOTE_ERROR);
        }
        return stringResponseEntity.getBody();
    }

    public String postForMap(MultiValueMap<String, Object> paramsMap, String url){
        try {
            return restTemplate.postForObject(url, paramsMap, String.class);
        }catch (HttpClientErrorException e){
            log.info("物流请求请求异常：{}",e.getResponseBodyAsString());
        }
        throw new MallBaseException(MemberMallExceptionEnum.REQUEST_REMOTE_ERROR);
    }

}
