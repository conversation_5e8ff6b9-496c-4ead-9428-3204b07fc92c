package com.holderzone.member.mall.controller.logistics;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.common.enums.mall.order.OrderConditionEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.mall.entity.order.HsaMallBaseOrder;
import com.holderzone.member.mall.service.commodity.HsaDeliverySetService;
import com.holderzone.member.mall.service.order.HsaMallBaseOrderService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/delivery_set")
public class DeliverySetController {

    @Resource
    private HsaDeliverySetService hsaDeliverySetService;

    @Resource
    private HsaMallBaseOrderService mallBaseOrderService;

    /**
     * 通过运营主体获取商品配送基础设置
     *
     * @param operSubjectGuid 运营主体
     * @return 配送设置
     */
    @GetMapping("/get_delivery_set")
    public Result<DeliverySetVO> getDeliverySet(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        return Result.success(hsaDeliverySetService.getDeliverySet(operSubjectGuid));
    }

    /**
     * 修改商品配送设置
     *
     * @param deliverySet 商品配送基础参数
     * @return 操作结果
     */
    @PostMapping("/update_delivery_set")
    public Result<Boolean> updateDeliverySet(@RequestBody DeliverySetVO deliverySet) {
        //关闭发货功能
        if (deliverySet.getIsExpressDelivery() == 0) {
            //判断是否有订单是否在发货中
            int count = mallBaseOrderService.count(new LambdaQueryWrapper<HsaMallBaseOrder>()
                    .eq(HsaMallBaseOrder::getOrderCondition, OrderConditionEnum.TREAT_RECEIPT.getCode())
                    .eq(HsaMallBaseOrder::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid()));
            if (count > 0) {
                throw new MallBaseException(MemberAccountExceptionEnum.CLOSE_ERROR);
            }
        }
        return Result.success(hsaDeliverySetService.updateDeliverySet(deliverySet));
    }

    @PostMapping("/update_express_state")
    public Result<Void> updateExpressState(@RequestBody DeliverySetVO vo) {
        hsaDeliverySetService.updateExpressState(vo);
        return Result.success();
    }

    /**
     * 获取所有快递类型
     *
     * @return 快递类型集合
     */
    @GetMapping("/get_all_express_type")
    public Result<List<String>> getAllExpressType() {
        return Result.success(ExpressTypeEnum.getAllExpressType());
    }

}
