package com.holderzone.member.mall.provider.logistics.yto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderCreateResponse implements Serializable {

    /**
     * 客户编码（K开头）
     */
    private String customerCode;

    /**
     * 物流单号，打印拉取运单号前，物流单号和渠道唯一确定一笔快递物流订单
     */
    private String logisticsNo;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 三段码
     */
    private String shortAddress;

    /**
     * 面单打印，脱敏字段及对应值列表
     */
    private List<SecretWaybillRo> secretWaybills;

    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCreateResponse buildCommonResponse(String json){
        OrderCreateResponse orderCreateResponse = JSONObject.parseObject(json, OrderCreateResponse.class);
        if(orderCreateResponse == null){
            return null;
        }
        //TODO
        return LogisticsOrderCreateResponse.builder().courierNumber(orderCreateResponse.getMailNo()).build();
    }

}
