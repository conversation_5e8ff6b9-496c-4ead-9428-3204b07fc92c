package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderCancelResponse implements Serializable {

    /**
     * 客户订单号
     */
    private String orderId;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorMessage;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 返回数据
     */
    private String obj;




    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCancelResponse buildCommonResponse(String rsp){
        SfBaseResponse sfBaseResponse = JSONObject.parseObject(rsp, SfBaseResponse.class);
        if(sfBaseResponse == null || sfBaseResponse.getApiResultData() == null){
            return null;
        }
        OrderCancelResponse orderCancelResponse = JSONObject.parseObject(sfBaseResponse.getApiResultData(), OrderCancelResponse.class);
        if(orderCancelResponse == null){
            return null;
        }
        return LogisticsOrderCancelResponse.builder().build();
    }

}
