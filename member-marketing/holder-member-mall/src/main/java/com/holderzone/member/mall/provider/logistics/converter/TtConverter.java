package com.holderzone.member.mall.provider.logistics.converter;

import com.google.common.collect.Lists;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderBase;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderCharge;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderCreate;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TtConverter {

    private TtConverter(){
        throw new MemberBaseException();
    }

    public static TtOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        TtOrderCreate.TtOrderCreateBuilder builder = TtOrderCreate.builder();
        builder.tracking_number(orderCreateDTO.getLogisticsNo());
        builder.courier_code("cainiao");
        builder.order_number("#1234");
        builder.destination_code("LV");
        builder.customer_name("test");
        builder.customer_email("<EMAIL>");
        builder.customer_phone("+**********");
        builder.note("check");
        builder.title("title");
        builder.lang("cn");
        //构建请求实体
        return builder.build();
    }

    public static List<TtOrderBase> orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO){

        TtOrderBase ttOrderBase = new TtOrderBase();
        ttOrderBase.setCourier_code("cainiao");
        ttOrderBase.setTracking_number(orderCancelDTO.getOrderNo());
        //构建请求实体
        return Lists.newArrayList(ttOrderBase);
    }

    public static String orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        String requestData = "?tracking_numbers=UB209300714LV,LX123456789CN";
        //构建请求实体
        return requestData + orderTrackDTO.getOrderNo();
    }

    public static TtOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        TtOrderCharge.TtOrderChargeBuilder builder = TtOrderCharge.builder();
        orderChargeDTO.setOrderNo("LX123456789CN");
        return builder.build();
    }
}
