package com.holderzone.member.mall.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.crm.CrmBindAppletDTO;
import com.holderzone.member.common.dto.crm.CrmCommodityDTO;
import com.holderzone.member.mall.manage.CommonManage;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022年12月30日 下午4:21
 * @description 公共接口控制器
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common")
public class CommonController {

    private CommonManage commonManage;

    @ApiOperation("获取商品地址")
    @PostMapping("/get_commodity_url")
    public Result<String> getCommodityUrl(@RequestBody CrmCommodityDTO commodityDTO) {
        return Result.success(commonManage.getCommodityUrl(commodityDTO));
    }
}
