package com.holderzone.member.mall.provider.logistics.sf.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class Cargo {

    /**
     * 货物名称
     */
    private String name;

    /**
     * 单位， 如个、台、件
     */
    private String unit;

    /**
     * 货物的分类
     */
    private String category;

    /**
     * 规格
     */
    private String spec;

    /**
     * count
     */
    private int count;

    /**
     * 长 单位默认为厘米
     */
    private BigDecimal length;

    /**
     * 高 单位默认为厘米
     */
    private BigDecimal height;
    /**
     * 宽 单位默认为厘米
     */
    private BigDecimal width;
    /**
     * 体积 单位默认为立方厘米
     */
    private BigDecimal volume;
    /**
     * 重量 单位:kg
     */
    private BigDecimal weight;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 国条码
     */
    private String stateBarCode;

    /**
     * 箱号
     */
    private String boxNo;

    /**
     * 验货服务SN码
     */
    private String snCode;
}
