package com.holderzone.member.mall.provider.logistics.zto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeResponse;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderChargeResponse {

    /**
     * 运费首重重量（单位：千克），保留两位小数
     */
    private double firstWeight;

    /**
     * 运费续重总价（单位：元），保留两位小数
     */
    private double additionalWeightTotalPrice;

    /**
     * 运费首重单价（单位：元），保留两位小数
     */
    private double firstWeightPrice;

    /**
     * 分钟
     */
    private double minute;
    /**
     * 运费续重重量（单位：千克），保留两位小数
     */
    private double additionalWeight;
    /**
     * 运费续重单价（单位：元），保留两位小数
     */
    private double additionalWeightPrice;
    /**
     * 小时
     */
    private double hour;

    /**
     * 总价（单位：元），保留两位小数
     */
    private double price;

    /**
     * 预计到件时间
     */
    private String receivedTime;

    /**
     * 时效描述
     */
    private String timeDesc;

    /**
     * 代收货款价格（单位：元），保留两位小数
     */
    private double codPrice;

    /**
     * 保价价格（单位：元），保留两位小数
     */
    private double insPrice;

    /**
     * 运费价格（单位：元），保留两位小数
     */
    private double freightPrice;

    /**
     * 天
     */
    private double day;

    public static LogisticsOrderChargeResponse buildCommonResponse(String json){
        ZtoBaseRsp<OrderChargeResponse> rsp = JSONObject.parseObject(json, new TypeReference<ZtoBaseRsp<OrderChargeResponse>>() {
        }.getType());

        if(rsp == null || rsp.getResult() == null) {
            return null;
        }
        //TODO
        return LogisticsOrderChargeResponse.builder().build();

    }
}
