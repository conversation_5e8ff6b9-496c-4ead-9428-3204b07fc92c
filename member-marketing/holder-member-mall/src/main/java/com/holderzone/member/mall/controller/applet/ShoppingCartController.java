package com.holderzone.member.mall.controller.applet;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.mall.shoppingcart.*;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.exception.CommodityExceptionEnum;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.commodity.CommodityDetailConditionQO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartVO;
import com.holderzone.member.mall.manage.ShoppingCartManage;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年03月06日 上午11:22
 * @description 购物车控制器
 */
@Slf4j
@RestController
@RequestMapping("/shopping_cart")
@AllArgsConstructor
public class ShoppingCartController {

    private ShoppingCartManage shoppingCartManage;

    @ApiOperation("增加商品")
    @PostMapping("/add")
    public Result<Void> add(@RequestBody @Validated ShoppingCartAddDTO addDTO) {
        if (CommodityExceptionEnum.COMMODITY_DOWN.getCode() == shoppingCartManage.add(addDTO)) {
            return Result.error(0, CommodityExceptionEnum.COMMODITY_DOWN.getDes());
        }
        return Result.success();
    }

    @ApiOperation("修改商品")
    @PostMapping("/edit")
    public Result<Void> edit(@RequestBody @Validated ShoppingCartEditDTO editDTO) {
        shoppingCartManage.edit(editDTO);
        return Result.success();
    }

    @ApiOperation("查询商品")
    @GetMapping("/query")
    public Result<ShoppingCartVO> query(@RequestParam(value = "memberInfoGuid",required = false) String memberInfoGuid) {
        return Result.success(shoppingCartManage.query(memberInfoGuid));
    }

    @ApiOperation("批量删除商品")
    @PostMapping("/batch_delete")
    public Result<Void> batchDelete(@RequestBody SingleDataDTO request) {
        shoppingCartManage.batchDelete(request);
        return Result.success();
    }

    @ApiOperation("商品数量变动")
    @PostMapping("/change_num")
    public Result<Void> changeNum(@RequestBody ShoppingCartNumChangeDTO request) {
        shoppingCartManage.changeNum(request);
        return Result.success();
    }

    @ApiOperation("清空购物车")
    @GetMapping("/clean")
    public Result<Void> clean() {
        shoppingCartManage.clean();
        return Result.success();
    }

    @ApiOperation("清除失效商品")
    @GetMapping("/clean_invalid")
    public Result<Void> cleanInvalid(@RequestParam("memberInfoGuid") String memberInfoGuid) {
        shoppingCartManage.cleanInvalid(memberInfoGuid);
        return Result.success();
    }

    @ApiOperation("推荐商品列表")
    @PostMapping("/list_recommend_commodity")
    public Result<PageResult> listRecommendCommodity(@RequestBody CommodityConditionQO request) {
        return Result.success(shoppingCartManage.listRecommendCommodity(request));
    }

    @ApiOperation("购物车商品详情")
    @GetMapping("/commodity_detail/{shoppingCartGuid}")
    public Result<CartCommodityDetailDTO> commodityDetail(@PathVariable String shoppingCartGuid) {
        return Result.success(shoppingCartManage.commodityDetail(shoppingCartGuid));
    }

    @ApiOperation("购物车结算校验")
    @PostMapping("/settle_check")
    public Result<SettleCheckDTO> toSettleCheck(@RequestBody Map<String,List<String>> shoppingCartGuidMap) {
        return Result.success(shoppingCartManage.toSettleCheck(shoppingCartGuidMap.get("list")));
    }

    @ApiOperation("商品详情")
    @PostMapping("/commodity/detail")
    public Result<CartCommodityDetailDTO> commodityDetail(@RequestBody CommodityDetailConditionQO commodityDetailConditionQO){
        return Result.success(shoppingCartManage.commodityDetail(commodityDetailConditionQO));
    }

}