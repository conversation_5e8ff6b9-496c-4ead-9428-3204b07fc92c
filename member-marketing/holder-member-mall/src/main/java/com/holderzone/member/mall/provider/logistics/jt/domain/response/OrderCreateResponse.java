package com.holderzone.member.mall.provider.logistics.jt.domain.response;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderCreateResponse implements Serializable {

    /**
     * 集包地
     */
    private String lastCenterName;

    /**
     * 返回客户订单号。
     */
    private String txlogisticId;

    /**
     * 订单创建时间 yyyy-MM-dd HH:mm:ss
     */
    private String createOrderTime;

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 三段码（获取到三段码优先返回三段码，无三段码返回大头笔）
     */
    private String sortingCode;

    /**
     * 参考总运费（数值型）
     */
    private String sumFreight;

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCreateResponse buildCommonResponse(String rsp){
        try {
            JtBaseResponse<OrderCreateResponse> jtBaseResponse = JSONObject.parseObject(rsp, new TypeReference<JtBaseResponse<OrderCreateResponse>>() {
            }.getType());
            if(jtBaseResponse == null || jtBaseResponse.getData() == null){
                return null;
            }
            return LogisticsOrderCreateResponse.builder().build();
        }catch (Exception e){
           log.error("解析极兔创建订单返回参数异常",e);
           return null;
        }

    }

}
