package com.holderzone.member.mall.provider.logistics.jt.domain.response;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderCancelResponse implements Serializable {

    /**
     * 集包地
     */
    private String billCode;

    /**
     * 返回客户订单号。
     */
    private String txlogisticId;

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCancelResponse buildCommonResponse(String rsp){
        try {
            JtBaseResponse<OrderCancelResponse> jtBaseResponse = JSONObject.parseObject(rsp, new TypeReference<JtBaseResponse<OrderCancelResponse>>() {
            }.getType());
            if(jtBaseResponse == null || jtBaseResponse.getData() == null){
                return null;
            }
            return LogisticsOrderCancelResponse.builder().build();
        }catch (Exception e){
           log.error("解析极兔取消订单返回参数异常",e);
           return null;
        }

    }

}
