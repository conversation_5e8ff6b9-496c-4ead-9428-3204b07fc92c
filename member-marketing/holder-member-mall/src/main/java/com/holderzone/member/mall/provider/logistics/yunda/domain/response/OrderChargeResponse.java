package com.holderzone.member.mall.provider.logistics.yunda.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeResponse;
import com.holderzone.member.common.exception.MallBaseException;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
public class OrderChargeResponse {

    private OrderChargeResponse(){
        throw new MallBaseException();
    }

    public static LogisticsOrderChargeResponse buildCommonResponse(String json){
        YundaBaseRsp<String> chargeRsp = JSONObject.parseObject(json, new TypeReference<YundaBaseRsp<String>>() {
        }.getType());

        if(chargeRsp == null || chargeRsp.getData() == null) {
            return null;
        }
        //TODO
        return LogisticsOrderChargeResponse.builder().build();
    }
}
