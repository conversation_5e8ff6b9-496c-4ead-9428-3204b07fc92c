package com.holderzone.member.mall.provider.logistics.converter;

import com.google.common.collect.Lists;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.sto.domain.*;

/**
 * <AUTHOR>
 */
public class StoConverter {

    private StoConverter(){
        throw new MemberBaseException();
    }

    public static StoOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        StoOrderCreate.StoOrderCreateBuilder builder = StoOrderCreate.builder();
        builder.orderNo(orderCreateDTO.getLogisticsNo());
        //构建请求实体
        return builder.build();
    }

    public static StoOrderCancel orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO){
        StoOrderCancel.StoOrderCancelBuilder builder = StoOrderCancel.builder();
        builder.sourceOrderId(orderCancelDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static StoOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        StoOrderTrack.StoOrderTrackBuilder builder = StoOrderTrack.builder();
        builder.order(orderTrackDTO.getOrderNo());
        builder.waybillNoList(Lists.newArrayList("884000360264465","773031309794393"));
        //构建请求实体
        return builder.build();
    }

    public static StoOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        StoOrderCharge.StoOrderChargeBuilder builder = StoOrderCharge.builder();
        builder.sendName("张三");
        builder.sendMobile(orderChargeDTO.getOrderNo());
        return builder.build();
    }
}
