package com.holderzone.member.mall.controller.order;

import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.aggregation.StoreOrderDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.order.AggregationOrderDetailResponseVO;
import com.holderzone.member.common.qo.order.AggregationOrderQueryVO;
import com.holderzone.member.common.qo.order.AggregationOrderResponseVO;
import com.holderzone.member.mall.adpter.AggregationOrderAdapter;
import com.holderzone.member.mall.service.order.HsaAggregationOrderService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 聚合订单
 */
@RestController
@RequestMapping("/aggregation/order")
@Slf4j
@RequiredArgsConstructor
public class AggregationOrderController {

    private final HsaAggregationOrderService aggregationOrderService;

    private final AggregationOrderAdapter aggregationOrderAdapter;

    @ApiOperation("老门店订单推送")
    @PostMapping(value = "/store/push", produces = "application/json;charset=utf-8")
    public Result<Void> push(@RequestBody StoreOrderDTO storeOrderDTO) {
        log.info("订单推送入参:{},", JacksonUtils.writeValueAsString(storeOrderDTO));
        aggregationOrderService.storeOrderPush(storeOrderDTO);
        return Result.success();
    }

    @ApiOperation("订单列表")
    @PostMapping(value = "/list", produces = "application/json;charset=utf-8")
    public Result<PageResult<AggregationOrderResponseVO>> list(@RequestBody AggregationOrderQueryVO queryVO) {
        log.info("查询订单列表入参:{},", JacksonUtils.writeValueAsString(queryVO));
        return Result.success(aggregationOrderAdapter.pageInfo(queryVO));
    }

    @ApiOperation("查询订单详情")
    @GetMapping(value = "/get/{guid}", produces = "application/json;charset=utf-8")
    public Result<AggregationOrderDetailResponseVO> get(@PathVariable String guid) {
        log.info("查询订单详情入参:{},", guid);
        return Result.success(aggregationOrderAdapter.getDetail(guid));
    }
}
