package com.holderzone.member.mall.provider.logistics.yto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum YtoFailEnum {

    /**
     * 错误信息(更多错误信息见官网https://open.yto.net.cn/home)
     */
    SYSTEM_OTHER_ERROR(200010002, "系统其它错误信息"),

    NON_STANDARD_PARAMS(200010003, "入参不规范等错误信息"),

    ;

    /**
     * 编号
     */
    private final long code;

    /**
     * 描述
     */
    private final String reason;



}
