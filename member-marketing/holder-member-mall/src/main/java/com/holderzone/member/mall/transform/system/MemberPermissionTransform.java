package com.holderzone.member.mall.transform.system;

import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.entity.permission.HsaSubjectPermission;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface MemberPermissionTransform {

    MemberPermissionTransform INSTANCE = Mappers.getMapper( MemberPermissionTransform.class );


    /**
     * 运营主体权限转VO
     * @param hsaSubjectPermissionList 运营主体
     * @return
     */
    List<HsaOperSubjectPermissionVO> subjectPermissionListTOVO(List<HsaSubjectPermission> hsaSubjectPermissionList);

    @Mappings({
            @Mapping(source = "guid",target = "id"),
            @Mapping(source = "multiMemberName",target = "name"),
            @Mapping(source = "isChecked",target = "is_checked")
    })
    HsaOperSubjectPermissionVO subjectPermissionTOVO(HsaSubjectPermission hsaSubjectPermission);

    /**
     * 运营主体集合转vo
     * @param operSubjectInfoList 运营主体集合
     * @return vo集合
     */
    List<HsaOperSubjectPermissionVO> subjectPermissionListToVo(List<OperSubjectInfo> operSubjectInfoList);

    /**
     * 运营主体转vo
     * @param operSubjectInfo 运营主体信息
     * @return 运营主体vo对象
     */
    @Mappings({
            @Mapping(source = "operSubjectGuid",target = "id"),
            @Mapping(source = "multiMemberName",target = "name"),
            @Mapping(source = "multiMemberStatus",target = "is_checked")
    })
    HsaOperSubjectPermissionVO subjectPermissionToVo(OperSubjectInfo operSubjectInfo);

    @Mappings({
            @Mapping(source = "id",target = "guid"),
            @Mapping(source = "name",target = "multiMemberName")
    })
    HsaSubjectPermission operSubjectPermissionVOTO(HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO);
}
