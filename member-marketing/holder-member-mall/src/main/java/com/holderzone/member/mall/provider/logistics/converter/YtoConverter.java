package com.holderzone.member.mall.provider.logistics.converter;

import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.yto.domain.YtoOrderCancel;
import com.holderzone.member.mall.provider.logistics.yto.domain.YtoOrderCharge;
import com.holderzone.member.mall.provider.logistics.yto.domain.YtoOrderCreate;
import com.holderzone.member.mall.provider.logistics.yto.domain.YtoOrderTrack;

/**
 * <AUTHOR>
 */
public class YtoConverter {

    private YtoConverter(){
        throw new MemberBaseException();
    }

    public static YtoOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO) {
        YtoOrderCreate.YtoOrderCreateBuilder builder = YtoOrderCreate.builder();
        orderCreateDTO.setLogisticsNo("LogisticsNo");
        //构建请求实体
        return builder.build();
    }

    public static YtoOrderCancel orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO) {
        YtoOrderCancel.YtoOrderCancelBuilder builder = YtoOrderCancel.builder();
        orderCancelDTO.setOrderNo("12");
        //构建请求实体
        return builder.build();
    }

    public static YtoOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO) {
        YtoOrderTrack.YtoOrderTrackBuilder builder = YtoOrderTrack.builder();
        orderTrackDTO.setOrderNo("12");
        //构建请求实体
        return builder.build();
    }

    public static YtoOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO) {
        YtoOrderCharge.YtoOrderChargeBuilder builder = YtoOrderCharge.builder();
        orderChargeDTO.setOrderNo("12");
        //构建请求实体
        return builder.build();
    }
}
