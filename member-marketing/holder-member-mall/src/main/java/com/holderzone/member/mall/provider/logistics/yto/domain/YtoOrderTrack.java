package com.holderzone.member.mall.provider.logistics.yto.domain;

import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.mall.provider.constant.ParamsCheckConstant;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 圆通订单取消
 */
@Data
@Builder
public class YtoOrderTrack {

    /**
     * 必填
     * 	圆通物流运单号，一次只能查询一个单号。
     */
    private String Number;

    public void checkParams() {
        if(StringUtils.isEmpty(Number)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"Number"));
        }
    }
}
