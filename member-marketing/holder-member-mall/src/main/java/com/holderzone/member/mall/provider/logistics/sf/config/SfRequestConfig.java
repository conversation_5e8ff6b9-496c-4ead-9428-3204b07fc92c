package com.holderzone.member.mall.provider.logistics.sf.config;

import com.holderzone.member.mall.provider.logistics.sf.client.SfClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "sf")
public class SfRequestConfig {

    private String partnerId;

    private String appSecret;

    private String url;

    private String createServiceCode;

    private String cancelServiceCode;

    private String chargeServiceCode;

    private String trackServiceCode;

    @Bean
    public SfClient sfClient(){
       return new SfClient(partnerId,appSecret,url);
    }
}
