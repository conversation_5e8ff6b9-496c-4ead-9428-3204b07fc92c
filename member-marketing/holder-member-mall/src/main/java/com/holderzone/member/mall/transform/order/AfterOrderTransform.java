package com.holderzone.member.mall.transform.order;

import com.holderzone.member.common.dto.order.AfterSaleOrderDTO;
import com.holderzone.member.mall.entity.order.HsaAfterSaleOrder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface AfterOrderTransform {

    AfterOrderTransform INSTANCE = Mappers.getMapper(AfterOrderTransform.class);

    HsaAfterSaleOrder orderDTO2DO(AfterSaleOrderDTO afterSaleOrderDTO);

    AfterSaleOrderDTO orderDO2DTO(HsaAfterSaleOrder afterSaleOrder);

}
