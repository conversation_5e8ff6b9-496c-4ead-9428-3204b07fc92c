package com.holderzone.member.mall.provider.logistics.best.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "best")
public class BestRequestConfig {

    private String url;

    private String partnerID;

    private String partnerKey;
}
