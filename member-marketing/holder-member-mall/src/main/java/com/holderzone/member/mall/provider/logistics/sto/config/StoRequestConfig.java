package com.holderzone.member.mall.provider.logistics.sto.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "sto")
public class StoRequestConfig {

    private String appKey;

    private String secretKey;

    private String sourceCode;

    private String orderSource;

    private String url;

    private BaseBusinessConfig create;

    private BaseBusinessConfig cancel;

    private BaseBusinessConfig charge;

    private BaseBusinessConfig track;

    @Data
    public static class BaseBusinessConfig{

        private String api;

        private String appKey;
    }
}
