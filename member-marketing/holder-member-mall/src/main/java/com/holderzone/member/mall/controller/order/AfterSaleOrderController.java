package com.holderzone.member.mall.controller.order;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.AgreeRefundDTO;
import com.holderzone.member.common.dto.order.RefuseRefundDTO;
import com.holderzone.member.common.qo.mall.order.ApplyRefundQO;
import com.holderzone.member.mall.manage.AfterSaleOrderManage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 售后订单控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/after_sale_order")
@Slf4j
@AllArgsConstructor
public class AfterSaleOrderController {

    private final AfterSaleOrderManage afterSaleOrderManage;

    @PostMapping("/member_apply_refund")
    public Result<?> memberApplyRefund(@RequestBody @Validated ApplyRefundQO refundQO){
        afterSaleOrderManage.memberApplyRefund(refundQO);
        return Result.success();
    }

    @PostMapping("/batch_agree_refund")
    public Result<?> batchAgreeRefund(@RequestBody @Validated AgreeRefundDTO listAgreeRefund){
        afterSaleOrderManage.batchAgreeRefund(listAgreeRefund);
        return Result.success();
    }

    @PostMapping("/batch_refuse_refund")
    public Result<?> batchRefuseRefund(@RequestBody @Validated RefuseRefundDTO refuseRefund){
        afterSaleOrderManage.batchRefuseRefund(refuseRefund);
        return Result.success();
    }
    @PostMapping("/user_cancel_refund/{guid}")
    public Result<?> userCancelRefund(@PathVariable String guid){
        afterSaleOrderManage.userCancelRefund(guid);
        return Result.success();
    }
}
