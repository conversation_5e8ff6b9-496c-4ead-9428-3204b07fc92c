package com.holderzone.member.mall.provider.logistics.tt;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.converter.TtConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.logistics.sto.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.tt.client.Api;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderBase;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderCreate;
import com.holderzone.member.mall.provider.logistics.tt.domain.response.OrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.tt.domain.response.OrderTrackResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * 天天快递服务(<a href="https://www.51tracking.com/ttkd-tracking/"/>)
 * 使用服务需付费 不支持在线下单 无查询运价服务
 */
@Service
@Slf4j
@AllArgsConstructor
public class TtService extends AbstractLogisticsService {

    private final Api api;

    @Override
    public ExpressTypeEnum type() {
        return ExpressTypeEnum.TT;
    }

    @Override
    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        TtOrderCreate ttOrderCreate = TtConverter.orderCreateConvertDo(orderCreateDTO);
        String requestData = JSONObject.toJSONString(ttOrderCreate);
        log.info("天天快递创建订单请求参数：{}",requestData);
        String rsp = api.create(requestData);
        log.info("天天快递创建订单返回参数：{}",rsp);
        return OrderCreateResponse.buildCommonResponse(rsp);
    }

    @Override
    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        List<TtOrderBase> list = TtConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        String requestData = JSONObject.toJSONString(list);
        log.info("天天快递取消订单请求参数：{}",requestData);
        String rsp = api.delete(requestData);
        log.info("天天快递取消订单返回参数：{}",rsp);
        return OrderCancelResponse.buildCommonResponse(rsp);
    }

    @Override
    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        String requestData = TtConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        log.info("天天快递查询订单轨迹请求参数：{}",requestData);
        String rsp = api.get(requestData);
        log.info("天天快递查询订单轨迹返回参数：{}",rsp);
        OrderTrackResponse.buildCommonResponse(rsp);
        return super.queryOrderTrack(logisticsOrderTrackDTO);
    }

    @Override
    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        return super.queryLogisticsCharge(orderChargeDTO);
    }
}
