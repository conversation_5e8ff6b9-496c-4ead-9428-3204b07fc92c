package com.holderzone.member.mall.controller.order;

import com.holderzone.member.mall.manage.AutoDealJobManage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 订单定时任务触发
 */
@RestController
@RequestMapping("/auto/deal")
@Slf4j
@AllArgsConstructor
public class AutoDealJobController {

    private final AutoDealJobManage autoDealJobManage;

    @PostMapping(value = "/order/cancel")
    public void orderCancel(@RequestBody LocalDateTime cancelTime) {
        autoDealJobManage.orderCancel(cancelTime);
    }

    @PostMapping(value = "/order/receive")
    public void orderReceive(@RequestBody LocalDateTime receiveTime) {
        autoDealJobManage.orderReceive(receiveTime);
    }

    @PostMapping(value = "/order/refund")
    public void orderRefundConfirm(@RequestBody LocalDateTime refundConfirmTime) {
        autoDealJobManage.refundConfirm(refundConfirmTime);
    }
}
