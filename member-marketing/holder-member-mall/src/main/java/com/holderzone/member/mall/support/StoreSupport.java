package com.holderzone.member.mall.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.grade.StoreInfoDTO;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.tool.StoreByIdQO;
import com.holderzone.member.common.vo.feign.CrmFeignModel;
import com.holderzone.member.common.vo.feign.FeignModel;
import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * crm 门店数据支持
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StoreSupport {

    // TODO 需要改造（已改）
    private final ExternalSupport externalSupport;

    private final StringRedisTemplate redisTemplate;

    private final static String CRM_STORE_INFO = "CRM_STORE_INFO:";

    /**
     * 远程调用获取门店和档口信息
     */
    public List<StoreInfoVO> getStoreInfo() {
        try {
            StoreInfoDTO query = new StoreInfoDTO();
            query.setChannel("会员商城");
            query.setOperatingSubjectId(ThreadLocalCache.getOperSubjectGuid());
            List<StoreInfoVO> dataList = externalSupport.storeServer(ThreadLocalCache.getSystem()).getStoreStall(query);
            log.info("[listStorePage]查询主体下的店铺和档口,query={},storeStallResult={}", JacksonUtils.writeValueAsString(query),
                    JacksonUtils.writeValueAsString(dataList));
            redisTemplate.opsForValue().set(CRM_STORE_INFO + ThreadLocalCache.getOperSubjectGuid(), JSONArray.toJSONString(dataList));
            redisTemplate.expire(CRM_STORE_INFO + ThreadLocalCache.getOperSubjectGuid(), 8L, TimeUnit.HOURS);
            return dataList;
        } catch (Exception e) {
            log.info("远程调用获取门店和档口信息失败", e);
            String data = redisTemplate.opsForValue().get(CRM_STORE_INFO + ThreadLocalCache.getOperSubjectGuid());
            if (data == null) {
                return Collections.emptyList();
            }
            return JSONArray.parseArray(data, StoreInfoVO.class);
        }

    }

    /**
     * 获取门店ids
     */
    public List<Long> getStoreIds() {
        List<StoreInfoVO> storeList = getStoreInfo();
        if (CollectionUtils.isEmpty(storeList)) {
            return Lists.newArrayList();
        }
        return storeList.stream().map(StoreInfoVO::getStore_id).distinct().collect(Collectors.toList());
    }


    /**
     * 查询门店基础信息
     */
    public List<StoreDataInfoVO> getStoreByIds(List<Integer> storeIds) {
        StoreByIdQO storageByIdQuery = new StoreByIdQO();
        storageByIdQuery.setStoreIdsFromIntegers(storeIds);
        return JSON.parseArray(getStoresTolerant(storageByIdQuery), StoreDataInfoVO.class);
    }


    public StoreDataInfoVO getStoreById(String storeId) {
        StoreByIdQO storageByIdQuery = new StoreByIdQO();
        storageByIdQuery.setStore_ids(Lists.newArrayList());
        storageByIdQuery.getStore_ids().add(Long.valueOf(storeId));
        List<StoreDataInfoVO> dataInfoList = JSONArray.parseArray(getStoresTolerant(storageByIdQuery), StoreDataInfoVO.class);
        if (CollectionUtils.isEmpty(dataInfoList)) {
            return null;
        }
        return dataInfoList.get(0);
    }

    private String getStoresTolerant(StoreByIdQO storageByIdQuery) {
        storageByIdQuery.getStore_ids().sort(Long::compareTo);
        int hashCode = storageByIdQuery.getStore_ids().hashCode();
        try {
            Object dataList = externalSupport.storeServer(ThreadLocalCache.getSystem()).getStoreById(storageByIdQuery);
            if (Objects.isNull(dataList)){
                return null;
            }
            String storeData = JSON.toJSONString(dataList);
            redisTemplate.opsForValue().set(CRM_STORE_INFO + hashCode, storeData);
            redisTemplate.expire(CRM_STORE_INFO + hashCode, 8L, TimeUnit.HOURS);
            return storeData;
        } catch (Exception e) {
            log.info("查询crm门店报错", e);
            return redisTemplate.opsForValue().get(CRM_STORE_INFO + hashCode);
        }
    }

}
