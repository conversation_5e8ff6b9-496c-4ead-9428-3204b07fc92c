package com.holderzone.member.mall.service.logistics.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.mapper.logistics.HsaLogisticsRechargeMapper;
import com.holderzone.member.mall.service.logistics.HsaLogisticsChargeService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HsaLogisticsChargeServiceImpl extends HolderBaseServiceImpl<HsaLogisticsRechargeMapper, HsaLogisticsCharge> implements HsaLogisticsChargeService {

    @Override
    public List<HsaLogisticsCharge> findByTemplateGuid(String templateGuid) {
        QueryWrapper<HsaLogisticsCharge> qw = new QueryWrapper<>();
        qw.lambda().eq(HsaLogisticsCharge::getTemplateGuid, templateGuid);
        return list(qw);
    }

    @Override
    public void removeByTemplateGuid(String templateGuid) {
        UpdateWrapper<HsaLogisticsCharge> uw = new UpdateWrapper<>();
        uw.lambda().eq(HsaLogisticsCharge::getTemplateGuid, templateGuid);
        remove(uw);
    }
}
