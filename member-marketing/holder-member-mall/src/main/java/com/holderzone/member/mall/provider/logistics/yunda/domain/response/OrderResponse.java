package com.holderzone.member.mall.provider.logistics.yunda.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderResponse {
    /**
     * 响应编码
     */
    private String orderid;

    /**
     * 原样返回字段
     */
    private String backparam;

    /**
     * 构建统一返回参数
     */
    public static OrderResponse buildCommonResponse(String json){
        YundaBaseRsp<OrderResponse> orderRsp = JSONObject.parseObject(json, new TypeReference<YundaBaseRsp<OrderResponse>>() {
        }.getType());
        if(orderRsp == null || orderRsp.getData() == null){
            return null;
        }
        return orderRsp.getData();
    }

    public static LogisticsOrderCreateResponse buildCreateResponse(String json){
        return LogisticsOrderCreateResponse.builder().courierNumber(json).build();
    }

    public static LogisticsOrderCancelResponse buildCancelResponse(String json){
        LogisticsOrderCreateResponse.builder().courierNumber(json).build();
        return LogisticsOrderCancelResponse.builder().build();
    }
}
