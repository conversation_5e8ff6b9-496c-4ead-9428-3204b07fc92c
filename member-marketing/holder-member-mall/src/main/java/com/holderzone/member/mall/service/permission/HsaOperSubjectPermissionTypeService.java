package com.holderzone.member.mall.service.permission;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.mall.entity.permission.HsaOperSubjectPermissionType;

/**
 * <AUTHOR>
 */
public interface HsaOperSubjectPermissionTypeService extends IHolderBaseService<HsaOperSubjectPermissionType> {


    /**
     * 新增或者修改运营主体权限类型接口
     * @param request 请求参数
     * @return 操作结果
     */
    boolean savaOrUpdate(OperSubjectPermissionQO request);

}
