package com.holderzone.member.mall.transform.set;

import com.holderzone.member.common.dto.mall.set.CommonSetDTO;
import com.holderzone.member.mall.entity.set.HsaCommonSet;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface HsaCommonSetTransform {

    HsaCommonSetTransform INSTANCE = Mappers.getMapper(HsaCommonSetTransform.class);

    List<HsaCommonSet> DTO2DOList(List<CommonSetDTO> dtoList);

    HsaCommonSet DTO2DO(CommonSetDTO commonSetDTO);

    CommonSetDTO DO2DTO(HsaCommonSet commonSet);
}
