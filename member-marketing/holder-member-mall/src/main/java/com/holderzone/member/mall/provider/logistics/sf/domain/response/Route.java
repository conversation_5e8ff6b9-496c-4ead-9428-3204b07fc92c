package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Route {

    /**
     * 路由节点发生的时间，格式：YYYY-MM-DD HH24:MM:SS，示例：2012-07-30 09:30:00。
     */
    private String acceptTime;

    /**
     * 路由节点发生的地点
     */
    private String acceptAddress;

    /**
     * 路由节点具体描述
     */
    private String remark;

    /**
     * 路由节点操作码
     */
    private String opcode;

}
