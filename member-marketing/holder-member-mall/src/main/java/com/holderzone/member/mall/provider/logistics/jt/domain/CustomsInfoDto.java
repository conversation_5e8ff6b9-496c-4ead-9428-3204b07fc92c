package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomsInfoDto {

    /**
     *货物数量 跨境件报关需要填写
     */
    private int count;

    /**
     * 货物单位，如：个、台、本， 跨境件报关需要填写
     */
    private String unit;

    /**
     * 原产地国别， 跨境件报关需要填写
     */
    private String sourceArea;

    /**
     * 货物产品国检备案编号 跨境件报关需要填写
     */
    private String productRecordNo;

    /**
     * 商品海关备案号 跨境件报关需要填写
     */
    private String goodPrepardNo;

    /**
     * 商品行邮税号 跨境件报关需要填写
     */
    private String taxNo;

    /**
     * 海关编码 跨境件报关需要填写
     */
    private String hsCode;

    /**
     * 商品编号 跨境件报关需要填写
     */
    private String goodsCode;

    /**
     * 货物品牌 跨境件报关需要填写
     */
    private String brand;

    /**
     * 货物规格型号 跨境件报关需要填写
     */
    private String specifications;

    /**
     * 生产厂家 跨境件报关需要填写
     */
    private String manufacturer;

    /**
     * 托寄物声明价值 跨境件报关需要填写
     */
    private double cargoDeclaredValue;

    /**
     * 托寄物声明价值币别 跨境件报关需要填写
     */
    private String declaredValueDeclaredCurrency;

    /**
     * 客户支付的运费 跨境件报关需要填写
     */
    private String customerFreight;

}
