package com.holderzone.member.mall.provider.logistics.zto.domain.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class TraceSiteInfoOutput {

    /**
     * 网点是否中心,T:true,F:false
     */
    private String isCenter;

    /**
     * 网点编码/机柜hallCode;网点扫描时为网点编码，入站出站第三方签收时是机柜的hallCode
     */
    private String code;

    /**
     * 网点联系方式
     */
    private String phone;

    /**
     * 网点所在城市
     */
    private String city;

    /**
     * 网点是否转运中心.1，是 2，否
     */
    private Integer isTransfer;

    /**
     * 网点名称
     */
    private String name;

    /**
     * 原始网点id,始终为网点id
     */
    private long siteId;

    /**
     * 网点扫描时为网点id，入站出站第三方签收时为null（即存代码，不确认是否有业务方在使用）
     */
    private long id;

    /**
     * 网点所在省份
     */
    private String prov;
}
