package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ZtoOrderCreate {
    /**
     * 必填
     * 合作模式 ，1：集团客户；2：非集团客户
     */
    private String partnerType;

    /**
     * 必填
     * partnerType为1时，orderType：1：全网件 2：预约件。
     * partnerType为2时，orderType：1：全网件 2：预约件（返回运单号）3：预约件（不返回运单号） 4：星联全网件
     */
    private String orderType;

    /**
     * 必填
     * 合作商订单号
     */
    private String partnerOrderCode;

    /**
     * 账号信息
     */
    private AccountDto accountInfo;

    /**
     * 运单号
     */
    private String billCode;


    /**
     * 必填
     * 发件人信息
     */
    private SenderInfoInput senderInfo;

    /**
     * 必填
     * 收件人信息
     */
    private ReceiveInfoInput receiveInfo;

    /**
     * 增值服务信息
     */
    private List<OrderVasDto> orderVasList;

    /**
     * 门店/仓库编码（partnerType为1时可使用）
     */
    private String hallCode;

    /**
     * 汇总信息
     */
    private SummaryDto summaryInfo;

    /**
     * 网点code（orderVasList.vasType为receiveReturnService必填）
     */
    private String siteCode;

    /**
     * 网点名称（orderVasList.vasType为receiveReturnService必填）
     */
    private String siteName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 物品信息
     */
    private List<OrderItemDto> orderItems;

    /**
     * 机柜信息
     */
    private CabinetDto cabinet;
}
