package com.holderzone.member.mall.service.permission.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.permission.HsaOperSubjectPermissionType;
import com.holderzone.member.mall.mapper.permission.HsaMallOperSubjectPermissionTypeMapper;
import com.holderzone.member.mall.mapper.permission.HsaSubjectPermissionMapper;
import com.holderzone.member.mall.service.permission.HsaOperSubjectPermissionTypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class HsaOperSubjectPermissionTypeServiceImpl extends HolderBaseServiceImpl<HsaMallOperSubjectPermissionTypeMapper,HsaOperSubjectPermissionType> implements HsaOperSubjectPermissionTypeService {

    private final HsaMallOperSubjectPermissionTypeMapper hsaMallOperSubjectPermissionTypeMapper;

    private final HsaSubjectPermissionMapper hsaOperSubjectPermissionMapper;

    private final GuidGeneratorUtil guidGeneratorUtil;

    public HsaOperSubjectPermissionTypeServiceImpl(HsaMallOperSubjectPermissionTypeMapper hsaMallOperSubjectPermissionTypeMapper,
                                                   GuidGeneratorUtil guidGeneratorUtil,
                                                   HsaSubjectPermissionMapper hsaOperSubjectPermissionMapper){
        this.hsaMallOperSubjectPermissionTypeMapper = hsaMallOperSubjectPermissionTypeMapper;
        this.guidGeneratorUtil = guidGeneratorUtil;
        this.hsaOperSubjectPermissionMapper = hsaOperSubjectPermissionMapper;
    }

    @Override
    public boolean savaOrUpdate(OperSubjectPermissionQO request) {
        HsaOperSubjectPermissionType hsaOperSubjectPermissionType = null;
        hsaOperSubjectPermissionType = hsaMallOperSubjectPermissionTypeMapper.selectOne(
                new LambdaQueryWrapper<HsaOperSubjectPermissionType>()
                    .eq(HsaOperSubjectPermissionType::getEnterpriseGuid, request.getTeamId())
                    .eq(HsaOperSubjectPermissionType::getPositionGuid, request.getRoleId())
                    .eq(HsaOperSubjectPermissionType::getSourceType, request.getSourceType())
                    .eq(HsaOperSubjectPermissionType::getIsRole, request.getIsRole()));
        if (Objects.isNull(hsaOperSubjectPermissionType)) {
            String guid = guidGeneratorUtil.getStringGuid(HsaOperSubjectPermissionType.class.getName());
            hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
            BeanUtils.copyProperties(request,hsaOperSubjectPermissionType);
            hsaOperSubjectPermissionType.setEnterpriseGuid(request.getTeamId());
            hsaOperSubjectPermissionType.setPositionGuid(request.getRoleId());
            hsaOperSubjectPermissionType.setGuid(guid);
            return this.save(hsaOperSubjectPermissionType);
        } else {
            BeanUtils.copyProperties(request,hsaOperSubjectPermissionType);
            return this.updateByGuid(hsaOperSubjectPermissionType);
        }
    }
}
