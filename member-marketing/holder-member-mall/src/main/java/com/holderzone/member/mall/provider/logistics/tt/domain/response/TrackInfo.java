package com.holderzone.member.mall.provider.logistics.tt.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class TrackInfo {

    /**
     * 本条物流信息的更新时间，由物流商提供（包裹被扫描时，物流信息会被更新）
     */
    private String checkpoint_date;

    /**
     * 具体的物流情况
     */
    private String tracking_detail;

    /**
     * 物流信息更新的地址（该包裹被扫描时，所在的地址）
     */
    private String location;

    /**
     * 根据具体物流情况所识别出来的物流状态（物流状态）
     */
    private String checkpoint_delivery_status;

    /**
     * 物流状态的子状态（物流状态）
     */
    private String checkpoint_delivery_substatus;

}
