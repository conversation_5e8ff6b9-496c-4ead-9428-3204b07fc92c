package com.holderzone.member.mall.provider.logistics.sf.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class SfOrderTrack {

    /**
     * 必填
     * 1.根据运单号查询 2：根据客户订单号查询
     */
    private int trackingType;

    /**
     * 必填
     * 如果tracking_type=1，则此值为顺丰运单号
     * 如果trackingType=2，则此值为客户订单号
     * 如果有多个单号，以逗号分隔，如”123,124,125”
     */
    private String trackingNumber;

    /**
     * 必填
     * 路由查询类别： 1：标准路由查询
     */
    private int methodType;

    /**
     * 校验电话号码后四位
     */
    private String checkPhoneNo;

}
