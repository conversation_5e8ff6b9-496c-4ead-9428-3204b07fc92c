package com.holderzone.member.mall.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.mall.entity.HsaMallTest;
import com.holderzone.member.mall.service.HsaMallTestService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/8/3 16:32
 */
@RestController
@RequestMapping("/mall_test")
public class TestController {

    @Resource
    private HsaMallTestService hsaMallTestService;

    @ApiOperation("获取商品基础数据")
    @PostMapping(value = "/testSave", produces = "application/json;charset=utf-8")
    public Result testSave(@RequestBody HsaMallTest hsaMallTest) {
        return Result.isSuccess(hsaMallTestService.saveTest(hsaMallTest));
    }

    @PostMapping(value = "/save", produces = "application/json;charset=utf-8")
    public Result save(@RequestParam(defaultValue = "10") int size) {
        return Result.isSuccess(hsaMallTestService.save(size));
    }

    @PostMapping(value = "/delete")
    public Result delete(Long id) {
        hsaMallTestService.delete(id);
        return Result.success();
    }

    @PostMapping(value = "/list")
    public Result list() {
        return Result.success(hsaMallTestService.list());
    }
}
