package com.holderzone.member.mall.controller.order;

import cn.hutool.core.collection.CollUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.enums.mall.order.OrderCancelEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.mall.order.*;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.feign.CrmFeignVo;
import com.holderzone.member.common.vo.mall.PayOrderVO;
import com.holderzone.member.common.vo.order.OrderReasonVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.mall.service.order.*;
import com.holderzone.member.mall.support.UserValidateSupport;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商城订单
 * @date 2022/8/3 16:32
 */
@RestController
@RequestMapping("/mall_order")
@Slf4j
public class MallOrderController {

    @Resource
    private HsaMallBaseOrderService hsaMallBaseOrderService;

    @Resource
    private HsaMallOrderTimeRuleService hsaMallOrderTimeRuleService;

    @Resource
    private HsaAfterSaleOrderService hsaAfterSaleOrderService;

    @Resource
    private HsaCancelOrderRuleService hsaCancelOrderRuleService;

    @Resource
    private HsaRefundNegotiationHistoryService hsaRefundNegotiationHistoryService;

    @Resource
    private UserValidateSupport userValidateSupport;

    // TODO 需要改造(不需要改造)
    @Resource
    private CrmFeign crmFeign;


    @ApiOperation("分页查询订单")
    @PostMapping(value = "/find_mall_order_page", produces = "application/json;charset=utf-8")
    public Result findMallBaseOrderPage(@RequestBody QueryMallOrderQO queryMallOrderQO) {
        return Result.success(hsaMallBaseOrderService.findMallBaseOrderPage(queryMallOrderQO));
    }

    @ApiOperation("订单详情")
    @GetMapping(value = "/find_mall_order_details", produces = "application/json;charset=utf-8")
    public Result findMallBaseOrderDetails(@RequestParam(value = "orderGuid") String orderGuid) {
        return Result.success(hsaMallBaseOrderService.findMallBaseOrderDetails(orderGuid));
    }

    @ApiOperation("订单商品")
    @GetMapping(value = "/find_mall_order_product", produces = "application/json;charset=utf-8")
    List<ResOrderCommodity> findMallBaseOrderProduct(@RequestParam(value = "orderNum") String orderNum) {
        return hsaMallBaseOrderService.findMallBaseOrderProduct(orderNum);
    }

    @ApiOperation("订单导出")
    @PostMapping(value = "/mall_order_export", produces = "application/json;charset=utf-8")
    public Result mallBaseOrderExport(@RequestBody QueryMallOrderQO queryMallOrderQO) {
        return Result.success(hsaMallBaseOrderService.mallBaseOrderExport(queryMallOrderQO));
    }

    @ApiOperation("订单时间设置")
    @PostMapping(value = "/saveMallOrderTimeRule", produces = "application/json;charset=utf-8")
    public Result saveMallOrderTimeRule(@RequestBody MallOrderTimeRuleVO mallOrderTimeRuleVO) {
        return Result.success(hsaMallOrderTimeRuleService.saveMallOrderTimeRule(mallOrderTimeRuleVO));
    }

    @ApiOperation("订单时间获取")
    @PostMapping(value = "/getMallOrderTimeRule", produces = "application/json;charset=utf-8")
    public Result getMallOrderTimeRule() {
        return Result.success(hsaMallOrderTimeRuleService.getMallOrderTimeRule());
    }

    @ApiOperation("立即购买")
    @PostMapping(value = "/checkOrder", produces = "application/json;charset=utf-8")
    public Result checkOrder(@RequestBody AppletMallOrderQO request) {
        log.info("立即购买入参：{}", JacksonUtils.writeValueAsString(request));
        return Result.success(hsaMallBaseOrderService.checkOrder(request));
    }

    @ApiOperation("立即支付")
    @PostMapping(value = "/createOrder", produces = "application/json;charset=utf-8")
    Result<PayOrderVO> payMallOrder(@RequestBody SavePayOrderQO savePayOrderQO) {
        return Result.success(hsaMallBaseOrderService.savePayMallOrder(savePayOrderQO));
    }

    /**
     * V1 获取运费时调用，后续删除
     */
    @ApiOperation("获取运费")
    @PostMapping(value = "/getFreightAmount", produces = "application/json;charset=utf-8")
    Result getSingleFreightAmount(@RequestBody GetFreightAmount getFreightAmount) {
        if (CollectionUtils.isEmpty(getFreightAmount.getOrderProductAmountQOS())) {
            throw new MallBaseException("商品不能为空");
        }
        return Result.success(hsaMallBaseOrderService.getSingleFreightAmount(getFreightAmount));
    }

    @ApiOperation("获取运费")
    @PostMapping(value = "/freight_amount", produces = "application/json;charset=utf-8")
    Result getFreightAmount(@RequestBody GetFreightAmount getFreightAmount) {
        if (CollectionUtils.isEmpty(getFreightAmount.getOrderProductAmountQOS())) {
            throw new MallBaseException("商品不能为空");
        }
        return Result.success(hsaMallBaseOrderService.getFreightAmount(getFreightAmount));
    }

    @ApiOperation("订单支付")
    @PostMapping(value = "/payOrder", produces = "application/json;charset=utf-8")
    Result payMallOrder(@RequestBody PayOrderQO payOrderQO) {
        return Result.success(hsaMallBaseOrderService.payMallOrder(payOrderQO));
    }

    @ApiOperation("后台发货")
    @PostMapping(value = "/orderShipDea", produces = "application/json;charset=utf-8")
    Result orderShipDea(@RequestBody OrderShipQO orderShipQO) {
        return Result.success(hsaMallBaseOrderService.orderShipDea(orderShipQO));
    }

    @ApiOperation("会员信息同步")
    @PostMapping(value = "/memberSynOrder", produces = "application/json;charset=utf-8")
    void memberSynOrder(@RequestBody MemberSynOrderQO memberSynOrderQO) {
        hsaMallBaseOrderService.memberSynOrder(memberSynOrderQO);
    }

    @ApiOperation("实时订单状态更新")
    @GetMapping(value = "/updateOrderCondition", produces = "application/json;charset=utf-8")
    void updateOrderCondition() {
        hsaMallBaseOrderService.updateOrderCondition();
    }

    @ApiOperation("售后维权查询")
    @PostMapping(value = "/findAfterSaleOrderPage", produces = "application/json;charset=utf-8")
    Result findAfterSaleOrderPage(@RequestBody QueryAfterOrderQO request) {
        return Result.success(hsaAfterSaleOrderService.findAfterSaleOrderPage(request));
    }

    @ApiOperation("售后维权查询详情")
    @GetMapping(value = "/findAfterSaleOrderDetails", produces = "application/json;charset=utf-8")
    Result findAfterSaleOrderDetails(@RequestParam(value = "guid") String guid) {
        return Result.success(hsaAfterSaleOrderService.findAfterSaleOrderDetails(guid));
    }

    @ApiOperation("售后维权导出")
    @PostMapping(value = "/afterSaleOrderExport", produces = "application/json;charset=utf-8")
    Result mallAfterSaleOrderExport(@RequestBody QueryAfterOrderQO request) {
        return Result.success(hsaAfterSaleOrderService.mallAfterSaleOrderExport(request));
    }

    @ApiOperation("新增原因")
    @PostMapping(value = "/addOrUpdate", produces = "application/json;charset=utf-8")
    Result addOrUpdate(@RequestBody CancelOrderRuleQO request) {
        return Result.success(hsaCancelOrderRuleService.addOrUpdate(request));
    }

    @ApiOperation("查询原因列表")
    @GetMapping(value = "/getCancelOrderRule", produces = "application/json;charset=utf-8")
    Result getCancelOrderRule() {
        return Result.success(hsaCancelOrderRuleService.getCancelOrderRule());
    }

    @PostMapping(value = "/deleteCancelOrderRule", produces = "application/json;charset=utf-8")
    Result deleteCancelOrderRule(String guid) {
        return Result.success(hsaCancelOrderRuleService.deleteCancelOrderRule(guid));
    }

    @PostMapping(value = "/orderDelivery", produces = "application/json;charset=utf-8")
    Result orderDelivery(@RequestBody MallOrderDeliveryQO request) {
        return Result.success(hsaMallBaseOrderService.orderDelivery(request));
    }

    @PostMapping(value = "/business/order_refund", produces = "application/json;charset=utf-8")
    Result<?> businessOrderRefund(@RequestBody @Validated OrderRefundQO request) {
        userValidateSupport.validatePasswordCode(request.getSuccessCode());
        hsaMallBaseOrderService.orderBusinessRefund(request);
        return Result.success();
    }

    @PostMapping(value = "/order_cancel/{cancelType}", produces = "application/json;charset=utf-8")
    Result<?> userOrderCancel(@RequestBody @Validated UserCancelQO userCancel, @PathVariable Integer cancelType) {
        hsaMallBaseOrderService.orderCancel(userCancel.getOrderGuid(), userCancel.getReason(), cancelType);
        return Result.success();
    }

    @GetMapping(value = "/list_reason/{type}", produces = "application/json;charset=utf-8")
    Result<List<OrderReasonVO>> listReason(@PathVariable String type) {
        return Result.success(hsaCancelOrderRuleService.listReason(type));
    }

    @PostMapping(value = "/validate_user_password", produces = "application/json;charset=utf-8")
    Result<String> validateUserPassword(@RequestBody ValidatePasswordQO qo) {
        return Result.success(userValidateSupport.validateUserPassword(qo.getTel(), qo.getPassword()));
    }

    @ApiOperation("查询协商历史")
    @GetMapping(value = "/getRefundNegotiationHistory", produces = "application/json;charset=utf-8")
    Result getRefundNegotiationHistory(String afterSaleOrderGuid) {

        return Result.success(hsaRefundNegotiationHistoryService.getRefundNegotiationHistory(afterSaleOrderGuid));
    }

    /**
     * 小程序查看协商历史
     *
     * @param orderGuid 订单guid
     * @return 查询结果
     */
    @ApiOperation("查询协商历史")
    @GetMapping(value = "/getAppletNegotiationHistory", produces = "application/json;charset=utf-8")
    Result getAppletNegotiationHistory(String orderGuid) {

        return Result.success(hsaRefundNegotiationHistoryService.getAppletNegotiationHistory(orderGuid));
    }

    /**
     * 小程序订单列表查询qo
     *
     * @return 查询结果
     */
    @ApiOperation("小程序订单列表查询")
    @PostMapping(value = "/appletOrder", produces = "application/json;charset=utf-8")
    Result appletOrderQO(@RequestBody AppletOrderQO request) {

        return Result.success(hsaMallBaseOrderService.appletOrderQO(request));
    }

    /**
     * 确认收货
     *
     * @param orderGuid 订单guid
     */
    @PostMapping(value = "/confirm_received/{orderGuid}", produces = "application/json;charset=utf-8")
    Result<Void> confirmReceived(@PathVariable String orderGuid) {
        hsaMallBaseOrderService.confirmReceived(orderGuid);
        return Result.success();
    }

    /**
     * 供应链发货
     */
    @ApiOperation("供应链发货")
    @PostMapping(value = "/update_order_state", produces = "application/json;charset=utf-8")
    Result<Void> updateOrderState(@RequestBody AppletOrderStateQO approveState) {
        hsaMallBaseOrderService.updateOrderState(approveState);
        return Result.success();
    }
}
