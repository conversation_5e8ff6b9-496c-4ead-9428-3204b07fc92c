package com.holderzone.member.mall.provider.logistics.yunda.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse {
    /**
     * 响应状态
     */
    private Boolean result;

    /**
     * 时间
     */
    private String time;

    /**
     * 运单号
     */
    private String mailno;

    /**
     * 备注
     */
    private String remark;

    /**
     * 节点状态
     */
    private String status;

    /**
     * 节点状态
     */
    private List<Step> steps;


    public static LogisticsOrderTrackResponse buildCommonResponse(String json){
        YundaBaseRsp<OrderTrackResponse> trackRsp = JSONObject.parseObject(json, new TypeReference<YundaBaseRsp<OrderTrackResponse>>() {
        }.getType());

        if(trackRsp == null || trackRsp.getData() == null) {
            return null;
        }
        //TODO
        return LogisticsOrderTrackResponse.builder().build();

    }
}
