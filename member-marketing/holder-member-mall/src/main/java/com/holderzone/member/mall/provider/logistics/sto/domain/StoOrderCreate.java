package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class StoOrderCreate {

    /**
     * 必填
     * 订单号（客户系统自己生成，唯一）
     */
    private String orderNo;

    /**
     * 必填
     * 订单来源（订阅服务时填写的来源编码）
     */
    private String orderSource;

    /**
     * 必填
     * 获取面单的类型（00-普通、03-国际、01-代收、02-到付、04-生鲜），默认普通业务，如果有其他业务先与业务方沟通清楚
     */
    private String billType;

    /**
     * 必填
     * 订单类型（01-普通订单、02-调度订单）默认01-普通订单，如果有散单业务需先业务方沟通清楚
     */
    private String orderType;

    /**
     * 必填
     * 寄件人信息
     */
    private PersonDto sender;

    /**
     * 必填
     * 收件人信息
     */
    private PersonDto receiver;

    /**
     * 必填
     *包裹信息
     */
    private Cargo cargo;

    /**
     *客户信息，在线下单取运单号必填，代单号下单不需要填写，测试账号传值如下，生产账号联系合作业务方提供
     */
    private Customer customer;

    /**
     * 国际订单附属信息（国际业务订单必填，其他业务不要填写）
     */
    private InternationalAnnex internationalAnnex;

    /**
     * 运单号（下单前已获取运单号时必传，否则不传或传NULL）
     */
    private String waybillNo;

    /**
     * 指定网点揽收（调度散单业务订单需要传）其他业务不需要
     */
    private AssignAnnex assignAnnex;

    /**
     * 代收货款金额，单位：元（代收货款业务时必填）
     */
    private String codValue;

    /**
     * 到付运费金额，单位：元（到付业务时必填）
     */
    private String freightCollectValue;

    /**
     * 时效类型（01-普通）
     */
    private String timelessType;

    /**
     *产品类型 （01-普通、02-冷链、03-生鲜）
     */
    private String productType;

    /**
     * 增值服务（DELIVER_CONTACT-派前电联,TRACE_PUSH-轨迹回传;PRIVACY_SURFACE_SINGLE-隐私面单标）
     */
    private List<String> serviceTypeList;

    /**
     * 拓展字段
     */
    private Map<String,String> extendFieldMap;

    /**
     * 备注
     */
    private String remark;

    /**
     * 快递流向（01-正向订单)默认01
     */
    private String expressDirection;

    /**
     * 创建原因（01-客户创建）默认01
     */
    private String createChannel;

    /**
     * 必填
     *到件方所在县/区级行政区名称，必须是标准的县/区称谓，
     * 如：福田区，南涧彝族自治县、准格尔旗等
     */
    private String deliveryCounty;

    /**
     * 区域类型（01-国内）默认01
     */
    private String regionType;

    /**
     * 保价模型（保价服务必填）
     */
    private InsuredAnnex insuredAnnex;

    /**
     * 预估费用（散单业务使用）
     */
    private String expectValue;

    /**
     * 支付方式（1-现付；2-到付；3-月结）
     */
    private String payModel;

}
