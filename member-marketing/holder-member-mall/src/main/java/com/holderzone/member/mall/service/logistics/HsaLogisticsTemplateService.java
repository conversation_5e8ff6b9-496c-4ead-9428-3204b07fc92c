package com.holderzone.member.mall.service.logistics;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.qo.logistics.LogisticsTemplateQO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;

import java.util.List;

public interface HsaLogisticsTemplateService extends IHolderBaseService<HsaLogisticsTemplate> {

    List<LogisticsTemplateVO> list(LogisticsTemplateQO query);

    List<LogisticsTemplateVO> guidList(LogisticsTemplateQO query);

    String queryDefaultTemplateGuid(String operSubjectGuid);

    void clearDefaultFlag();
}
