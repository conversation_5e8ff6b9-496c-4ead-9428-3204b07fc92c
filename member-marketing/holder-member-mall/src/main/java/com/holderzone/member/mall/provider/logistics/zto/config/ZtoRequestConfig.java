package com.holderzone.member.mall.provider.logistics.zto.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "zto")
public class ZtoRequestConfig {

    private String appKey;

    private String appSecret;

    private String createUrl;

    private String cancelUrl;

    private String chargeUrl;

    private String trackUrl;
}
