package com.holderzone.member.mall.provider.logistics.sto;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.client.LogisticsRestClient;
import com.holderzone.member.mall.provider.logistics.converter.StoConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.logistics.sto.config.StoRequestConfig;
import com.holderzone.member.mall.provider.logistics.sto.domain.StoOrderCancel;
import com.holderzone.member.mall.provider.logistics.sto.domain.StoOrderCharge;
import com.holderzone.member.mall.provider.logistics.sto.domain.StoOrderCreate;
import com.holderzone.member.mall.provider.logistics.sto.domain.StoOrderTrack;
import com.holderzone.member.mall.provider.logistics.sto.domain.response.OrderChargeResponse;
import com.holderzone.member.mall.provider.logistics.sto.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.sto.domain.response.OrderTrackResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * 申通快递服务(<a href="https://open.sto.cn/#/help"/>)
 */
@Service
@Slf4j
@AllArgsConstructor
public class StoService extends AbstractLogisticsService {

    private final StoRequestConfig stoRequestConfig;

    private final LogisticsRestClient restClient;

    @Override
    public ExpressTypeEnum type() {
        return ExpressTypeEnum.STO;
    }

    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        StoOrderCreate stoOrderCreate = StoConverter.orderCreateConvertDo(orderCreateDTO);
        stoOrderCreate.setOrderSource(stoRequestConfig.getOrderSource());
        String requestData = JSONObject.toJSONString(stoOrderCreate);
        log.info("申通创建订单请求参数：{}",requestData);
        String rsp = remote(stoRequestConfig.getCreate(), requestData);
        log.info("申通创建订单返回参数：{}",rsp);
        return OrderCreateResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        StoOrderCancel stoOrderCancel = StoConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        stoOrderCancel.setOrderSource(stoRequestConfig.getOrderSource());
        String requestData = JSONObject.toJSONString(stoOrderCancel);
        log.info("申通取消订单请求参数：{}",requestData);
        String rsp = remote(stoRequestConfig.getCancel(), requestData);
        log.info("申通取消订单返回参数：{}",rsp);
        return null;
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        StoOrderTrack stoOrderTrack = StoConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        String requestData = JSONObject.toJSONString(stoOrderTrack);
        log.info("申通查询订单轨迹请求参数：{}",requestData);
        String rsp = remote(stoRequestConfig.getTrack(), requestData);
        log.info("申通查询订单轨迹返回参数：{}",rsp);
        return OrderTrackResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        StoOrderCharge stoOrderCharge = StoConverter.orderChargeConvertDo(orderChargeDTO);
        String requestData = JSONObject.toJSONString(stoOrderCharge);
        log.info("申通查询订单实效运费请求参数：{}",requestData);
        String rsp = remote(stoRequestConfig.getCharge(), requestData);
        log.info("申通查询订单实效运费返回参数：{}",rsp);
        return OrderChargeResponse.buildCommonResponse(rsp);
    }

    private String remote(StoRequestConfig.BaseBusinessConfig baseBusinessConfig,String content){
        MultiValueMap<String, Object> requestMap = buildRequestMap(
                baseBusinessConfig.getApi(), baseBusinessConfig.getAppKey(), content);
        return restClient.postForMap(requestMap,stoRequestConfig.getUrl());
    }

    private MultiValueMap<String, Object> buildRequestMap(String api,String apiCode,String content){
        MultiValueMap<String, Object> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.set("from_appkey", stoRequestConfig.getAppKey());
        paramsMap.set("from_code", stoRequestConfig.getSourceCode());
        paramsMap.set("to_appkey", apiCode);
        paramsMap.set("to_code", apiCode);
        paramsMap.set("api_name", api);
        paramsMap.set("content", content);
        paramsMap.set("data_digest", getSignature(content,stoRequestConfig.getSecretKey()));
        return paramsMap;
    }

    private static String getSignature(String content, String secretKey) {
        String text = content + secretKey;
        byte[] md5 = DigestUtils.md5(text);
        return Base64.encodeBase64String(md5);
    }

}
