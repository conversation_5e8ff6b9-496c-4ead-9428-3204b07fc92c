package com.holderzone.member.mall.controller.logistics;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.logistics.LogisticsProductReqDTO;
import com.holderzone.member.common.dto.logistics.LogisticsTemplateReqDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.logistics.LogisticsCommodityQO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.manage.LogisticsTemplateManage;
import com.holderzone.member.mall.manage.bo.LogisticsProductBO;
import com.holderzone.member.mall.manage.bo.LogisticsTemplateBO;
import com.holderzone.member.mall.manage.builder.LogisticsProductBizBuilder;
import com.holderzone.member.mall.manage.builder.LogisticsTemplateBizBuilder;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * 运费模板
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/logistics/template")
public class LogisticsController {

    private final HsaLogisticsTemplateService logisticsTemplateService;

    private final LogisticsTemplateManage logisticsTemplateManage;

    private final LogisticsCacheSupport logisticsCacheSupport;

    @PostMapping(value = "/page")
    @ApiOperation(value = "运费模板列表查询")
    public Result<List<LogisticsTemplateVO>> list() {
        return Result.success(logisticsTemplateManage.list());
    }

    @GetMapping(value = "/guid/list")
    @ApiOperation(value = "运费模板列表查询,选择模板时")
    public Result<List<LogisticsTemplateVO>> guidList(String templateGuid) {
        return Result.success(logisticsTemplateManage.guidList(templateGuid));
    }

    @GetMapping(value = "/{guid}")
    @ApiOperation(value = "运费模板查询")
    public Result<LogisticsTemplateDetailsVO> getById(@PathVariable String guid) {
        return Result.success(logisticsTemplateManage.get(guid));
    }

    @PostMapping(value = "/save")
    @ApiOperation(value = "添加运费模板")
    public Result<Void> save(@RequestBody LogisticsTemplateReqDTO reqDTO) {
        LogisticsTemplateBO biz = LogisticsTemplateBizBuilder.build(reqDTO);
        logisticsTemplateManage.save(biz);
        return Result.success();
    }

    @PostMapping(value = "/update")
    @ApiOperation(value = "修改运费模板")
    public Result<Void> update(@RequestBody LogisticsTemplateReqDTO reqDTO) {
        LogisticsTemplateBO biz = LogisticsTemplateBizBuilder.build(reqDTO);
        logisticsTemplateManage.update(biz);
        return Result.success();
    }

    @DeleteMapping(value = "/{guid}")
    @ApiOperation(value = "删除运费模板")
    public Result<Void> remove(@PathVariable String guid) {
        logisticsTemplateManage.remove(guid);
        return Result.success();
    }

    @PostMapping(value = "/product/page")
    @ApiOperation(value = "关联商品查询")
    public Result<PageResult> productPage(@RequestBody LogisticsCommodityQO query) {
        return Result.success(logisticsTemplateManage.pageLogisticsCommodity(query));
    }

    @PostMapping(value = "/product/choice")
    @ApiOperation(value = "选择关联商品")
    public Result<PageResult> choiceProduct(@RequestBody LogisticsCommodityQO query) {
        return Result.success(logisticsTemplateManage.pageExcludeLogisticsCommodity(query));
    }

    @PostMapping(value = "/product/save")
    @ApiOperation(value = "关联商品")
    public Result<Void> saveCommodity(@RequestBody LogisticsProductReqDTO reqDTO) {
        LogisticsProductBO biz = LogisticsProductBizBuilder.build(reqDTO);
        logisticsTemplateManage.saveCommodity(biz);
        return Result.success();
    }

    @PostMapping(value = "/product/update")
    @ApiOperation(value = "商品修改运费模板")
    public Result<Void> updateCommodity(@RequestBody LogisticsProductReqDTO reqDTO) {
        logisticsTemplateManage.updateCommodity(reqDTO.getTemplateGuid(), reqDTO.getCommodityCode());
        return Result.success();
    }

    @GetMapping(value = "/default")
    @ApiOperation(value = "查询默认模板guid")
    public String queryDefaultTemplateGuid(String operSubjectGuid) {
        return logisticsTemplateService.queryDefaultTemplateGuid(operSubjectGuid);
    }

    @GetMapping(value = "/freight/{commodityCode}/{productNum}/{province}/{city}")
    @ApiOperation(value = "获取商品运费")
    public Result<BigDecimal> getFreightAmount(@PathVariable("commodityCode") String commodityCode,
                                               @PathVariable("productNum") Integer productNum,
                                               @PathVariable("province") String province,
                                               @PathVariable("city") String city) {
        return Result.success(logisticsCacheSupport.getFreightAmount(commodityCode, productNum, province, city));
    }
}
