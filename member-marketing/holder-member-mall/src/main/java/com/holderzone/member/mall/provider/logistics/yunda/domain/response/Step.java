package com.holderzone.member.mall.provider.logistics.yunda.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Step {
    /**
     * 轨迹产生时间 格式 yyyy-MM-dd HH : mm : ss
     */
    private String time;

    /**
     * 当前状态节点状态status
     */
    private String status;

    /**
     * 节点状态 见下面节点状态action说明
     */
    private String action;

    /**
     * 当前城市
     */
    private String city;

    /**
     * 下级站点城市
     */
    private String next_city;

    /**
     * 轨迹发生站点编码
     */
    private String station;

    /**
     * 轨迹发生站点名称
     */
    private String station_name;

    /**
     * 轨迹发生站点类型1是网点，2是分拨中心
     */
    private String station_type;

    /**
     * 下级站点编码
     */
    private String next;

    /**
     * 下级站点名称
     */
    private String next_name;

    /**
     * 下级站点类型 1是网点，2是分拨中心
     */
    private String next_type;

    /**
     * 轨迹描述信息
     */
    private String description;

    /**
     * 快递员电话
     */
    private String phone;

    /**
     * 签收人
     */
    private String signer;
}
