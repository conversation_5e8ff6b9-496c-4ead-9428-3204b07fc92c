package com.holderzone.member.mall.provider.logistics.yunda.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SpecialEnum {


    FILE("0", "文件类"),

    ELECTRONIC_PRODUCT("1", "电子产品类(包括家用电器)"),

    OFFICE_CLOTHING_CLOTHING("2", "办公用品类, 服装鞋帽，箱包类"),

    BEAUTY_PRODUCTS("3", "化妆品，美容产品类"),

    VALUABLE_JEWELRY("4", "珠宝，手表，眼镜，贵重饰品类"),

    FOOD_HEALTH("5", "食品，保健药品类"),

    CRAFTS("6", "工艺品类(包括瓷器，茶具，烹饪用品)"),

    TOY_MUSICAL_INSTRUMENT("7", "玩具乐器类"),

    OTHER("8", "其他类"),

    ;

    /**
     * 编号
     */
    private final String code;

    /**
     * 描述
     */
    private final String reason;



}
