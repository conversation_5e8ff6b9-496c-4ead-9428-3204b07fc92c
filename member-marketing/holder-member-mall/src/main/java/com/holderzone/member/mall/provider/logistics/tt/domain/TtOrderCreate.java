package com.holderzone.member.mall.provider.logistics.tt.domain;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class TtOrderCreate {

    /**
     * 必填
     * 包裹物流单号
     */
    private String tracking_number;

    /**
     * 必填
     * 物流商对应的唯一简码
     */
    private String courier_code;

    /**
     * 必填
     * 目的国的二字简码
     */
    private String destination_code;

    /**
     * 包裹名称
     */
    private String title;

    /**
     * 自定义字段，用于填写物流渠道（比如某货代商）
     */
    private String logistics_channel;

    /**
     * 客户姓名
     */
    private String customer_name;

    /**
     * 客户邮箱
     */
    private String customer_email;

    /**
     *顾客接收短信的手机号码。手机号码的格式应该为：“+区号手机号码”（例子：+8612345678910）
     */
    private String customer_phone;

    /**
     * 包裹的订单号，由商家/平台所产生的订单编号
     */
    private String order_number;

    /**
     * 包裹发货时间（例子：2020-09-17 16:51）
     */
    private String shipping_date;

    /**
     * 包裹的发货时间，其格式为：YYYYMMDD，有部分的物流商（如 deutsch-post）需要这个参数（例子：********）
     */
    private String tracking_shipping_date;

    /**
     * 收件人所在地邮编，仅有部分的物流商（如 postnl-3s）需要这个参数
     */
    private String tracking_postal_code;

    /**
     * 目的国对应的二字简码，部分物流商（如postnl-3s)
     */
    private String tracking_destination_code;

    /**
     * 物流商的官方账号，仅有部分的物流商（如 dynamic-logistics）
     */
    private String tracking_courier_account;

    /**
     *查询结果的语言（例子：cn, en），若未指定该参数，结果会以英文或中文呈现
     */
    private String lang;

    /**
     * 备注，可自定义
     */
    private String note;

}
