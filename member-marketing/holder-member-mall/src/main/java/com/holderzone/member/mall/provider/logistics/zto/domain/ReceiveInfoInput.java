package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ReceiveInfoInput {

    /**
     * 必填
     * 收件人座机（与receiverMobile二者不能同时为空）
     */
    private String receiverPhone;

    /**
     * 必填
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 必填
     * 收件人详细地址
     */
    private String receiverAddress;

    /**
     * 必填
     * 收件人区
     */
    private String receiverDistrict;
    /**
     * 收件人手机号（与receiverPhone二者不能同时为空）
     */
    private String receiverMobile;
    /**
     * 必填
     * 收件人省
     */
    private String receiverProvince;
    /**
     * 必填
     * 收件人市
     */
    private String receiverCity;
}
