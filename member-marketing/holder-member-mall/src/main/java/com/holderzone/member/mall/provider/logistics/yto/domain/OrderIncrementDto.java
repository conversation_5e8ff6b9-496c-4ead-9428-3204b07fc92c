package com.holderzone.member.mall.provider.logistics.yto.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: member-marketing
 * @description: 增值服务
 * @author: pan tao
 * @create: 2022-08-30 11:01
 */
@Data
public class OrderIncrementDto {

    /**
     * 必填
     * 增值类型：1-代收货款；2-到付；4-保价，注：增值服务不能同时选择代收和到付。
     */
    private Integer type;

    /**必填
     * 金额，单位：元，代收货款金额：[3,20000]；到付金额：[1,5000]；保价金额：[100,30000]
     */
    private BigDecimal amount;

}
