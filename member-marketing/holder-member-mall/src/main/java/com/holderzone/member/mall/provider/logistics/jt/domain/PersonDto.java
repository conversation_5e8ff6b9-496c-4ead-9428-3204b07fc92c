package com.holderzone.member.mall.provider.logistics.jt.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PersonDto {

    /**
     * 必填
     * 姓名
     */
    private String name;

    /**
     * 公司
     */
    private String company;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 邮箱
     */
    private String mailBox;

    /**
     * 必填
     * 手机（手机和电话二选一必填）
     */
    private String mobile;

    /**
     * 必填
     * 电话（手机和电话二选一必填）
     */
    private String phone;

    /**
     * 国家三字码（如：中国=CHN、印尼=IDN）
     */
    private String countryCode;

    /**
     * 必填
     * 省份
     */
    private String prov;

    /**
     * 必填
     * 城市
     */
    private String city;

    /**
     * 必填
     * 区域
     */
    private String area;

    /**
     * 乡镇
     */
    private String town;

    /**
     * 街道
     */
    private String street;

    /**
     * 必填
     * 详细地址（省+市+区县+详细地址）
     */
    private String address;
}
