package com.holderzone.member.mall.controller.permission;

import com.holderzone.member.common.constant.PermissionResult;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.enums.SystemPermissionEnum;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.mall.service.permission.HsaSubjectPermissionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/member_permission")
public class PermissionController {

    @Resource
    private HsaSubjectPermissionService hsaSubjectPermissionService;

    @ApiOperation("获取当前用户操作权限")
    @GetMapping("/get_account_permission")
    public Result<MemberSystemPermissionVO> getAccountPermission(){
        String memberMall = SystemPermissionEnum.MEMBER_MALL.getDes();
        return Result.success(hsaSubjectPermissionService.getAccountPermission(memberMall));
    }

    @ApiOperation("通过账户和企业id查询当前用户运营主体权限")
    @PostMapping("/get_mall_oper_subject")
    public PermissionResult getMarketingOperSubjectPermission(@RequestBody @Validated OperSubjectPermissionQO request){
        request.setSourceType(SystemPermissionEnum.MEMBER_MALL.getCode());
        return PermissionResult.success(hsaSubjectPermissionService.getSubjectPermissionList(request));
    }

    @ApiOperation("修改当前账户中运营主体权限")
    @PostMapping("/update_mall_oper_subject")
    public PermissionResult updateMarketingOperSubjectPermission(@RequestBody HsaOperSubjectPermissionQO data){
        data.setSourceType(SystemPermissionEnum.MEMBER_MALL.getCode());
        return PermissionResult.isSuccess(hsaSubjectPermissionService.updateSubjectPermission(data));
    }

    /**
     * 获取运营主体
     *
     * @return Result
     */
    @ApiOperation("获取基础数据")
    @GetMapping(value = "/getOperatingSubjectInfo", produces = "application/json;charset=utf-8")
    public Result<OperSubjectInfoVO> getOperatingSubjectInfo() {
        return Result.success(hsaSubjectPermissionService.getOperatingSubject());
    }

}
