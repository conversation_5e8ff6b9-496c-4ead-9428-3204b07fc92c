package com.holderzone.member.mall.provider.logistics.sf.client;

import cn.hutool.core.codec.Base64;
import com.holderzone.member.common.exception.MemberBaseException;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 远程调用顺丰服务
 * <AUTHOR>
 */
@Slf4j
public class SfClient {

    private final String partnerId;

    private final String appSecret;

    private final String url;

    public SfClient(String partnerId, String appSecret, String url) {
        this.partnerId = partnerId;
        this.appSecret = appSecret;
        this.url = url;
    }

    public String remote(String serviceCode, String data){
        try {
            Map<String, String> params = new HashMap<>();

            Long timestamp = System.currentTimeMillis();
            //这个是接口对应的service code
            params.put("serviceCode", serviceCode);

            params.put("partnerID", partnerId);
            params.put("requestID", UUID.randomUUID().toString());
            params.put("timestamp", String.valueOf(timestamp));
            params.put("msgDigest", genDigest(timestamp.toString(), data, appSecret));
            params.put("msgData", data);

           return sendPostForm(url, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private final static String ENC = "UTF-8";
    public static String genDigest(String timestamp, String mgsData, String md5key){
        //将业务报文+时间戳+秘钥组合成需加密的字符串(注意顺序)
        String toVerifyText = mgsData + timestamp + md5key;

        //因业务报文中可能包含加号、空格等特殊字符，需要urlEnCode处理
        try {
            toVerifyText = URLEncoder.encode(toVerifyText, ENC);
            //进行Md5加密
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(toVerifyText.getBytes(StandardCharsets.UTF_8));
            byte[] md = md5.digest();

            //通过BASE64生成数字签名
            String msgDigest = Base64.encode(md);

            return msgDigest;
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
            throw new MemberBaseException();
        }
    }

    public static String sendPostForm(String url, Map<String, String> param) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();

        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());

            StringBuilder body = new StringBuilder();
            for (Map.Entry<String, String> entry : param.entrySet()) {
                body.append("&").append(entry.getKey()).append("=").append(URLEncoder.encode(param.get(entry.getKey()), ENC));
            }
            body = new StringBuilder(body.substring(1));
            // 发送请求参数
            out.print(body);
            log.info("远程调用顺丰请求参数 , url:{},body:{}", url, body);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            return result.toString();
        } catch (Exception e) {
            log.error("远程调用顺丰失败, url={}|param={}|e={}", url, param, e);
            return result.toString();
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("sendPostForm close is error, url={}, params={}", url, param, ex);
            }
        }
    }
}
