package com.holderzone.member.mall.provider.logistics.yto.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-08-30 11:03
 */
@Data
public class OrderGoodsDto {

    /**
     * 必填
     * 物品名称
     */
    private String name;

    /**
     * 	重量，单位：千克
     */
    private BigDecimal weight;

    /**
     * 长，单位：米
     */
    private BigDecimal length;

    /**
     * 宽，单位：米
     */
    private BigDecimal width;

    /**
     * 高，单位：米
     */
    private BigDecimal height;

    /**
     * 单价，单位：元
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer quantity;
}
