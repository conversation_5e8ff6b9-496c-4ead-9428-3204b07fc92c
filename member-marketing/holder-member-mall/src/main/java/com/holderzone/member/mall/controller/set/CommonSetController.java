package com.holderzone.member.mall.controller.set;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.mall.set.CommonSetDTO;
import com.holderzone.member.mall.manage.CommonSetManage;
import com.holderzone.member.mall.service.set.HsaCommonSetService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 通用设置控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/common_set")
public class CommonSetController {

    private CommonSetManage commonSetManage;

    /**
     * 更新
     */
    @PostMapping("/update")
    public Result<Void> update(@RequestBody CommonSetDTO commonSetDTO) {
        commonSetManage.updateCommonSet(commonSetDTO);
        return Result.success();
    }

    /**
     * 查询
     */
    @GetMapping("/query")
    public Result<CommonSetDTO> query(@RequestParam("memberInfoGuid") String memberInfoGuid) {
        return Result.success(commonSetManage.query(memberInfoGuid));
    }
}
