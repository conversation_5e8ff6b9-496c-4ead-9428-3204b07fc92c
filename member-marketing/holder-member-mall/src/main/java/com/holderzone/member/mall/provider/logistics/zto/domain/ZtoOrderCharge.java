package com.holderzone.member.mall.provider.logistics.zto.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class ZtoOrderCharge {

    /**
     * 必填
     * 产品编码（1：尊享VIP）
     */
    private Integer transportType;

    /**
     * 必填
     * 发件人信息
     */
    private AddressInfo sender;

    /**
     * 必填
     * 收件人信息
     */
    private AddressInfo addresser;

    /**
     * 必填
     * 重量（单位：千克），保留两位小数
     */
    private double weight;

}
