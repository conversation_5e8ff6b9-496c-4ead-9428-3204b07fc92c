package com.holderzone.member.mall.service.logistics.impl;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.dto.logistics.KdhResponse;
import com.holderzone.member.common.dto.logistics.KdhTrackDetail;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.qo.logistics.ExpressTrackQO;
import com.holderzone.member.mall.config.KdhConfig;
import com.holderzone.member.mall.service.logistics.ExpressService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class ExpressServiceImpl implements ExpressService {

    private final KdhConfig kdhConfig;

    private final RestTemplate restTemplate;

    @Override
    public List<KdhTrackDetail> queryExpress(ExpressTrackQO expressTrackQO){
        return remoteKdh(expressTrackQO.getLogisticsNumber(), ExpressTypeEnum.getStringCodeByName(expressTrackQO.getLogisticsName()));
    }

    private List<KdhTrackDetail> remoteKdh(String logisticsNumber, String logisticsCode) {
        String time = String.valueOf(System.currentTimeMillis() / 1000);

        // 设置请求参数
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("app_id",kdhConfig.getAppId());
        postParameters.add("method",kdhConfig.getMethod());
        postParameters.add("ts",time);
        postParameters.add("sign",buildSign(time));
        postParameters.add("data",KdhQuery.buildJsonString(logisticsNumber,logisticsCode,kdhConfig.getResultSort()));
        HttpHeaders headers = new HttpHeaders();
        MediaType mediaType = MediaType.parseMediaType("application/x-www-form-urlencoded; charset=UTF-8");
        headers.setContentType(mediaType);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(postParameters, headers);
        try {
            String result = restTemplate.postForObject(kdhConfig.getUrl(), entity, String.class);
            log.info("请求快递100返回参数：{}",result);
            if(result == null){
                return Collections.emptyList();
            }
            KdhResponse kdhResponse = JSON.parseObject(result, KdhResponse.class);
            if(kdhResponse.getCode() != 0){
                throw new MallBaseException(kdhResponse.getMsg());
            }
            return kdhResponse.parseTrackDetail();
        }catch (HttpClientErrorException e){
            log.info("请求快递100失败：{}",e.getResponseBodyAsString());
            return Collections.emptyList();
        }
    }

    private String buildSign(String time) {
       return DigestUtils.md5Hex((kdhConfig.getAppId() + kdhConfig.getMethod() + time + kdhConfig.getAppKey()).getBytes(StandardCharsets.UTF_8));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class KdhQuery{
        private String waybill_no;

        private String exp_company_code;

        private String result_sort;

        public static String buildJsonString(String waybillNo,String companyCode,String sort){
            return JSON.toJSONString(new KdhQuery(waybillNo,companyCode,sort));
        }
    }
}
