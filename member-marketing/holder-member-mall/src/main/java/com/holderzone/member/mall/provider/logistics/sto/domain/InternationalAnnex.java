package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InternationalAnnex {

    /**
     * 必填
     * 国际业务类型（01-国际进口，02-国际保税，03-国际直邮)
     */
    private String internationalProductType;

    /**
     * 必填
     * 是否报关，默认为否
     */
    private Boolean customsDeclaration;

    /**
     * 必填
     * 发件人所在国家，国际件为必填字段
     */
    private String senderCountry;

    /**
     * 收件人所在国家，国际件为必填字段
     */
    private String receiverCountry;

}
