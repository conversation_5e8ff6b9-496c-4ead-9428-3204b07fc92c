package com.holderzone.member.mall.service.shoppingcart.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.mall.shoppingcart.CartAddNumDTO;
import com.holderzone.member.common.dto.mall.shoppingcart.ShoppingCartSkuDTO;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;
import com.holderzone.member.mall.mapper.shoppingcart.HsaShoppingCartCommodityMapper;
import com.holderzone.member.mall.service.shoppingcart.IHsaShoppingCartCommodityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 购物车商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@Service
public class HsaShoppingCartCommodityServiceImpl extends ServiceImpl<HsaShoppingCartCommodityMapper, HsaShoppingCartCommodity>
        implements IHsaShoppingCartCommodityService {

    @Autowired
    private HsaShoppingCartCommodityMapper shoppingCartCommodityMapper;

    /**
     * 改变购物车商品数量
     *
     * @param cartAddNumDTO 改变购物车商品实体
     */
    @Override
    public void changeNum(CartAddNumDTO cartAddNumDTO) {
        if (cartAddNumDTO.getType() != 1 && (cartAddNumDTO.getChangeNum() == null || cartAddNumDTO.getChangeNum() < 1)) {
            cartAddNumDTO.setChangeNum(1);
        }
        shoppingCartCommodityMapper.changeNum(cartAddNumDTO);
    }

    /**
     * 删除主体下所有商品
     *
     * @param operSubjectGuid 运营主体
     */
    @Override
    public void removeAll(String operSubjectGuid) {
        this.remove(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaShoppingCartCommodity::getIsDelete, 0)
        );
    }

    @Override
    public List<HsaShoppingCartCommodity> listShoppingCartCommodity(String memberInfoGuid, String operSubjectGuid) {
        return this.list(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getOperSubjectGuid, operSubjectGuid)
                .eq(HsaShoppingCartCommodity::getMemberInfoGuid, memberInfoGuid)
                .orderByDesc(HsaShoppingCartCommodity::getUpdateTime));
    }

    @Override
    public List<HsaShoppingCartCommodity> listShoppingCartCommodity(List<String> shoppingCartGuidList) {
        if (CollectionUtils.isEmpty(shoppingCartGuidList)) {
            return Collections.emptyList();
        }
        return shoppingCartCommodityMapper.listShoppingCartCommodity(shoppingCartGuidList);
    }

    @Override
    public List<Integer> listStoreId(String memberInfoGuid, String operSubjectGuid) {
        return shoppingCartCommodityMapper.listStoreId(memberInfoGuid, operSubjectGuid);
    }

    @Override
    public Integer queryShoppingCartCommodityNum(String memberInfoGuid, String operSubjectGuid) {
        return this.count(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaShoppingCartCommodity::getOperSubjectGuid, operSubjectGuid));
    }

    @Override
    public void removeByShoppingCartGuidList(List<String> shoppingCartGuidList) {
        this.remove(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .in(HsaShoppingCartCommodity::getGuid, shoppingCartGuidList)
                .eq(HsaShoppingCartCommodity::getIsDelete, 0)
        );
    }

    @Override
    public HsaShoppingCartCommodity queryRepeatCommodity(String operSubjectGuid, String skuJson, String attrJson,
                                                         String memberInfoGuid, Long commodityId) {
        List<HsaShoppingCartCommodity> list = this.list(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getOperSubjectGuid, ThreadLocalCache.getOperSubjectGuid())
                .eq(HsaShoppingCartCommodity::getAttr, attrJson)
                .eq(HsaShoppingCartCommodity::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaShoppingCartCommodity::getCommodityId, commodityId));
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        HsaShoppingCartCommodity resp = null;
        for (HsaShoppingCartCommodity one : list) {
            String sku = one.getSku();
            List<ShoppingCartSkuDTO> skuList = JSON.parseArray(sku, ShoppingCartSkuDTO.class);
            StringBuilder skuIdList = new StringBuilder();
            skuList.forEach(e -> {
                if (CollUtil.isNotEmpty(e.getSkuIdList())) {
                    skuIdList.append(JSON.toJSONString(e.getSkuIdList()));
                }
            });
            if (ObjectUtil.equal(skuJson, skuIdList.toString())) {
                resp = one;
                break;
            }
        }
        return resp;
    }

    @Override
    public HsaShoppingCartCommodity queryByGuid(String shoppingCartGuid) {
        return this.getOne(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getGuid, shoppingCartGuid)
                .eq(HsaShoppingCartCommodity::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    @Override
    public List<HsaShoppingCartCommodity> listByGuid(List<String> shoppingCartGuidList) {
        return this.list(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .in(HsaShoppingCartCommodity::getGuid, shoppingCartGuidList)
                .eq(HsaShoppingCartCommodity::getIsDelete, BooleanEnum.FALSE.getCode()));
    }

    @Override
    public Integer countLimit(String memberInfoGuid) {
        return this.count(new LambdaQueryWrapper<HsaShoppingCartCommodity>()
                .eq(HsaShoppingCartCommodity::getMemberInfoGuid, memberInfoGuid)
                .eq(HsaShoppingCartCommodity::getIsDelete, BooleanEnum.FALSE.getCode()));
    }
}
