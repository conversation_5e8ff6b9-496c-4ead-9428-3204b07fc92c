package com.holderzone.member.mall.provider.logistics.sf;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.converter.SfConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.logistics.sf.client.SfClient;
import com.holderzone.member.mall.provider.logistics.sf.config.SfRequestConfig;
import com.holderzone.member.mall.provider.logistics.sf.domain.SfOrderCancel;
import com.holderzone.member.mall.provider.logistics.sf.domain.SfOrderCreate;
import com.holderzone.member.mall.provider.logistics.sf.domain.SfOrderTrack;
import com.holderzone.member.mall.provider.logistics.sf.domain.response.OrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.sf.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.sf.domain.response.OrderTrackResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 顺丰快递服务(<a href="https://open.sf-freight.com/api/home"/>)
 * TODO 注意:官方文档没有运费查询接口 需联系顺丰技术群进行支持
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class SfService extends AbstractLogisticsService {

    private final SfClient client;

    private final SfRequestConfig sfRequestConfig;


    public ExpressTypeEnum type() {
        return ExpressTypeEnum.SF;
    }


    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        SfOrderCreate sfOrderCreate = SfConverter.orderCreateConvertDo(orderCreateDTO);
        String requestData = JSONObject.toJSONString(sfOrderCreate);
        log.info("顺丰创建订单请求参数：{}",requestData);
        String rsp = client.remote(sfRequestConfig.getCreateServiceCode(), requestData);
        log.info("顺丰创建订单返回参数：{}",rsp);
        return OrderCreateResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        SfOrderCancel sfOrderCancel = SfConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        String requestData = JSONObject.toJSONString(sfOrderCancel);
        log.info("顺丰取消订单请求参数：{}",requestData);
        String rsp = client.remote(sfRequestConfig.getCancelServiceCode(), requestData);
        log.info("顺丰取消订单返回参数：{}",rsp);
        return OrderCancelResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        SfOrderTrack sfOrderTrack = SfConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        String requestData = JSONObject.toJSONString(sfOrderTrack);
        log.info("顺丰查询订单轨迹请求参数：{}",requestData);
        String rsp = client.remote(sfRequestConfig.getTrackServiceCode(), requestData);
        log.info("顺丰查询订单轨迹返回参数：{}",rsp);
        return OrderTrackResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        return super.queryLogisticsCharge(orderChargeDTO);
    }
}
