package com.holderzone.member.mall.provider.logistics.sf.domain;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class Package {

    /**
     * 运单号或子单号
     */
    private String waybillNo;

    /**
     * 箱号
     */
    private String boxNo;

    /**
     * 包裹长，单位为厘米
     */
    private BigDecimal length;

    /**
     * 包裹高，单位为厘米
     */
    private BigDecimal height;
    /**
     * 包裹宽，单位为厘米
     */
    private BigDecimal width;
    /**
     * 包裹体积
     */
    private BigDecimal volume;
    /**
     * 包裹重量
     */
    private BigDecimal weight;

    /**
     * 包裹重量单位，默认为KG
     */
    private String unitWeight;

    /**
     * 体积单位, 默认立方厘米
     */
    private String unitVolume;

}
