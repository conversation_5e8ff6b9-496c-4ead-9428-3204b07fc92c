package com.holderzone.member.mall.controller;


import com.alibaba.fastjson.annotation.JSONField;
import java.util.Base64;
import com.holderzone.member.common.annotation.RedissonLock;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.FileConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.excel.FileVO;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.exception.FileIllegalException;
import com.holderzone.member.common.util.verify.VerifyUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @className MemberFileController
 */
@RestController
@RequestMapping("/file/member")
@ApiModel(description = "会员上传文件接口")
public class MemberFileController {

    private static final Logger logger = LoggerFactory.getLogger(MemberFileController.class);

    /**
     * 卡背景图片有效格式
     */
    private static final List<String> VALID_IMAGE_TYPE = Arrays.asList("jpeg", "jpg", "png", "mp3");

    /**
     * 背景音乐有效格式
     */
    private static final List<String> VALID_MUSIC_TYPE = Collections.singletonList("mp3");

    /**
     * 卡背景图片最大5M
     */
    private static final long IMAGE_BYTE_SIZE = 5242880;

    private static final long MUSIC_BYTE_SIZE = 10485760;

    @Autowired
    private FileOssService baseService;

    @ApiOperation(value = "会员导入接口,文件上传name 必须为file", notes = "单个文件不能超过5000条，上传成功返回文件的地址，失败返回空")
    @PostMapping("/memberUploadExcel")
    public Result<String> memberUploadExcel(@RequestParam(value = "file") MultipartFile file) {
        logger.info("文件上传， fileName={}", file.getOriginalFilename());
        // 仅校验商品图片
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1) : null;
        if (!FileConstant.CORRECT_TYPE_LIST.contains(fileType)) {
            logger.info("当前文件类型为：{}", fileType);
            throw new FileIllegalException(MemberAccountExceptionEnum.ERROR_FILE.getDes());
        }
        String upload = null;
        try {
            FileDto fileDto = new FileDto();
            fileDto.setFileContent(Base64.getEncoder().encodeToString(file.getBytes()));
            fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
            upload = baseService.upload(fileDto);
            logger.info("图片上传下载路径->>>>>{}", upload);
        } catch (IOException e) {
            logger.error("上传文件失败");
            e.printStackTrace();
        }
        return Result.success(upload);
    }


    /**
     * 图片上传
     *
     * @param fileInfo 文件
     */
    @SneakyThrows
    @ApiOperation(value = "上传卡背景图片(.jpeg .jpg .png且大小不超过5M)")
    @PostMapping("/uploadImage")
    @RedissonLock(lockName = "UPLOAD_IMAGE", leaseTime = 10, tryLock = true)
    public Result<String> uploadImage(FileInfo fileInfo){
        // 校验图片格式
        MultipartFile file = fileInfo.getFile();
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase() : null;
        VerifyUtil.verify(VALID_IMAGE_TYPE.contains(fileType), MemberAccountExceptionEnum.ERROR_CARD_IMAGE_TYPE);
        // 校验图片大小
        VerifyUtil.verify(file.getSize() <= IMAGE_BYTE_SIZE, MemberAccountExceptionEnum.ERROR_CARD_IMAGE_SIZE);
        // 上传图片
        FileDto fileDto = new FileDto();
        fileDto.setFileContent(Base64.getEncoder().encodeToString(file.getBytes()));
        fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
        String imagePath = baseService.upload(fileDto).replace("http", "https");
        return Result.success(imagePath);
    }

    /**
     * 音乐上传
     *
     * @param fileInfo 文件
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadMusic")
    @SneakyThrows
    public Result<FileVO> uploadMusic(FileInfo fileInfo) {
        FileVO fileVO = new FileVO();
        MultipartFile file = fileInfo.getFile();
        String fileName = file.getOriginalFilename();
        String fileType = fileName != null && fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase() : null;
        VerifyUtil.verify(VALID_MUSIC_TYPE.contains(fileType), MemberAccountExceptionEnum.ERROR_CARD_MUSIC_TYPE);
        VerifyUtil.verify(file.getSize() <= MUSIC_BYTE_SIZE, MemberAccountExceptionEnum.ERROR_CARD_MUSIC_SIZE);
        FileDto fileDto = new FileDto();
        fileDto.setFileContent(Base64.getEncoder().encodeToString(file.getBytes()));
        fileDto.setFileName(UUID.randomUUID().toString().replace("-", "").substring(0, 5) + "." + fileType);
        String imagePath = baseService.upload(fileDto).replace("http", "https");
        fileVO.setFileName(fileName);
        fileVO.setFileUrl(imagePath);
        return Result.success(fileVO);
    }

    @Data
    class FileInfo {
        // 请求时报序列化错误解决方案
        @JSONField(serialize = false)
        private MultipartFile file;
    }

}