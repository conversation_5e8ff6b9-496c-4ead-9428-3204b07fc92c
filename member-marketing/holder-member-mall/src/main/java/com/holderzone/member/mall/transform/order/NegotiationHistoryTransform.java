package com.holderzone.member.mall.transform.order;

import com.holderzone.member.common.dto.order.NegotiationHistoryDTO;
import com.holderzone.member.mall.entity.order.HsaRefundNegotiationHistory;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface NegotiationHistoryTransform {

    NegotiationHistoryTransform INSTANCE = Mappers.getMapper(NegotiationHistoryTransform.class);

    HsaRefundNegotiationHistory orderDTO2DO(NegotiationHistoryDTO negotiationHistory);

    List<HsaRefundNegotiationHistory> orderDTO2DOList(List<NegotiationHistoryDTO> buildList);
}
