package com.holderzone.member.mall.service.commodity;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.DistributionSetDTO;
import com.holderzone.member.mall.entity.commodity.HsaDistributionSet;

/**
 * @program: member-marketing
 * @description: ${description}
 * @author: pan tao
 * @create: 2022-08-30 18:00
 */
public interface HsaDistributionSetService extends IHolderBaseService<HsaDistributionSet> {

    /**
     * 保存、修改商家地址设置
     *
     * @param request 配送设置dto
     * @return 操作
     */
    boolean saveOrUpdateDistributionSet(DistributionSetDTO request);

    /**
     * 查询商家地址设置详情
     *
     * @param guid 配送设置guid
     * @return 查询结果
     */
    DistributionSetDTO queryDistributionSetDetail(String guid);

    /**
     * 查询商家地址设置列表
     *
     * @param request 查询列表请求参数
     * @return 查询结果
     */
    PageResult queryDistributionSetList(PageDTO request);

    /**
     * 删除商家地址设置
     *
     * @param guid guid
     * @return 操作结果
     */
    boolean deleteDistributionSet(String guid);

}
