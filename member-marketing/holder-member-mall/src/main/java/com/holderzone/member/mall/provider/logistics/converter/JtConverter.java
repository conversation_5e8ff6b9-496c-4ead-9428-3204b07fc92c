package com.holderzone.member.mall.provider.logistics.converter;

import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.mall.provider.logistics.jt.domain.*;

/**
 * <AUTHOR>
 */
public class JtConverter {

    private JtConverter(){
        throw new MemberBaseException();
    }

    private final static String BUSINESS_DIGEST = "qonqb4O1eNr6VCWS07Ieeg==";

    public static JtOrderCreate orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        JtOrderCreate.JtOrderCreateBuilder builder = JtOrderCreate.builder();
        builder.digest(orderCreateDTO.getLogisticsNo());
        //构建请求实体
        return builder.build();
    }

    public static JtOrderCancel orderCancelConvertDo(LogisticsOrderCancelDTO orderCancelDTO){
        JtOrderCancel.JtOrderCancelBuilder builder = JtOrderCancel.builder();
        builder.digest(orderCancelDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static JtOrderTrack orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        JtOrderTrack.JtOrderTrackBuilder builder = JtOrderTrack.builder();
        builder.billCodes(orderTrackDTO.getOrderNo());
        //构建请求实体
        return builder.build();
    }

    public static JtOrderCharge orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        JtOrderCharge.JtOrderChargeBuilder builder = JtOrderCharge.builder();
        builder.digest(orderChargeDTO.getOrderNo());
        return builder.build();
    }
}
