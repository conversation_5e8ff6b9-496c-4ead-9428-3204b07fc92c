package com.holderzone.member.mall.provider.logistics.zto;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.logistics.converter.ZtoConverter;
import com.holderzone.member.mall.provider.AbstractLogisticsService;
import com.holderzone.member.mall.provider.logistics.zto.config.ZtoRequestConfig;
import com.holderzone.member.mall.provider.logistics.zto.domain.ZtoOrderCancel;
import com.holderzone.member.mall.provider.logistics.zto.domain.ZtoOrderCharge;
import com.holderzone.member.mall.provider.logistics.zto.domain.ZtoOrderCreate;
import com.holderzone.member.mall.provider.logistics.zto.domain.ZtoOrderTrack;
import com.holderzone.member.mall.provider.logistics.zto.domain.response.OrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.zto.domain.response.OrderChargeResponse;
import com.holderzone.member.mall.provider.logistics.zto.domain.response.OrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.zto.domain.response.OrderTrackResponse;
import com.zto.zop.EncryptionType;
import com.zto.zop.ZopClient;
import com.zto.zop.ZopPublicRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 中通快递服务(<a href="https://open.zto.com/#/index"/>)
 * <AUTHOR>
 */
@Service
@Slf4j
public class ZtoService extends AbstractLogisticsService {

    @Resource
    private ZtoRequestConfig ztoRequestConfig;

    private ZopClient client;

    @PostConstruct
    void initClient(){
        client = new ZopClient(ztoRequestConfig.getAppKey(),ztoRequestConfig.getAppSecret());
    }


    public ExpressTypeEnum type() {
        return ExpressTypeEnum.ZTO;
    }

    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        ZtoOrderCreate ztoOrderCreate = ZtoConverter.orderCreateConvertDo(orderCreateDTO);
        String requestData = JSONObject.toJSONString(ztoOrderCreate);
        log.info("中通创建订单请求参数：{}",requestData);
        String rsp = remoteRequest(ztoRequestConfig.getCreateUrl(), requestData);
        log.info("中通创建订单返回参数：{}",rsp);
        //构建返回数据
        return OrderCreateResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        ZtoOrderCancel ztoOrderCancel = ZtoConverter.orderCancelConvertDo(logisticsOrderCancelDTO);
        String requestData = JSONObject.toJSONString(ztoOrderCancel);
        log.info("中通取消订单请求参数：{}",requestData);
        String rsp = remoteRequest(ztoRequestConfig.getCancelUrl(), requestData);
        log.info("中通取消订单返回参数：{}",rsp);
        //构建返回数据
        return OrderCancelResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        ZtoOrderTrack ztoOrderTrack = ZtoConverter.orderTrackConvertDo(logisticsOrderTrackDTO);
        String requestData = JSONObject.toJSONString(ztoOrderTrack);
        log.info("中通查询订单轨迹请求参数：{}",requestData);
        String rsp = remoteRequest(ztoRequestConfig.getTrackUrl(), requestData);
        log.info("中通查询订单轨迹请返回参数：{}",rsp);
        //构建返回数据
        return OrderTrackResponse.buildCommonResponse(rsp);
    }

    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        ZtoOrderCharge ztoOrderCharge = ZtoConverter.orderChargeConvertDo(orderChargeDTO);
        String requestData = JSONObject.toJSONString(ztoOrderCharge);
        log.info("中通取消订单请求参数：{}",requestData);
        String rsp = remoteRequest(ztoRequestConfig.getChargeUrl(), requestData);
        log.info("中通取消订单返回参数：{}",rsp);
        //构建返回数据
        return OrderChargeResponse.buildCommonResponse(rsp);
    }

    private String remoteRequest(String url,String requestData){
        try {
            ZopPublicRequest request = new ZopPublicRequest();
            request.setBody(requestData);
            request.setUrl(url);
            request.setTimestamp(null);
            request.setEncryptionType(EncryptionType.MD5);
            request.setBase64(Boolean.TRUE);
            return client.execute(request);
        }catch (Exception e){
            log.error("中通快递请求失败",e);
        }
        return null;
    }

}
