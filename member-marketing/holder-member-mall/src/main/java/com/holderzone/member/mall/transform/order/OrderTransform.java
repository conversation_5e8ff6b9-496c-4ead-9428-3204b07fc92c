package com.holderzone.member.mall.transform.order;

import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDetailDTO;
import com.holderzone.member.common.dto.order.MallOrderRefundDTO;
import com.holderzone.member.common.dto.pay.AggRefundDTO;
import com.holderzone.member.common.qo.mall.order.OrderRefundQO;
import com.holderzone.member.mall.entity.order.HsaMallBaseOrder;
import com.holderzone.member.mall.entity.order.HsaProductOrderDetail;
import org.mapstruct.Mapper;

import java.util.List;


@Mapper
public interface OrderTransform {

    MallBaseOrderDTO orderDO2DTO(HsaMallBaseOrder hsaMallBaseOrder);

    MallBaseOrderDetailDTO detailDO2DTO(HsaProductOrderDetail detail);

    List<MallBaseOrderDetailDTO> detailDOList2DTOList(List<HsaProductOrderDetail> details);

    MallOrderRefundDTO refundQO2DTO(OrderRefundQO refundQO);

    AggRefundDTO refundDTO2Agg(MallOrderRefundDTO dto);

}
