package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class StoOrderCancel {

    /**
     * 客户订单号（与运单号二者必传其一）ps：最好使用运单号进行取消
     */
    private String sourceOrderId;

    /**
     * 运单号（与客户订单号必传其一） ps：最好使用运单号进行取消
     */
    private String billCode;

    /**
     * 必填
     * 订单来源
     */
    private String orderSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 取消人姓名
     */
    private String creater;

    /**
     * 必填
     * 02:调度订单（默认值），01：普通订单
     */
    private String orderType;
}
