package com.holderzone.member.mall.provider.logistics.yunda.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum TrackStatusEnum {


    GOT("GOT", "已揽件"),

    TRANSIT("TRANSIT", "运输中"),

    SIGNED("SIGNED", "已签收"),

    RETURN("RETURN", "退回件"),

    SIGNFAIL("SIGNFAIL", "异常签收"),

    ;

    /**
     * 编号
     */
    private final String code;

    /**
     * 描述
     */
    private final String reason;



}
