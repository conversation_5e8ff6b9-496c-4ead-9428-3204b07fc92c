package com.holderzone.member.mall.provider;

import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;

/**
 * <AUTHOR>
 * 物流通用接口
 */
public interface LogisticsService {

    /**
     * 物流类型
     * @return 返回物流类型
     */
    ExpressTypeEnum type();

    /**
     * 创建物流订单
     * @param orderCreateDTO 物流订单创建参数
     */
    LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO);

    /**
     * 关闭物流订单
     * @param logisticsOrderCancelDTO 物流订单取消参数
     */
    LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO);

    /**
     * 物流轨迹查询
     * @param logisticsOrderTrackDTO 物流轨迹查询参数
     * @return 物流公共返回数据
     */
    LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO);

    /**
     * 运价查询接口
     * @param orderChargeDTO 运价查询参数
     * @return 物流公共返回数据
     */
    LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO);
}
