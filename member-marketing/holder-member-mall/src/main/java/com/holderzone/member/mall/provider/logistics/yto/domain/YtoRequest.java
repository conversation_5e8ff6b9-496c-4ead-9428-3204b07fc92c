package com.holderzone.member.mall.provider.logistics.yto.domain;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class YtoRequest {

    private String sign;

    private String timestamp;

    private String param;

    private String format;

    public YtoRequest(String sign, String timestamp, String param, String format) {
        this.sign = sign;
        this.timestamp = timestamp;
        this.param = param;
        this.format = format;
    }

    public YtoRequest(String sign, String param) {
        this.sign = sign;
        this.timestamp = String.valueOf(System.currentTimeMillis());
        this.param = param;
        this.format = "JSON";
    }

    public static String buildRequestData(String sign, String param){
        return JSONObject.toJSONString(new YtoRequest(sign,param));
    }
}
