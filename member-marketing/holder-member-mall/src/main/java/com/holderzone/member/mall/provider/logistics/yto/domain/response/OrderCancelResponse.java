package com.holderzone.member.mall.provider.logistics.yto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderCancelResponse implements Serializable {

    /**
     * 客户编码（K开头）
     */
    private String customerCode;

    /**
     * 物流单号，打印拉取运单号前，物流单号和渠道唯一确定一笔快递物流订单
     */
    private String logisticsNo;


    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCancelResponse buildCommonResponse(String json){
        OrderCancelResponse orderCancelResponse = JSONObject.parseObject(json, OrderCancelResponse.class);
        if(orderCancelResponse == null){
            return null;
        }
        //TODO
        return LogisticsOrderCancelResponse.builder().build();
    }

}
