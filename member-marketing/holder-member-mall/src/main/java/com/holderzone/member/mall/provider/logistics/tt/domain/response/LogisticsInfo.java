package com.holderzone.member.mall.provider.logistics.tt.domain.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class LogisticsInfo {

    /**
     * 物流商编码
     */
    private String courier_code;

    /**
     * 物流商官网上的电话
     */
    private String courier_phone;

    /**
     * 物流商的官网的链接
     */
    private String weblink;

    /**
     * 包裹对应的另一个单号，作用与当前单号相同（仅有少部分物流商提供）
     */
    private String reference_number;

    /**
     * 物流商接收包裹的时间（也称为上网时间）
     */
    private String received_date;

    /**
     * 包裹封发时间，封发指将多个小包裹打包成一个货物（方便运输）
     */
    private String dispatched_date;

    /**
     * 包裹离开此出发机场的时间
     */
    private String departed_airport_date;

    /**
     * 包裹达到目的国的时间
     */
    private String arrived_abroad_date;

    /**
     * 包裹移交给海关的时间
     */
    private String customs_received_date;

    /**
     * 包裹达到目的国、目的城市的时间
     */
    private String arrived_destination_date;

    /**
     * 包裹达到目的国、目的城市的时间
     */
    private List<TrackInfo> trackinfo;
}
