package com.holderzone.member.mall.controller.commodity;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.qo.mall.DistributionSetDTO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import com.holderzone.member.mall.service.commodity.HsaDistributionSetService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/commodity_set")
public class CommoditySetController {

    @Resource
    private HsaCommoditySetService hsaCommoditySetService;

    @Resource
    private HsaDistributionSetService hsaDistributionSetService;

    /**
     * 通过运营主体获取商品分享标签格式
     *
     * @param operSubjectGuid 运营主体
     * @return 商品分享格式
     */
    @GetMapping("/get_share_format")
    public Result<CommodityShareFormatVO> getShareFormat(@RequestParam("operSubjectGuid") String operSubjectGuid) {
        return Result.success(hsaCommoditySetService.getShareFormat(operSubjectGuid));
    }

    /**
     * 修改商品分享标签格式
     *
     * @param shareFormat 商品分享格式参数
     * @return 操作结果
     */
    @PostMapping("/update_share_format")
    public Result<Boolean> updateShareFormat(@RequestBody CommodityShareFormatVO shareFormat) {
        return Result.success(hsaCommoditySetService.updateShareFormat(shareFormat));
    }

    /**
     * 保存、修改商家地址设置
     *
     * @param request 配送设置dto
     * @return 查询结果
     */
    @PostMapping("/saveOrUpdateDistributionSet")
    public Result<Boolean> saveOrUpdateDistributionSet(@RequestBody DistributionSetDTO request) {
        return Result.success(hsaDistributionSetService.saveOrUpdateDistributionSet(request));
    }

    /**
     * 查询商家地址设置详情
     *
     * @param guid 配送设置guid
     * @return 查询结果
     */
    @GetMapping("/queryDistributionSetDetail")
    public Result queryDistributionSetDetail(String guid) {
        return Result.success(hsaDistributionSetService.queryDistributionSetDetail(guid));
    }

    /**
     * 查询商家地址设置列表
     *
     * @param request 查询列表请求参数
     * @return 查询结果
     */
    @PostMapping("/queryDistributionSetList")
    public Result queryDistributionSetList(@RequestBody PageDTO request) {
        return Result.success(hsaDistributionSetService.queryDistributionSetList(request));
    }

    /**
     * 删除商家地址设置
     *
     * @param guid guid
     * @return 操作结果
     */
    @PostMapping("/deleteDistributionSet")
    public Result<Boolean> deleteDistributionSet(String guid) {
        return Result.success(hsaDistributionSetService.deleteDistributionSet(guid));
    }


}
