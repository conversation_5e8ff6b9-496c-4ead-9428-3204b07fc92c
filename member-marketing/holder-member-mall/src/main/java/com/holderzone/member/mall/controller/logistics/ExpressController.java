package com.holderzone.member.mall.controller.logistics;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.logistics.KdhTrackDetail;
import com.holderzone.member.common.qo.logistics.ExpressTrackQO;
import com.holderzone.member.mall.service.logistics.ExpressService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/express")
@AllArgsConstructor
public class ExpressController {

    private final ExpressService expressService;

    @PostMapping(value = "/query_track")
    public Result<List<KdhTrackDetail>> queryTrack(@RequestBody ExpressTrackQO expressTrackQO){
        return Result.success(expressService.queryExpress(expressTrackQO));
    }

}
