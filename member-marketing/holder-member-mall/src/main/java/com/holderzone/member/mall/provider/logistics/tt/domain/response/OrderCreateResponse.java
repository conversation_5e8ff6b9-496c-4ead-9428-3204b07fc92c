package com.holderzone.member.mall.provider.logistics.tt.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateResponse;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderBase;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderCreateResponse extends TtOrderBase implements Serializable{

    /**
     * 大字/三段码
     */
    private String order_number;



    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCreateResponse buildCommonResponse(String rsp){
        TtBaseResponse<OrderCreateResponse> ttBaseResponse = JSONObject.parseObject(rsp, new TypeReference<TtBaseResponse<OrderCreateResponse>>(){}.getType());

        if(ttBaseResponse == null || ttBaseResponse.getData() == null){
            return null;
        }
        return LogisticsOrderCreateResponse.builder().build();
    }

}
