package com.holderzone.member.mall.provider.logistics.converter;

import com.best.javaSdk.kyCancelOrderNotify.request.KyCancelOrderNotifyReq;
import com.best.javaSdk.kyCreateOrderNotify.request.KyCreateOrderNotifyReq;
import com.best.javaSdk.kyPriceAgingSearch.request.KyPriceAgingSearchReq;
import com.best.javaSdk.kyTraceQuery.request.KyTraceQueryReq;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderChargeDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCreateDTO;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackDTO;
import com.holderzone.member.common.exception.MemberBaseException;

/**
 * <AUTHOR>
 */
public class BestConverter {

    private BestConverter(){
        throw new MemberBaseException();
    }

    public static KyCreateOrderNotifyReq orderCreateConvertDo(LogisticsOrderCreateDTO orderCreateDTO){
        KyCreateOrderNotifyReq createOrderReq = new KyCreateOrderNotifyReq();
        createOrderReq.setAcceptAddress(orderCreateDTO.getRecipientAddress());
        //构建请求实体
        return createOrderReq;
    }

    public static KyCancelOrderNotifyReq orderCancelConvertDo(LogisticsOrderCancelDTO logisticsOrderCancelDTO){
        KyCancelOrderNotifyReq kyCancelOrderNotifyReq = new KyCancelOrderNotifyReq();
        kyCancelOrderNotifyReq.setGmtCancel(logisticsOrderCancelDTO.getOrderNo());
        //构建请求实体
        return kyCancelOrderNotifyReq;
    }

    public static KyPriceAgingSearchReq orderChargeConvertDo(LogisticsOrderChargeDTO orderChargeDTO){
        KyPriceAgingSearchReq req = new KyPriceAgingSearchReq();
        req.setDestCityCode(orderChargeDTO.getOrderNo());
        //构建请求实体
        return req;
    }

    public static KyTraceQueryReq orderTrackConvertDo(LogisticsOrderTrackDTO orderTrackDTO){
        KyTraceQueryReq kyTraceQueryReq = new KyTraceQueryReq();
        kyTraceQueryReq.setLogisticID(orderTrackDTO.getOrderNo());
        //构建请求实体
        return kyTraceQueryReq;
    }

}
