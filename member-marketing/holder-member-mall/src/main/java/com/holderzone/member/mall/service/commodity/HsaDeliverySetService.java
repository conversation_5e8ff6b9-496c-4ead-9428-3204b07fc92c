package com.holderzone.member.mall.service.commodity;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.mall.entity.commodity.HsaDeliverySet;

import java.util.List;

/**
 * @description: 配送设置mapper
 * <AUTHOR>
 */
public interface HsaDeliverySetService extends IHolderBaseService<HsaDeliverySet> {

    /**
     * 通过运营主体获取商品配送基础设置
     * @param operSubjectGuid 运营主体
     * @return 配送设置
     */
    DeliverySetVO getDeliverySet(String operSubjectGuid);

    /**
     * 通过运营主体获取商品分享标签格式
     * @param deliverySet 商品配送基础参数
     * @return 操作结果
     */
    Boolean updateDeliverySet(DeliverySetVO deliverySet);

    /**
     * 初始化商品配送设置数据
     * 回调接口
     * @param operSubjectGuids 运营主体集合
     */
    void initDeliverySet(List<String> operSubjectGuids);

    /**
     * 更新快递发货状态
     * @param vo 快递发货状态
     */
    void updateExpressState(DeliverySetVO vo);
}
