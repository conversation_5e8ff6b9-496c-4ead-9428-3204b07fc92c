package com.holderzone.member.mall.provider;

import com.holderzone.member.common.dto.mall.logistics.*;
import com.holderzone.member.common.enums.mall.MemberMallExceptionEnum;
import com.holderzone.member.common.exception.MallBaseException;

/**
 * <AUTHOR>
 */
public abstract class AbstractLogisticsService implements LogisticsService{


    @Override
    public LogisticsOrderCreateResponse createOrder(LogisticsOrderCreateDTO orderCreateDTO) {
        throw new MallBaseException(MemberMallExceptionEnum.LOGISTICS_NOT_OPEND_SERVICE);
    }

    @Override
    public LogisticsOrderCancelResponse cancelOrder(LogisticsOrderCancelDTO logisticsOrderCancelDTO) {
        throw new MallBaseException(MemberMallExceptionEnum.LOGISTICS_NOT_OPEND_SERVICE);

    }

    @Override
    public LogisticsOrderTrackResponse queryOrderTrack(LogisticsOrderTrackDTO logisticsOrderTrackDTO) {
        throw new MallBaseException(MemberMallExceptionEnum.LOGISTICS_NOT_OPEND_SERVICE);
    }

    @Override
    public LogisticsOrderChargeResponse queryLogisticsCharge(LogisticsOrderChargeDTO orderChargeDTO) {
        throw new MallBaseException(MemberMallExceptionEnum.LOGISTICS_NOT_OPEND_SERVICE);
    }
}
