package com.holderzone.member.mall.service.commodity;

import com.holderzone.member.common.config.mybatis.IHolderBaseService;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.mall.entity.commodity.HsaCommoditySet;

import java.util.List;


/**
 * @description: 商品设置service
 * <AUTHOR>
 */
@Deprecated
public interface HsaCommoditySetService extends IHolderBaseService<HsaCommoditySet> {

    /**
     * 通过运营主体获取商品分享标签格式
     * @param operSubjectGuid 运营主体
     * @return 商品分享格式
     */
    CommodityShareFormatVO getShareFormat(String operSubjectGuid);

    /**
     * 通过运营主体获取商品分享标签格式
     * @param shareFormat 商品分享格式参数
     * @return 操作结果
     */
    Boolean updateShareFormat(CommodityShareFormatVO shareFormat);

    /**
     * 初始化商品分享标签格式数据
     * @param operSubjectGuids 运营主体集合
     */
    void initShareFormat(List<String> operSubjectGuids);
}
