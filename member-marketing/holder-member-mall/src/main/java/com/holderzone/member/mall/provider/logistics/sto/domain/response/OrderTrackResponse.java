package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse implements Serializable {


    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderTrackResponse buildCommonResponse(String rsp){
        StoBaseResponse<Map<String,List<CommonResultVO>>> stoBaseResponse = JSONObject.parseObject(rsp, new TypeReference<StoBaseResponse<Map<String,List<CommonResultVO>>>>(){}.getType());

        if(stoBaseResponse == null || stoBaseResponse.getData() == null){
            return null;
        }
        return LogisticsOrderTrackResponse.builder().build();
    }

}
