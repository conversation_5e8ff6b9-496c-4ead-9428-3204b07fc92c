package com.holderzone.member.mall.provider.logistics.jt.client;

import com.alibaba.fastjson.JSONObject;
import com.yl.jms.sdk.auth.ClientConfiguration;
import com.yl.jms.sdk.auth.Digests;
import com.yl.jms.sdk.http.HttpClientManager;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class JtClient {

    private final ClientConfiguration clientConfiguration;

    public JtClient(ClientConfiguration clientConfiguration) {
        this.clientConfiguration = clientConfiguration;
    }

    public String executor(String paramJson,String url){
        try {
            Map<String, String> headers = new HashMap<>(8);
            headers.put("digest", Digests.digestForHead(paramJson, this.clientConfiguration.getPrivateKey()));
            headers.put("timestamp", System.currentTimeMillis() + "");
            headers.put("apiAccount", this.clientConfiguration.getApiAccount());
            return HttpClientManager.getInstance().post(url, paramJson, headers);
        } catch (IOException e) {
            log.error("远程调用极兔失败，异常:",e);
            return null;
        }
    }
}
