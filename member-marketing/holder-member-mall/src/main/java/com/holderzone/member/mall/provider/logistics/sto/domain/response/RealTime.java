package com.holderzone.member.mall.provider.logistics.sto.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RealTime {

    /**
     * 实时单上门服务时间，单位为分钟
     */
    private String deliveryServiceId;

    /**
     * 能否选择，超过实时单下单时间则不能再下实时单
     */
    private Boolean selectable;

    /**
     * 实时单服务类型名称
     */
    private String name;

    /**
     * 不可选文案提示
     */
    private String selectDisableTip;

}
