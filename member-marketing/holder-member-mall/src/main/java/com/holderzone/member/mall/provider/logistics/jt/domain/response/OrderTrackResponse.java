package com.holderzone.member.mall.provider.logistics.jt.domain.response;

import cn.hutool.core.lang.TypeReference;
import com.alibaba.fastjson.JSONObject;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class OrderTrackResponse implements Serializable {

    /**
     * 运单号
     */
    private String billCode;

    /**
     * 运单轨迹详情。
     */
    private List<TrackDetailRsp> details;

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderTrackResponse buildCommonResponse(String rsp){
        try {
            JtBaseResponse<List<OrderTrackResponse>> jtBaseResponse = JSONObject.parseObject(rsp, new TypeReference<JtBaseResponse<List<OrderTrackResponse>>>() {
            }.getType());
            if(jtBaseResponse == null || jtBaseResponse.getData() == null){
                return null;
            }
            return LogisticsOrderTrackResponse.builder().build();
        }catch (Exception e){
           log.error("解析极兔创建订单返回参数异常",e);
           return null;
        }

    }

}
