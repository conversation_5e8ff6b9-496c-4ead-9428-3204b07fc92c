package com.holderzone.member.mall.provider.logistics.sf.domain.response;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RlsInfo {

    /**
     * 返回调用结果，ERR：调用失败；OK调用成功
     */
    private String invokeResult;

    /**
     * 调用Rls的错误代码
     * 0000（接口参数异常）
     * 0010（其它异常） 0001（xml解析异常
     * 0002（字段校验异常）
     * 0003（票数节点超出最大值，批量请求最大票数为100票）
     * 0004（RLS获取路由标签的必要字段为空
     * 1000 成功
     */
    private String rlsCode;

    /**
     * 错误信息
     */
    private String errorDesc;

    /**
     * 路由查询结果详情
     */
    private RlsDetail rlsDetail;

}
