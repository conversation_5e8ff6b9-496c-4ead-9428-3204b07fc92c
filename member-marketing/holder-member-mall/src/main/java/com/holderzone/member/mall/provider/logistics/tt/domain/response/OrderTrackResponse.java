package com.holderzone.member.mall.provider.logistics.tt.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderTrackResponse;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderTrackResponse extends TtOrderBase implements Serializable{

    /**
     * 自定义字段，用于填写物流渠道（比如某货代商
     */
    private String logistics_channel;

    /**
     * 目的国的二字简码
     */
    private String destination;

    /**
     * 自动更新查询功能的状态，“true”代表系统会自动更新查询结果，“false”则反之
     */
    private Boolean track_update;

    /**
     * 签收人
     */
    private String consignee;

    /**
     * “true”表示该单号会被继续更新，“false”表示该单号已停止更新
     */
    private String updating;

    /**
     * 系统最后更新查询的时间
     */
    private String update_date;

    /**
     * 创建查询的时间
     */
    private String created_at;

    /**
     * 包裹发货时间
     */
    private String order_create_time;

    /**
     * 客户邮箱
     */
    private String customer_email;

    /**
     * 顾客接收短信的手机号码
     */
    private String customer_phone;

    /**
     * 包裹名称
     */
    private String title;

    /**
     * 包裹的订单号，由商家/平台所产生的订单编号
     */
    private String order_number;

    /**
     * 备注，可自定义
     */
    private String note;

    /**
     * 客户姓名
     */
    private String customer_name;

    /**
     * “true”表示该单号已被归档，“false”表示该单号处于未归档状态
     */
    private Boolean archived;

    /**
     * 发件国的名称
     */
    private String original;

    /**
     * 目的国的名称
     */
    private String destination_country;

    /**
     * 包裹的从被揽收至被送达的时长（天）
     */
    private Integer transit_time;

    /**
     * 物流信息未更新的时长（单位：天），由当前时间减去物流信息最近更新时间得到
     */
    private Integer stay_time;

    /**
     * 发件国的物流信息
     */
    private LogisticsInfo origin_info;

    /**
     * 目的国的物流信息
     */
    private LogisticsInfo destination_info;


    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderTrackResponse buildCommonResponse(String rsp){
        TtBaseResponse<List<OrderTrackResponse>> ttBaseResponse = JSONObject.parseObject(rsp, new TypeReference<TtBaseResponse<List<OrderTrackResponse>>>(){}.getType());

        if(ttBaseResponse == null || ttBaseResponse.getData() == null){
            return null;
        }
        return LogisticsOrderTrackResponse.builder().build();
    }

}
