package com.holderzone.member.mall.provider.logistics.yunda.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "yunda")
public class YundaRequestConfig {

    private String appKey;

    private String appSecret;

    private String partnerId;

    private String partnerSecret;

    private String createUrl;

    private String cancelUrl;

    private String chargeUrl;

    private String trackUrl;
}
