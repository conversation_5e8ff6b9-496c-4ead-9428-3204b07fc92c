package com.holderzone.member.mall.provider.logistics.tt.domain.response;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.holderzone.member.common.dto.mall.logistics.LogisticsOrderCancelResponse;
import com.holderzone.member.mall.provider.logistics.tt.domain.TtOrderBase;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderCancelResponse implements Serializable{

    private List<TtOrderBase> success;

    private List<TtOrderBase> error;



    

    /**
     * 构建统一返回参数
     */
    public static LogisticsOrderCancelResponse buildCommonResponse(String rsp){
        TtBaseResponse<OrderCancelResponse> ttBaseResponse = JSONObject.parseObject(rsp, new TypeReference<TtBaseResponse<OrderCancelResponse>>(){}.getType());

        if(ttBaseResponse == null || ttBaseResponse.getData() == null){
            return null;
        }
        return LogisticsOrderCancelResponse.builder().build();
    }

}
