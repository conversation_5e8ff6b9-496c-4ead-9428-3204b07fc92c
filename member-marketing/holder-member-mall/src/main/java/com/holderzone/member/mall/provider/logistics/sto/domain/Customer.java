package com.holderzone.member.mall.provider.logistics.sto.domain;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Customer {

    /**
     * 必填
     * 网点编码必传，测试传值"siteCode":"666666",
     */
    private String siteCode;

    /**
     * 必填
     * 客户编码，测试传值"customerName":"666666000001"
     */
    private String customerName;

    /**
     * 必填
     * 电子面单密码，测试传值"sitePwd":"abc123"
     */
    private String sitePwd;

    /**
     * 月结客户编码（不传单号需调度才传月结编号）如果填写一般和客户编号值相同
     */
    private String monthCustomerCode;

}
