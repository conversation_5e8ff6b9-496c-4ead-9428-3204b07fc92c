package com.holderzone.member.mall.provider.factory;

import cn.hutool.core.util.ObjectUtil;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.mall.provider.LogisticsService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 */
@Component
public class LogisticsServiceFactory implements ApplicationContextAware{

    public ConcurrentMap<ExpressTypeEnum, LogisticsService> logisticsServiceMap = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, LogisticsService> beans = applicationContext.getBeansOfType(LogisticsService.class);
        for (Map.Entry<String, LogisticsService> data : beans.entrySet()) {
            ExpressTypeEnum type = data.getValue().type();
            if (ObjectUtil.isNotNull(type)) {
                logisticsServiceMap.put(type, data.getValue());
            }
        }
    }

    public LogisticsService build(ExpressTypeEnum type) {
        LogisticsService logisticsService = logisticsServiceMap.get(type);
        if (ObjectUtil.isNotNull(logisticsService)) {
            return logisticsService;
        } else {
            return null;
        }
    }
}
