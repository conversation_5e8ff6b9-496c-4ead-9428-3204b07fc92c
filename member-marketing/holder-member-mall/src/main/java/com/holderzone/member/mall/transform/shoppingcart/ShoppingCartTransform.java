package com.holderzone.member.mall.transform.shoppingcart;

import com.holderzone.member.common.dto.mall.shoppingcart.ShoppingCartAddDTO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartCommodityVO;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ShoppingCartTransform {

    ShoppingCartTransform INSTANCE = Mappers.getMapper(ShoppingCartTransform.class);

    HsaShoppingCartCommodity addDTO2ShoppingCartCommodity(ShoppingCartAddDTO addDTO);

    @Mapping(target = "shoppingCartGuid", source = "guid")
    ShoppingCartCommodityVO cartCommodityDO2CartCommodityVO(HsaShoppingCartCommodity cartCommodity);

    List<ShoppingCartCommodityVO> cartCommodityDOList2CartCommodityVOList(List<HsaShoppingCartCommodity> cartCommodityList);
}
