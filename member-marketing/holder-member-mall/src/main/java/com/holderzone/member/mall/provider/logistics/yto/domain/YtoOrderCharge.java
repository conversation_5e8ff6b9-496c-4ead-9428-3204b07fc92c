package com.holderzone.member.mall.provider.logistics.yto.domain;

import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.mall.provider.constant.ParamsCheckConstant;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 圆通订单取消
 */
@Data
@Builder
public class YtoOrderCharge {

    /**
     * 必填
     * 	始发国家,国内价格默认‘CN’
     */
    private String fromCountry;

    /**
     * 必填
     * 	目的国家,国内价格默认‘CN’
     */
    private String country;

    /**
     * 必填
     * 	始发省份
     */
    private String startProv;

    /**
     * 必填
     * 始发城市
     */
    private String startCity;

    /**
     * 始发区、县
     */
    private String startCountry;

    /**
     * 	始发街道
     */
    private String startTown;


    /**
     * 始发详细地址
     */
    private String startAddress;

    /**
     * 必填
     * 目的省份
     */
    private String endProv;


    /**
     * 目的城市
     */
    private String endCity;

    /**
     * 目的区、县
     */
    private String endCountry;

    /**
     * 目的街道
     */
    private String endTown;

    /**
     * 目的详细地址
     */
    private String endAddress;

    /**
     * 快件重量,重量和长宽高二者选其一必填
     */
    private String weight;

    /**
     * 快件长度,重量和长宽高二者选其一必填
     */
    private String length;

    /**
     * 快件宽度,重量和长宽高二者选其一必填
     */
    private String width;

    /**
     * 快件高度,重量和长宽高二者选其一必填
     */
    private String height;

    public void checkParams() {
        if(StringUtils.isEmpty(fromCountry)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"fromCountry"));
        }
        if(StringUtils.isEmpty(country)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"country"));
        }
        if(StringUtils.isEmpty(startProv)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"startProv"));
        }
        if(StringUtils.isEmpty(startCity)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"startCity"));
        }

        if(StringUtils.isEmpty(endProv)){
            throw new MallBaseException(String.format(ParamsCheckConstant.PARAMS_NOT_NULL,"endProv"));
        }
    }
}
