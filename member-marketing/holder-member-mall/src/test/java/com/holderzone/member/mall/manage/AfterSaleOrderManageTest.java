package com.holderzone.member.mall.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.dto.order.*;
import com.holderzone.member.common.qo.mall.order.ApplyRefundQO;
import com.holderzone.member.mall.entity.order.HsaMallOrderTimeRule;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.domain.EventEnum;
import com.holderzone.member.mall.service.order.HsaAfterSaleOrderService;
import com.holderzone.member.mall.service.order.HsaMallBaseOrderService;
import com.holderzone.member.mall.service.order.HsaMallOrderTimeRuleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AfterSaleOrderManageTest {

    @Mock
    private HsaAfterSaleOrderService mockAfterSaleOrderService;
    @Mock
    private HsaMallBaseOrderService mockOrderService;
    @Mock
    private HsaMallOrderTimeRuleService mockOrderTimeRuleService;
    @Mock
    private MemberMallPublisher mockPublisher;

    private AfterSaleOrderManage afterSaleOrderManageUnderTest;

    @Before
    public void setUp() {
        afterSaleOrderManageUnderTest = new AfterSaleOrderManage(mockAfterSaleOrderService, mockOrderService,
                mockOrderTimeRuleService, MoreExecutors.directExecutor(), mockPublisher);
    }

    @Test
    public void testMemberApplyRefund() {
        // Setup
        final ApplyRefundQO refundQO = new ApplyRefundQO();
        refundQO.setOrderGuid("guid");
        refundQO.setReason("cancel");

        // Configure HsaMallOrderTimeRuleService.getOne(...).
        final HsaMallOrderTimeRule hsaMallOrderTimeRule = new HsaMallOrderTimeRule();
        hsaMallOrderTimeRule.setId(0L);
        hsaMallOrderTimeRule.setGuid("b7120d0b-33a3-4c11-8fbc-b2f94e109a0c");
        hsaMallOrderTimeRule.setOperSubjectGuid("operSubjectGuid");
        hsaMallOrderTimeRule.setAutomaticRefund(0);
        hsaMallOrderTimeRule.setAutomaticTimeValue(0);
        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallOrderTimeRule);

        // Configure HsaMallBaseOrderService.getAbleRefundOrder(...).
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("guid");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setMemberInfoGuid("memberInfoGuid");
        mallBaseOrderDTO.setOrderNumber("orderNumber");
        mallBaseOrderDTO.setOrderCondition(0);
        mallBaseOrderDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        mallBaseOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.getAbleRefundOrder("guid")).thenReturn(mallBaseOrderDTO);

        // Configure HsaAfterSaleOrderService.refundOrderSave(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("guid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("orderNumber");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("cancel");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.refundOrderSave(afterSaleOrderDTO, Boolean.FALSE)).thenReturn("result");

        // Run the test
        afterSaleOrderManageUnderTest.memberApplyRefund(refundQO);

        // Verify the results
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testMemberApplyRefund_HsaMallOrderTimeRuleServiceReturnsNull() {
        // Setup
        final ApplyRefundQO refundQO = new ApplyRefundQO();
        refundQO.setOrderGuid("guid");
        refundQO.setReason("cancel");

        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Configure HsaMallBaseOrderService.orderRefund(...).
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("guid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("cancel");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));
        request.setUpdateAfterOrder(false);
        when(mockOrderService.orderRefund(request)).thenReturn("result");

        // Run the test
        afterSaleOrderManageUnderTest.memberApplyRefund(refundQO);

        // Verify the results
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testBusinessAgreeRefund() {
        // Setup
        final AgreeRefundDTO agreeRefundDTO = new AgreeRefundDTO("afterOrderGuid", new BigDecimal("0.00"),
                Arrays.asList("value"));

        // Configure HsaAfterSaleOrderService.getRefundApply(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("guid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("orderNumber");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("cancel");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.getRefundApply("afterOrderGuid")).thenReturn(afterSaleOrderDTO);

        // Configure HsaMallBaseOrderService.orderRefund(...).
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("guid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("cancel");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));
        request.setUpdateAfterOrder(false);
        when(mockOrderService.orderRefund(request)).thenReturn("result");

        // Run the test
        afterSaleOrderManageUnderTest.businessAgreeRefund(agreeRefundDTO);

        // Verify the results
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testBusinessAgreeRefund_HsaMallBaseOrderServiceReturnsNull() {
        // Setup
        final AgreeRefundDTO agreeRefundDTO = new AgreeRefundDTO("afterOrderGuid", new BigDecimal("0.00"),
                Arrays.asList("value"));

        // Configure HsaAfterSaleOrderService.getRefundApply(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("guid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("orderNumber");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("cancel");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.getRefundApply("afterOrderGuid")).thenReturn(afterSaleOrderDTO);

        // Configure HsaMallBaseOrderService.orderRefund(...).
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("guid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("cancel");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));
        request.setUpdateAfterOrder(false);
        when(mockOrderService.orderRefund(request)).thenReturn(null);

        // Run the test
        afterSaleOrderManageUnderTest.businessAgreeRefund(agreeRefundDTO);

        // Verify the results
    }

    @Test
    public void testBatchAgreeRefund() {
        // Setup
        final AgreeRefundDTO agreeRefund = new AgreeRefundDTO("afterOrderGuid", new BigDecimal("0.00"),
                Arrays.asList("value"));

        // Configure HsaAfterSaleOrderService.getRefundApply(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("guid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("orderNumber");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("cancel");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.getRefundApply("afterOrderGuid")).thenReturn(afterSaleOrderDTO);

        // Configure HsaMallBaseOrderService.orderRefund(...).
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("guid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("cancel");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));
        request.setUpdateAfterOrder(false);
        when(mockOrderService.orderRefund(request)).thenReturn("result");

        // Run the test
        afterSaleOrderManageUnderTest.batchAgreeRefund(agreeRefund);

        // Verify the results
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testBatchAgreeRefund_HsaMallBaseOrderServiceReturnsNull() {
        // Setup
        final AgreeRefundDTO agreeRefund = new AgreeRefundDTO("afterOrderGuid", new BigDecimal("0.00"),
                Arrays.asList("value"));

        // Configure HsaAfterSaleOrderService.getRefundApply(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("guid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("orderNumber");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("cancel");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.getRefundApply("afterOrderGuid")).thenReturn(afterSaleOrderDTO);

        // Configure HsaMallBaseOrderService.orderRefund(...).
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("guid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("cancel");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));
        request.setUpdateAfterOrder(false);
        when(mockOrderService.orderRefund(request)).thenReturn(null);

        // Run the test
        afterSaleOrderManageUnderTest.batchAgreeRefund(agreeRefund);

        // Verify the results
    }

    @Test
    public void testBatchRefuseRefund() {
        // Setup
        final RefuseRefundDTO refuseRefund = new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0);

        // Run the test
        afterSaleOrderManageUnderTest.batchRefuseRefund(refuseRefund);

        // Verify the results
        verify(mockAfterSaleOrderService).batchRefuseRefund(
                new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0));
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testUserCancelRefund() {
        // Setup
        // Run the test
        afterSaleOrderManageUnderTest.userCancelRefund("afterOrderGuid");

        // Verify the results
        verify(mockAfterSaleOrderService).batchRefuseRefund(
                new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0));
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }
}
