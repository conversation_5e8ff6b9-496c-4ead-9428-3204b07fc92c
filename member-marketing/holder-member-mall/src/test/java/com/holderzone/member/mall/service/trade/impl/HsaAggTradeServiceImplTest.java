package com.holderzone.member.mall.service.trade.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.mall.OrderStatusDTO;
import com.holderzone.member.common.dto.mall.trade.TradePayDTO;
import com.holderzone.member.common.dto.mall.trade.TradeRefundDTO;
import com.holderzone.member.common.dto.order.MallOrderRefundDTO;
import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.feign.SaasAggPayFeign;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.order.HsaTransactionRecord;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.PushOrderEvent;
import com.holderzone.member.mall.event.domain.EventEnum;
import com.holderzone.member.mall.service.cache.CacheService;
import com.holderzone.member.mall.service.order.HsaAfterSaleOrderService;
import com.holderzone.member.mall.service.order.HsaMallBaseOrderService;
import com.holderzone.member.mall.service.order.HsaTransactionRecordService;
import com.holderzone.member.mall.service.trade.cache.AggTradeCache;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaAggTradeServiceImplTest {

    @Mock
    private SaasAggPayFeign mockSaasAggPayFeign;
    @Mock
    private AggTradeCache mockCache;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMallBaseOrderService mockOrderService;
    @Mock
    private HsaTransactionRecordService mockTransactionRecordService;
    @Mock
    private HsaAfterSaleOrderService mockAfterSaleOrderService;
    @Mock
    private PushOrderEvent mockPushOrderEvent;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private MemberMallPublisher mockMallPublisher;
    @Mock
    private CacheService mockCacheService;
    @Mock
    private HsaBalanceTradeServiceImpl mockBalanceTradeService;

    @InjectMocks
    private HsaAggTradeServiceImpl hsaAggTradeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaAggTradeServiceImplUnderTest, "callbackHost", "callbackHost");
    }

    @Test
    public void testPay() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);
        tradePayDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        tradePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        tradePayDTO.setStoreName("storeName");
        tradePayDTO.setStoreGuid("storeGuid");
        tradePayDTO.setOpenId("subOpenId");
        tradePayDTO.setAppId("appId");
        tradePayDTO.setEnterpriseGuid("enterpriseGuid");
        tradePayDTO.setOperSubjectGuid("operSubjectGuid");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn("payGuid");

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "value", 30);
    }

    @Test
    public void testPay_AggTradeCacheGetPrePaySuccessReturnsNull() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);
        tradePayDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        tradePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        tradePayDTO.setStoreName("storeName");
        tradePayDTO.setStoreGuid("storeGuid");
        tradePayDTO.setOpenId("subOpenId");
        tradePayDTO.setAppId("appId");
        tradePayDTO.setEnterpriseGuid("enterpriseGuid");
        tradePayDTO.setOperSubjectGuid("operSubjectGuid");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(0);

        // Configure AggTradeCache.getAggAccountSet(...).
        final PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        paySettingBaseRes.setDeveloperKey("A6041E8B17CA0082EECA481D623137F2");
        paySettingBaseRes.setDeveloperId("developerId");
        paySettingBaseRes.setAppId("subAppId");
        paySettingBaseRes.setAppSecret("appSecret");
        paySettingBaseRes.setPayMerchantNum("subAppId");
        paySettingBaseRes.setPayMerchantKey("appSecret");
        final PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId("appId");
        paySettingDTO.setStoreId("storeGuid");
        paySettingDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockCache.getAggAccountSet(paySettingDTO)).thenReturn(paySettingBaseRes);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("payGuid");

        // Configure SaasAggPayFeign.pay(...).
        final AggPayRespDTO aggPayRespDTO = AggPayRespDTO.builder()
                .code("code")
                .msg("msg")
                .build();
        final SaasAggPayOdooDTO saasAggPayOdooDTO = new SaasAggPayOdooDTO();
        saasAggPayOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayOdooDTO.setEnterpriseName("enterpriseName");
        saasAggPayOdooDTO.setRequestTimestamp(0L);
        saasAggPayOdooDTO.setReqDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("fe944f58-d4b0-4092-974f-6f68dade4bd4")
                .payGUID("e94f25c0-e672-4071-8a8a-577b32d9ba5a")
                .goodsName("goodsName")
                .body("body")
                .payPowerId("payPowerId")
                .currency("currency")
                .subAppId("subAppId")
                .subOpenId("subOpenId")
                .enterpriseName("enterpriseName")
                .enterpriseGuid("enterpriseGuid")
                .storeName("storeName")
                .timestamp(0L)
                .developerId("developerId")
                .signature("signature")
                .build());
        saasAggPayOdooDTO.setIsQuickReceipt(false);
        saasAggPayOdooDTO.setIsLast(false);
        saasAggPayOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggPayOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.pay(saasAggPayOdooDTO)).thenReturn(aggPayRespDTO);

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");

        // Confirm HsaTransactionRecordService.save(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).save(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockCache).putPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4", "payGuid");

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("fe944f58-d4b0-4092-974f-6f68dade4bd4", convertPolling, 0);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "value", 30);
    }

    @Test
    public void testPay_HsaMallBaseOrderServiceQueryPayStateByGuidReturnsNull() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);
        tradePayDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        tradePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        tradePayDTO.setStoreName("storeName");
        tradePayDTO.setStoreGuid("storeGuid");
        tradePayDTO.setOpenId("subOpenId");
        tradePayDTO.setAppId("appId");
        tradePayDTO.setEnterpriseGuid("enterpriseGuid");
        tradePayDTO.setOperSubjectGuid("operSubjectGuid");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> hsaAggTradeServiceImplUnderTest.pay(tradePayDTO))
                .isInstanceOf(MallBaseException.class);
    }

    @Test
    public void testPay_SaasAggPayFeignReturnsError() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);
        tradePayDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        tradePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        tradePayDTO.setStoreName("storeName");
        tradePayDTO.setStoreGuid("storeGuid");
        tradePayDTO.setOpenId("subOpenId");
        tradePayDTO.setAppId("appId");
        tradePayDTO.setEnterpriseGuid("enterpriseGuid");
        tradePayDTO.setOperSubjectGuid("operSubjectGuid");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(0);

        // Configure AggTradeCache.getAggAccountSet(...).
        final PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        paySettingBaseRes.setDeveloperKey("A6041E8B17CA0082EECA481D623137F2");
        paySettingBaseRes.setDeveloperId("developerId");
        paySettingBaseRes.setAppId("subAppId");
        paySettingBaseRes.setAppSecret("appSecret");
        paySettingBaseRes.setPayMerchantNum("subAppId");
        paySettingBaseRes.setPayMerchantKey("appSecret");
        final PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId("appId");
        paySettingDTO.setStoreId("storeGuid");
        paySettingDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockCache.getAggAccountSet(paySettingDTO)).thenReturn(paySettingBaseRes);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("payGuid");

        // Configure SaasAggPayFeign.pay(...).
        final AggPayRespDTO aggPayRespDTO = AggPayRespDTO.errorPaymentResp("code", "msg");
        final SaasAggPayOdooDTO saasAggPayOdooDTO = new SaasAggPayOdooDTO();
        saasAggPayOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayOdooDTO.setEnterpriseName("enterpriseName");
        saasAggPayOdooDTO.setRequestTimestamp(0L);
        saasAggPayOdooDTO.setReqDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("fe944f58-d4b0-4092-974f-6f68dade4bd4")
                .payGUID("e94f25c0-e672-4071-8a8a-577b32d9ba5a")
                .goodsName("goodsName")
                .body("body")
                .payPowerId("payPowerId")
                .currency("currency")
                .subAppId("subAppId")
                .subOpenId("subOpenId")
                .enterpriseName("enterpriseName")
                .enterpriseGuid("enterpriseGuid")
                .storeName("storeName")
                .timestamp(0L)
                .developerId("developerId")
                .signature("signature")
                .build());
        saasAggPayOdooDTO.setIsQuickReceipt(false);
        saasAggPayOdooDTO.setIsLast(false);
        saasAggPayOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggPayOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.pay(saasAggPayOdooDTO)).thenReturn(aggPayRespDTO);

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");

        // Confirm HsaTransactionRecordService.save(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).save(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockCache).putPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4", "payGuid");

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("fe944f58-d4b0-4092-974f-6f68dade4bd4", convertPolling, 0);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "value", 30);
    }

    @Test
    public void testRefund() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setOrderGuid("orderGuid");
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setReason("reason");
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaTransactionRecordService.getOne(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaTransactionRecord);

        // Configure SaasAggPayFeign.refund(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefundFee("refundFee");
        aggRefundRespDTO.setState("state");
        final SaasAggRefundOdooDTO saasAggRefundOdooDTO = new SaasAggRefundOdooDTO();
        saasAggRefundOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundOdooDTO.setEnterpriseName("enterpriseName");
        saasAggRefundOdooDTO.setRequestTimestamp(0L);
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        aggRefundReqDTO.setOrderGUID("bcc51bfe-1f2c-4f37-866a-11ebdf59778b");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");
        saasAggRefundOdooDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggRefundOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.refund(saasAggRefundOdooDTO)).thenReturn(aggRefundRespDTO);

        // Run the test
        final Integer result = hsaAggTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockAfterSaleOrderService).aggCallbackUpdateRefund("fe944f58-d4b0-4092-974f-6f68dade4bd4");

        // Confirm HsaBalanceTradeServiceImpl.refund(...).
        final TradeRefundDTO tradeRefundDTO1 = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO1 = new MallOrderRefundDTO();
        orderRefundDTO1.setOrderGuid("orderGuid");
        orderRefundDTO1.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO1.setReason("reason");
        orderRefundDTO1.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO1.setOrderRefundDTO(orderRefundDTO1);
        verify(mockBalanceTradeService).refund(tradeRefundDTO1);
    }

    @Test
    public void testRefund_HsaTransactionRecordServiceGetOneReturnsNull() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setOrderGuid("orderGuid");
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setReason("reason");
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final Integer result = hsaAggTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm HsaBalanceTradeServiceImpl.refund(...).
        final TradeRefundDTO tradeRefundDTO1 = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO1 = new MallOrderRefundDTO();
        orderRefundDTO1.setOrderGuid("orderGuid");
        orderRefundDTO1.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO1.setReason("reason");
        orderRefundDTO1.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO1.setOrderRefundDTO(orderRefundDTO1);
        verify(mockBalanceTradeService).refund(tradeRefundDTO1);
    }

    @Test
    public void testRefund_SaasAggPayFeignReturnsError() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setOrderGuid("orderGuid");
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setReason("reason");
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaTransactionRecordService.getOne(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaTransactionRecord);

        // Configure SaasAggPayFeign.refund(...).
        final AggRefundRespDTO aggRefundRespDTO = AggRefundRespDTO.errorPaymentResp("code", "msg");
        final SaasAggRefundOdooDTO saasAggRefundOdooDTO = new SaasAggRefundOdooDTO();
        saasAggRefundOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundOdooDTO.setEnterpriseName("enterpriseName");
        saasAggRefundOdooDTO.setRequestTimestamp(0L);
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        aggRefundReqDTO.setOrderGUID("bcc51bfe-1f2c-4f37-866a-11ebdf59778b");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");
        saasAggRefundOdooDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggRefundOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.refund(saasAggRefundOdooDTO)).thenReturn(aggRefundRespDTO);

        // Run the test
        final Integer result = hsaAggTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockAfterSaleOrderService).aggCallbackUpdateRefund("fe944f58-d4b0-4092-974f-6f68dade4bd4");

        // Confirm HsaBalanceTradeServiceImpl.refund(...).
        final TradeRefundDTO tradeRefundDTO1 = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO1 = new MallOrderRefundDTO();
        orderRefundDTO1.setOrderGuid("orderGuid");
        orderRefundDTO1.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO1.setReason("reason");
        orderRefundDTO1.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO1.setOrderRefundDTO(orderRefundDTO1);
        verify(mockBalanceTradeService).refund(tradeRefundDTO1);
    }

    @Test
    public void testAggPrePay() {
        // Setup
        final AggPrePayDTO aggPrePayDTO = new AggPrePayDTO();
        aggPrePayDTO.setAppId("appId");
        aggPrePayDTO.setEnterpriseGuid("enterpriseGuid");
        aggPrePayDTO.setOperSubjectGuid("operSubjectGuid");
        aggPrePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        aggPrePayDTO.setSubOpenId("subOpenId");
        aggPrePayDTO.setAmount(new BigDecimal("0.00"));
        aggPrePayDTO.setStoreGuid("storeGuid");
        aggPrePayDTO.setStoreName("storeName");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn("payGuid");

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.aggPrePay(aggPrePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");
    }

    @Test
    public void testAggPrePay_AggTradeCacheGetPrePaySuccessReturnsNull() {
        // Setup
        final AggPrePayDTO aggPrePayDTO = new AggPrePayDTO();
        aggPrePayDTO.setAppId("appId");
        aggPrePayDTO.setEnterpriseGuid("enterpriseGuid");
        aggPrePayDTO.setOperSubjectGuid("operSubjectGuid");
        aggPrePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        aggPrePayDTO.setSubOpenId("subOpenId");
        aggPrePayDTO.setAmount(new BigDecimal("0.00"));
        aggPrePayDTO.setStoreGuid("storeGuid");
        aggPrePayDTO.setStoreName("storeName");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(0);

        // Configure AggTradeCache.getAggAccountSet(...).
        final PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        paySettingBaseRes.setDeveloperKey("A6041E8B17CA0082EECA481D623137F2");
        paySettingBaseRes.setDeveloperId("developerId");
        paySettingBaseRes.setAppId("subAppId");
        paySettingBaseRes.setAppSecret("appSecret");
        paySettingBaseRes.setPayMerchantNum("subAppId");
        paySettingBaseRes.setPayMerchantKey("appSecret");
        final PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId("appId");
        paySettingDTO.setStoreId("storeGuid");
        paySettingDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockCache.getAggAccountSet(paySettingDTO)).thenReturn(paySettingBaseRes);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("payGuid");

        // Configure SaasAggPayFeign.pay(...).
        final AggPayRespDTO aggPayRespDTO = AggPayRespDTO.builder()
                .code("code")
                .msg("msg")
                .build();
        final SaasAggPayOdooDTO saasAggPayOdooDTO = new SaasAggPayOdooDTO();
        saasAggPayOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayOdooDTO.setEnterpriseName("enterpriseName");
        saasAggPayOdooDTO.setRequestTimestamp(0L);
        saasAggPayOdooDTO.setReqDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("fe944f58-d4b0-4092-974f-6f68dade4bd4")
                .payGUID("e94f25c0-e672-4071-8a8a-577b32d9ba5a")
                .goodsName("goodsName")
                .body("body")
                .payPowerId("payPowerId")
                .currency("currency")
                .subAppId("subAppId")
                .subOpenId("subOpenId")
                .enterpriseName("enterpriseName")
                .enterpriseGuid("enterpriseGuid")
                .storeName("storeName")
                .timestamp(0L)
                .developerId("developerId")
                .signature("signature")
                .build());
        saasAggPayOdooDTO.setIsQuickReceipt(false);
        saasAggPayOdooDTO.setIsLast(false);
        saasAggPayOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggPayOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.pay(saasAggPayOdooDTO)).thenReturn(aggPayRespDTO);

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.aggPrePay(aggPrePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");

        // Confirm HsaTransactionRecordService.save(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).save(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockCache).putPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4", "payGuid");

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("fe944f58-d4b0-4092-974f-6f68dade4bd4", convertPolling, 0);
    }

    @Test
    public void testAggPrePay_HsaMallBaseOrderServiceQueryPayStateByGuidReturnsNull() {
        // Setup
        final AggPrePayDTO aggPrePayDTO = new AggPrePayDTO();
        aggPrePayDTO.setAppId("appId");
        aggPrePayDTO.setEnterpriseGuid("enterpriseGuid");
        aggPrePayDTO.setOperSubjectGuid("operSubjectGuid");
        aggPrePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        aggPrePayDTO.setSubOpenId("subOpenId");
        aggPrePayDTO.setAmount(new BigDecimal("0.00"));
        aggPrePayDTO.setStoreGuid("storeGuid");
        aggPrePayDTO.setStoreName("storeName");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> hsaAggTradeServiceImplUnderTest.aggPrePay(aggPrePayDTO))
                .isInstanceOf(MallBaseException.class);
    }

    @Test
    public void testAggPrePay_SaasAggPayFeignReturnsError() {
        // Setup
        final AggPrePayDTO aggPrePayDTO = new AggPrePayDTO();
        aggPrePayDTO.setAppId("appId");
        aggPrePayDTO.setEnterpriseGuid("enterpriseGuid");
        aggPrePayDTO.setOperSubjectGuid("operSubjectGuid");
        aggPrePayDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        aggPrePayDTO.setSubOpenId("subOpenId");
        aggPrePayDTO.setAmount(new BigDecimal("0.00"));
        aggPrePayDTO.setStoreGuid("storeGuid");
        aggPrePayDTO.setStoreName("storeName");

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(null);
        when(mockOrderService.queryPayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4")).thenReturn(0);

        // Configure AggTradeCache.getAggAccountSet(...).
        final PaySettingBaseRes paySettingBaseRes = new PaySettingBaseRes();
        paySettingBaseRes.setDeveloperKey("A6041E8B17CA0082EECA481D623137F2");
        paySettingBaseRes.setDeveloperId("developerId");
        paySettingBaseRes.setAppId("subAppId");
        paySettingBaseRes.setAppSecret("appSecret");
        paySettingBaseRes.setPayMerchantNum("subAppId");
        paySettingBaseRes.setPayMerchantKey("appSecret");
        final PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId("appId");
        paySettingDTO.setStoreId("storeGuid");
        paySettingDTO.setOperSubjectGuid("operSubjectGuid");
        when(mockCache.getAggAccountSet(paySettingDTO)).thenReturn(paySettingBaseRes);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("payGuid");

        // Configure SaasAggPayFeign.pay(...).
        final AggPayRespDTO aggPayRespDTO = AggPayRespDTO.errorPaymentResp("code", "msg");
        final SaasAggPayOdooDTO saasAggPayOdooDTO = new SaasAggPayOdooDTO();
        saasAggPayOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggPayOdooDTO.setEnterpriseName("enterpriseName");
        saasAggPayOdooDTO.setRequestTimestamp(0L);
        saasAggPayOdooDTO.setReqDTO(AggPayPreTradingReqDTO.builder()
                .amount(new BigDecimal("0.00"))
                .orderGUID("fe944f58-d4b0-4092-974f-6f68dade4bd4")
                .payGUID("e94f25c0-e672-4071-8a8a-577b32d9ba5a")
                .goodsName("goodsName")
                .body("body")
                .payPowerId("payPowerId")
                .currency("currency")
                .subAppId("subAppId")
                .subOpenId("subOpenId")
                .enterpriseName("enterpriseName")
                .enterpriseGuid("enterpriseGuid")
                .storeName("storeName")
                .timestamp(0L)
                .developerId("developerId")
                .signature("signature")
                .build());
        saasAggPayOdooDTO.setIsQuickReceipt(false);
        saasAggPayOdooDTO.setIsLast(false);
        saasAggPayOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggPayOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.pay(saasAggPayOdooDTO)).thenReturn(aggPayRespDTO);

        // Run the test
        final String result = hsaAggTradeServiceImplUnderTest.aggPrePay(aggPrePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("payGuid");

        // Confirm HsaTransactionRecordService.save(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).save(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockCache).putPrePaySuccess("fe944f58-d4b0-4092-974f-6f68dade4bd4", "payGuid");

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("fe944f58-d4b0-4092-974f-6f68dade4bd4", convertPolling, 0);
    }

    @Test
    public void testAggPayCallback() {
        // Setup
        final AggPayCallbackRspDTO aggPayCallbackRsp = new AggPayCallbackRspDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setOrderHolderNo("orderHolderNo");
        aggPayPollingRespDTO.setOrderNo("orderNo");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setBankTransactionId("bankTransactionId");
        aggPayPollingRespDTO.setOrderGUID("orderGUID");
        aggPayPollingRespDTO.setPayGUID("451d73f4-eba2-49be-b176-3db279292dac");
        aggPayPollingRespDTO.setPrepayInfo("prepayInfo");
        aggPayCallbackRsp.setAggPayPollingRespDTO(aggPayPollingRespDTO);

        when(mockCache.getPrePaySuccess("orderGUID")).thenReturn("guid");
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGUID")).thenReturn(orderPollingDTO);

        // Configure HsaMallBaseOrderService.queryOrderStatusByGuid(...).
        final OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setGuid("guid");
        orderStatusDTO.setOrderCondition(0);
        orderStatusDTO.setPayCondition(0);
        orderStatusDTO.setOrderProcessJson("orderProcessJson");
        orderStatusDTO.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.queryOrderStatusByGuid("orderGUID")).thenReturn(orderStatusDTO);

        // Configure HsaTransactionRecordService.queryByGuid(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.queryByGuid("guid")).thenReturn(hsaTransactionRecord);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggPayCallback(aggPayCallbackRsp);

        // Verify the results
        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);

        // Confirm HsaMallBaseOrderService.updateOrderStatusByGuid(...).
        final OrderStatusDTO dto = new OrderStatusDTO();
        dto.setGuid("guid");
        dto.setOrderCondition(0);
        dto.setPayCondition(0);
        dto.setOrderProcessJson("orderProcessJson");
        dto.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrderService).updateOrderStatusByGuid(dto);
        verify(mockCache).deletePrePaySuccess("orderGUID");
        verify(mockPushOrderEvent).sendChain("guid", 0);
        verify(mockMallPublisher).publish(EventEnum.ORDER_MEMBER_PAY_RECORD, "guid");
        verify(mockCache).updatePayPolling("orderGUID", 0);
    }

    @Test
    public void testAggPayCallback_AggTradeCacheGetPayPollingReturnsNull() {
        // Setup
        final AggPayCallbackRspDTO aggPayCallbackRsp = new AggPayCallbackRspDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setOrderHolderNo("orderHolderNo");
        aggPayPollingRespDTO.setOrderNo("orderNo");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setBankTransactionId("bankTransactionId");
        aggPayPollingRespDTO.setOrderGUID("orderGUID");
        aggPayPollingRespDTO.setPayGUID("451d73f4-eba2-49be-b176-3db279292dac");
        aggPayPollingRespDTO.setPrepayInfo("prepayInfo");
        aggPayCallbackRsp.setAggPayPollingRespDTO(aggPayPollingRespDTO);

        when(mockCache.getPrePaySuccess("orderGUID")).thenReturn("guid");
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPayPolling("orderGUID")).thenReturn(null);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggPayCallback(aggPayCallbackRsp);

        // Verify the results
    }

    @Test
    public void testAggPayCallback_HsaMallBaseOrderServiceQueryOrderStatusByGuidReturnsNull() {
        // Setup
        final AggPayCallbackRspDTO aggPayCallbackRsp = new AggPayCallbackRspDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setOrderHolderNo("orderHolderNo");
        aggPayPollingRespDTO.setOrderNo("orderNo");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setBankTransactionId("bankTransactionId");
        aggPayPollingRespDTO.setOrderGUID("orderGUID");
        aggPayPollingRespDTO.setPayGUID("451d73f4-eba2-49be-b176-3db279292dac");
        aggPayPollingRespDTO.setPrepayInfo("prepayInfo");
        aggPayCallbackRsp.setAggPayPollingRespDTO(aggPayPollingRespDTO);

        when(mockCache.getPrePaySuccess("orderGUID")).thenReturn("guid");
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGUID")).thenReturn(orderPollingDTO);

        when(mockOrderService.queryOrderStatusByGuid("orderGUID")).thenReturn(null);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggPayCallback(aggPayCallbackRsp);

        // Verify the results
    }

    @Test
    public void testAggPayCallback_HsaTransactionRecordServiceQueryByGuidReturnsNull() {
        // Setup
        final AggPayCallbackRspDTO aggPayCallbackRsp = new AggPayCallbackRspDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setOrderHolderNo("orderHolderNo");
        aggPayPollingRespDTO.setOrderNo("orderNo");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setBankTransactionId("bankTransactionId");
        aggPayPollingRespDTO.setOrderGUID("orderGUID");
        aggPayPollingRespDTO.setPayGUID("451d73f4-eba2-49be-b176-3db279292dac");
        aggPayPollingRespDTO.setPrepayInfo("prepayInfo");
        aggPayCallbackRsp.setAggPayPollingRespDTO(aggPayPollingRespDTO);

        when(mockCache.getPrePaySuccess("orderGUID")).thenReturn("guid");
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGUID")).thenReturn(orderPollingDTO);

        // Configure HsaMallBaseOrderService.queryOrderStatusByGuid(...).
        final OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setGuid("guid");
        orderStatusDTO.setOrderCondition(0);
        orderStatusDTO.setPayCondition(0);
        orderStatusDTO.setOrderProcessJson("orderProcessJson");
        orderStatusDTO.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.queryOrderStatusByGuid("orderGUID")).thenReturn(orderStatusDTO);

        when(mockTransactionRecordService.queryByGuid("guid")).thenReturn(null);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggPayCallback(aggPayCallbackRsp);

        // Verify the results
    }

    @Test
    public void testAggPayPolling() {
        // Setup
        final AggPayPollingResultDTO expectedResult = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGuid")).thenReturn(orderPollingDTO);

        // Configure HsaTransactionRecordService.queryByGuid(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.queryByGuid("payGuid")).thenReturn(hsaTransactionRecord);

        // Configure HsaMallBaseOrderService.queryOrderStatusByGuid(...).
        final OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setGuid("guid");
        orderStatusDTO.setOrderCondition(0);
        orderStatusDTO.setPayCondition(0);
        orderStatusDTO.setOrderProcessJson("orderProcessJson");
        orderStatusDTO.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.queryOrderStatusByGuid("orderGuid")).thenReturn(orderStatusDTO);

        // Configure SaasAggPayFeign.polling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setOrderHolderNo("orderHolderNo");
        aggPayPollingRespDTO.setOrderNo("orderNo");
        aggPayPollingRespDTO.setPaySt("paySt");
        aggPayPollingRespDTO.setBankTransactionId("bankTransactionId");
        aggPayPollingRespDTO.setOrderGUID("orderGUID");
        aggPayPollingRespDTO.setPayGUID("451d73f4-eba2-49be-b176-3db279292dac");
        aggPayPollingRespDTO.setPrepayInfo("prepayInfo");
        final SaasPollingOdooDTO saasPollingOdooDTO = new SaasPollingOdooDTO();
        saasPollingOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingOdooDTO.setEnterpriseName("enterpriseName");
        saasPollingOdooDTO.setRequestTimestamp(0L);
        saasPollingOdooDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        saasPollingOdooDTO.setPayGuid("payGuid");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasPollingOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.polling(saasPollingOdooDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final AggPayPollingResultDTO result = hsaAggTradeServiceImplUnderTest.aggPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("orderGuid", convertPolling, 0);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);

        // Confirm HsaMallBaseOrderService.updateOrderStatusByGuid(...).
        final OrderStatusDTO dto = new OrderStatusDTO();
        dto.setGuid("guid");
        dto.setOrderCondition(0);
        dto.setPayCondition(0);
        dto.setOrderProcessJson("orderProcessJson");
        dto.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrderService).updateOrderStatusByGuid(dto);
        verify(mockCache).deletePrePaySuccess("orderGUID");
        verify(mockPushOrderEvent).sendChain("guid", 0);
        verify(mockMallPublisher).publish(EventEnum.ORDER_MEMBER_PAY_RECORD, "guid");
    }

    @Test
    public void testAggPayPolling_AggTradeCacheGetPayPollingReturnsNull() {
        // Setup
        final AggPayPollingResultDTO expectedResult = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockCache.getPayPolling("orderGuid")).thenReturn(null);

        // Run the test
        final AggPayPollingResultDTO result = hsaAggTradeServiceImplUnderTest.aggPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAggPayPolling_HsaTransactionRecordServiceQueryByGuidReturnsNull() {
        // Setup
        final AggPayPollingResultDTO expectedResult = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGuid")).thenReturn(orderPollingDTO);

        when(mockTransactionRecordService.queryByGuid("payGuid")).thenReturn(null);

        // Run the test
        final AggPayPollingResultDTO result = hsaAggTradeServiceImplUnderTest.aggPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAggPayPolling_HsaMallBaseOrderServiceQueryOrderStatusByGuidReturnsNull() {
        // Setup
        final AggPayPollingResultDTO expectedResult = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGuid")).thenReturn(orderPollingDTO);

        // Configure HsaTransactionRecordService.queryByGuid(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.queryByGuid("payGuid")).thenReturn(hsaTransactionRecord);

        when(mockOrderService.queryOrderStatusByGuid("orderGuid")).thenReturn(null);

        // Run the test
        final AggPayPollingResultDTO result = hsaAggTradeServiceImplUnderTest.aggPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAggPayPolling_SaasAggPayFeignReturnsError() {
        // Setup
        final AggPayPollingResultDTO expectedResult = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure AggTradeCache.getPayPolling(...).
        final OrderPollingDTO orderPollingDTO = new OrderPollingDTO();
        orderPollingDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        orderPollingDTO.setEnterpriseGuid("enterpriseGuid");
        orderPollingDTO.setPayGuid("payGuid");
        orderPollingDTO.setAppSecret("appSecret");
        orderPollingDTO.setAppId("subAppId");
        orderPollingDTO.setState(0);
        when(mockCache.getPayPolling("orderGuid")).thenReturn(orderPollingDTO);

        // Configure HsaTransactionRecordService.queryByGuid(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.queryByGuid("payGuid")).thenReturn(hsaTransactionRecord);

        // Configure HsaMallBaseOrderService.queryOrderStatusByGuid(...).
        final OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setGuid("guid");
        orderStatusDTO.setOrderCondition(0);
        orderStatusDTO.setPayCondition(0);
        orderStatusDTO.setOrderProcessJson("orderProcessJson");
        orderStatusDTO.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderService.queryOrderStatusByGuid("orderGuid")).thenReturn(orderStatusDTO);

        // Configure SaasAggPayFeign.polling(...).
        final AggPayPollingRespDTO aggPayPollingRespDTO = AggPayPollingRespDTO.errorResp("code", "msg");
        final SaasPollingOdooDTO saasPollingOdooDTO = new SaasPollingOdooDTO();
        saasPollingOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasPollingOdooDTO.setEnterpriseName("enterpriseName");
        saasPollingOdooDTO.setRequestTimestamp(0L);
        saasPollingOdooDTO.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        saasPollingOdooDTO.setPayGuid("payGuid");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasPollingOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.polling(saasPollingOdooDTO)).thenReturn(aggPayPollingRespDTO);

        // Run the test
        final AggPayPollingResultDTO result = hsaAggTradeServiceImplUnderTest.aggPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm AggTradeCache.putPayPolling(...).
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setAppId("subAppId");
        convertPolling.setState(0);
        verify(mockCache).putPayPolling("orderGuid", convertPolling, 0);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);

        // Confirm HsaMallBaseOrderService.updateOrderStatusByGuid(...).
        final OrderStatusDTO dto = new OrderStatusDTO();
        dto.setGuid("guid");
        dto.setOrderCondition(0);
        dto.setPayCondition(0);
        dto.setOrderProcessJson("orderProcessJson");
        dto.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockOrderService).updateOrderStatusByGuid(dto);
        verify(mockCache).deletePrePaySuccess("orderGUID");
        verify(mockPushOrderEvent).sendChain("guid", 0);
        verify(mockMallPublisher).publish(EventEnum.ORDER_MEMBER_PAY_RECORD, "guid");
    }

    @Test
    public void testAggRefund() {
        // Setup
        final AggRefundDTO refundDTO = new AggRefundDTO("bcc51bfe-1f2c-4f37-866a-11ebdf59778b", new BigDecimal("0.00"),
                "reason", "callbackHost", 0);
        final AggRefundResult expectedResult = new AggRefundResult(0, "message");
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaTransactionRecordService.getOne(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaTransactionRecord);

        // Configure SaasAggPayFeign.refund(...).
        final AggRefundRespDTO aggRefundRespDTO = new AggRefundRespDTO();
        aggRefundRespDTO.setCode("code");
        aggRefundRespDTO.setMsg("msg");
        aggRefundRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundRespDTO.setRefundFee("refundFee");
        aggRefundRespDTO.setState("state");
        final SaasAggRefundOdooDTO saasAggRefundOdooDTO = new SaasAggRefundOdooDTO();
        saasAggRefundOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundOdooDTO.setEnterpriseName("enterpriseName");
        saasAggRefundOdooDTO.setRequestTimestamp(0L);
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        aggRefundReqDTO.setOrderGUID("bcc51bfe-1f2c-4f37-866a-11ebdf59778b");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");
        saasAggRefundOdooDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggRefundOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.refund(saasAggRefundOdooDTO)).thenReturn(aggRefundRespDTO);

        // Run the test
        final AggRefundResult result = hsaAggTradeServiceImplUnderTest.aggRefund(refundDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockAfterSaleOrderService).aggCallbackUpdateRefund("fe944f58-d4b0-4092-974f-6f68dade4bd4");
    }

    @Test
    public void testAggRefund_HsaTransactionRecordServiceGetOneReturnsNull() {
        // Setup
        final AggRefundDTO refundDTO = new AggRefundDTO("bcc51bfe-1f2c-4f37-866a-11ebdf59778b", new BigDecimal("0.00"),
                "reason", "callbackHost", 0);
        final AggRefundResult expectedResult = new AggRefundResult(0, "message");
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        final AggRefundResult result = hsaAggTradeServiceImplUnderTest.aggRefund(refundDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAggRefund_SaasAggPayFeignReturnsError() {
        // Setup
        final AggRefundDTO refundDTO = new AggRefundDTO("bcc51bfe-1f2c-4f37-866a-11ebdf59778b", new BigDecimal("0.00"),
                "reason", "callbackHost", 0);
        final AggRefundResult expectedResult = new AggRefundResult(0, "message");
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaTransactionRecordService.getOne(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaTransactionRecord);

        // Configure SaasAggPayFeign.refund(...).
        final AggRefundRespDTO aggRefundRespDTO = AggRefundRespDTO.errorPaymentResp("code", "msg");
        final SaasAggRefundOdooDTO saasAggRefundOdooDTO = new SaasAggRefundOdooDTO();
        saasAggRefundOdooDTO.setEnterpriseGuid("enterpriseGuid");
        saasAggRefundOdooDTO.setEnterpriseName("enterpriseName");
        saasAggRefundOdooDTO.setRequestTimestamp(0L);
        final AggRefundReqDTO aggRefundReqDTO = new AggRefundReqDTO();
        aggRefundReqDTO.setPayGUID("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        aggRefundReqDTO.setOrderGUID("bcc51bfe-1f2c-4f37-866a-11ebdf59778b");
        aggRefundReqDTO.setRefundType(0);
        aggRefundReqDTO.setRefundFee(new BigDecimal("0.00"));
        aggRefundReqDTO.setReason("reason");
        saasAggRefundOdooDTO.setAggRefundReqDTO(aggRefundReqDTO);
        saasAggRefundOdooDTO.setSaasCallBackUrl("saasCallBackUrl");
        final PaymentInfoDTO paymentInfo = new PaymentInfoDTO();
        paymentInfo.setEnterpriseGuid("enterpriseGuid");
        paymentInfo.setAppId("subAppId");
        paymentInfo.setAppSecret("appSecret");
        saasAggRefundOdooDTO.setPaymentInfo(paymentInfo);
        when(mockSaasAggPayFeign.refund(saasAggRefundOdooDTO)).thenReturn(aggRefundRespDTO);

        // Run the test
        final AggRefundResult result = hsaAggTradeServiceImplUnderTest.aggRefund(refundDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);

        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockAfterSaleOrderService).aggCallbackUpdateRefund("fe944f58-d4b0-4092-974f-6f68dade4bd4");
    }

    @Test
    public void testAggRefundCallback() {
        // Setup
        final AggRefundCallbackRespDTO refundCallbackRsp = new AggRefundCallbackRespDTO();
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefundFee(0);
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        refundCallbackRsp.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);

        // Configure HsaTransactionRecordService.getOne(...).
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        hsaTransactionRecord.setEnterpriseGuid("enterpriseGuid");
        hsaTransactionRecord.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        hsaTransactionRecord.setOrderHolderNo("orderHolderNo");
        hsaTransactionRecord.setBankTransactionId("bankTransactionId");
        hsaTransactionRecord.setAmount(new BigDecimal("0.00"));
        hsaTransactionRecord.setRefundableFee(new BigDecimal("0.00"));
        hsaTransactionRecord.setState(0);
        hsaTransactionRecord.setStoreName("storeName");
        hsaTransactionRecord.setStoreGuid("storeGuid");
        hsaTransactionRecord.setAppId("subAppId");
        hsaTransactionRecord.setAppSecret("appSecret");
        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaTransactionRecord);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggRefundCallback(refundCallbackRsp);

        // Verify the results
        // Confirm HsaTransactionRecordService.updateByGuid(...).
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("bb9f285f-fe28-47ef-bf5e-a8ce39510936");
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setOrderGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4");
        t.setOrderHolderNo("orderHolderNo");
        t.setBankTransactionId("bankTransactionId");
        t.setAmount(new BigDecimal("0.00"));
        t.setRefundableFee(new BigDecimal("0.00"));
        t.setState(0);
        t.setStoreName("storeName");
        t.setStoreGuid("storeGuid");
        t.setAppId("subAppId");
        t.setAppSecret("appSecret");
        verify(mockTransactionRecordService).updateByGuid(t);
        verify(mockOrderService).updatePayStateByGuid("fe944f58-d4b0-4092-974f-6f68dade4bd4", 0);
        verify(mockAfterSaleOrderService).aggCallbackUpdateRefund("fe944f58-d4b0-4092-974f-6f68dade4bd4");
    }

    @Test
    public void testAggRefundCallback_HsaTransactionRecordServiceGetOneReturnsNull() {
        // Setup
        final AggRefundCallbackRespDTO refundCallbackRsp = new AggRefundCallbackRespDTO();
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        final AggRefundDetailRespDTO aggRefundDetailRespDTO = new AggRefundDetailRespDTO();
        aggRefundDetailRespDTO.setRefundFee(0);
        aggRefundDetailRespDTO.setStatus("status");
        aggRefundPollingRespDTO.setRefundOrderDetial(Arrays.asList(aggRefundDetailRespDTO));
        refundCallbackRsp.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);

        when(mockTransactionRecordService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        hsaAggTradeServiceImplUnderTest.aggRefundCallback(refundCallbackRsp);

        // Verify the results
    }

    @Test
    public void testPayWay() {
        assertThat(hsaAggTradeServiceImplUnderTest.payWay()).isEqualTo(PayWayEnum.AGGREGATION_PAY);
    }
}
