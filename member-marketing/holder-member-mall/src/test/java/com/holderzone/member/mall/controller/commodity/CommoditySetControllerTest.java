package com.holderzone.member.mall.controller.commodity;

import com.holderzone.member.common.dto.label.LabelAreaJson;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.DistributionSetDTO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import com.holderzone.member.mall.service.commodity.HsaDistributionSetService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(CommoditySetController.class)
public class CommoditySetControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaCommoditySetService mockHsaCommoditySetService;
    @MockBean
    private HsaDistributionSetService mockHsaDistributionSetService;

    @Test
    public void testGetShareFormat() throws Exception {
        // Setup
        // Configure HsaCommoditySetService.getShareFormat(...).
        final CommodityShareFormatVO commodityShareFormatVO = new CommodityShareFormatVO();
        commodityShareFormatVO.setGuid("7c209a01-19cb-4495-b73a-f8c4bb88fb53");
        commodityShareFormatVO.setShareFormat(0);
        commodityShareFormatVO.setStyle(0);
        when(mockHsaCommoditySetService.getShareFormat("operSubjectGuid")).thenReturn(commodityShareFormatVO);

        // Run the test and verify the results
        mockMvc.perform(get("/commodity_set/get_share_format")
                        .param("operSubjectGuid", "operSubjectGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateShareFormat() throws Exception {
        // Setup
        // Configure HsaCommoditySetService.updateShareFormat(...).
        final CommodityShareFormatVO shareFormat = new CommodityShareFormatVO();
        shareFormat.setGuid("7c209a01-19cb-4495-b73a-f8c4bb88fb53");
        shareFormat.setShareFormat(0);
        shareFormat.setStyle(0);
        when(mockHsaCommoditySetService.updateShareFormat(shareFormat)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity_set/update_share_format")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveOrUpdateDistributionSet() throws Exception {
        // Setup
        // Configure HsaDistributionSetService.saveOrUpdateDistributionSet(...).
        final DistributionSetDTO request = new DistributionSetDTO();
        request.setGuid("38a3da97-b076-4d83-9079-2100c382dc54");
        request.setLinkman("linkman");
        request.setLinkmanPhone("linkmanPhone");
        final LabelAreaJson areaJson = new LabelAreaJson();
        areaJson.setProvinceCode(0);
        request.setAreaJson(areaJson);
        when(mockHsaDistributionSetService.saveOrUpdateDistributionSet(request)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity_set/saveOrUpdateDistributionSet")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryDistributionSetDetail() throws Exception {
        // Setup
        // Configure HsaDistributionSetService.queryDistributionSetDetail(...).
        final DistributionSetDTO distributionSetDTO = new DistributionSetDTO();
        distributionSetDTO.setGuid("38a3da97-b076-4d83-9079-2100c382dc54");
        distributionSetDTO.setLinkman("linkman");
        distributionSetDTO.setLinkmanPhone("linkmanPhone");
        final LabelAreaJson areaJson = new LabelAreaJson();
        areaJson.setProvinceCode(0);
        distributionSetDTO.setAreaJson(areaJson);
        when(mockHsaDistributionSetService.queryDistributionSetDetail(
                "b13eba85-9fc9-46b2-80dd-1bfc0d18fde8")).thenReturn(distributionSetDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/commodity_set/queryDistributionSetDetail")
                        .param("guid", "b13eba85-9fc9-46b2-80dd-1bfc0d18fde8")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryDistributionSetList() throws Exception {
        // Setup
        // Configure HsaDistributionSetService.queryDistributionSetList(...).
        final PageDTO request = new PageDTO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        when(mockHsaDistributionSetService.queryDistributionSetList(request)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/commodity_set/queryDistributionSetList")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDeleteDistributionSet() throws Exception {
        // Setup
        when(mockHsaDistributionSetService.deleteDistributionSet("a05164fd-0dff-456d-9e79-a27bc376e646"))
                .thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity_set/deleteDistributionSet")
                        .param("guid", "a05164fd-0dff-456d-9e79-a27bc376e646")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
