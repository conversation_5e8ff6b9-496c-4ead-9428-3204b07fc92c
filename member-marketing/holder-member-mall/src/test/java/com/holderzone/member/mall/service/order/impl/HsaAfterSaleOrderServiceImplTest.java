package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.order.AfterSaleOrderDTO;
import com.holderzone.member.common.dto.order.RefuseRefundDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.order.QueryAfterOrderQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.order.AfterSaleOrderVO;
import com.holderzone.member.common.vo.order.MallAfterOrderDetailsVO;
import com.holderzone.member.common.vo.order.ProductOrderVO;
import com.holderzone.member.mall.entity.order.HsaAfterSaleOrder;
import com.holderzone.member.mall.entity.order.HsaMallBaseOrder;
import com.holderzone.member.mall.entity.order.HsaOrderAutoConfig;
import com.holderzone.member.mall.entity.order.HsaProductOrderDetail;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.domain.EventEnum;
import com.holderzone.member.mall.mapper.order.HsaAfterSaleOrderMapper;
import com.holderzone.member.mall.mapper.order.HsaMallBaseOrderMapper;
import com.holderzone.member.mall.mapper.order.HsaOrderAutoConfigMapper;
import com.holderzone.member.mall.mapper.order.HsaProductOrderDetailMapper;
import com.holderzone.member.mall.service.order.HsaMallOrderTimeRuleService;
import com.holderzone.member.mall.service.order.HsaOrderAutoConfigService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaAfterSaleOrderServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private FileOssService mockFileOssService;
    @Mock
    private HsaAfterSaleOrderMapper mockHsaAfterSaleOrderMapper;
    @Mock
    private HsaMallOrderTimeRuleService mockHsaMallOrderTimeRuleService;
    @Mock
    private HsaOrderAutoConfigMapper mockOrderAutoConfigMapper;
    @Mock
    private HsaMallBaseOrderMapper mockHsaMallBaseOrderMapper;
    @Mock
    private HsaProductOrderDetailMapper mockHsaProductOrderDetailMapper;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private MemberMallPublisher mockPublisher;
    @Mock
    private HsaOrderAutoConfigService mockOrderAutoConfigService;

    @InjectMocks
    private HsaAfterSaleOrderServiceImpl hsaAfterSaleOrderServiceImplUnderTest;

    @Test
    public void testFindAfterSaleOrderPage() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request1)).thenReturn(afterSaleOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Run the test
        final PageResult result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderPage(request);

        // Verify the results
    }

    @Test
    public void testFindAfterSaleOrderPage_HsaAfterSaleOrderMapperReturnsNoItems() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request1)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Run the test
        final PageResult result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderPage(request);

        // Verify the results
    }

    @Test
    public void testFindAfterSaleOrderPage_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request1)).thenReturn(afterSaleOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderPage(request);

        // Verify the results
    }

    @Test
    public void testFindAfterSaleOrderDetails() {
        // Setup
        final MallAfterOrderDetailsVO expectedResult = new MallAfterOrderDetailsVO();
        expectedResult.setOrderDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setIntegralDeductAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPreferentialAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPaidAmount(new BigDecimal("0.00"));
        expectedResult.setAfterTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefuseReason("refuseReason");
        expectedResult.setSurplusAfterTime(0L);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request)).thenReturn(afterSaleOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaAfterSaleOrderMapper.selectOne(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setId(0L);
        hsaAfterSaleOrder.setGuid("guid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setOrderNumber("orderNumber");
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefuseReason("refuseReason");
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAfterSaleOrder.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaAfterSaleOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrder);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("875b6fb6-23a0-47cc-a19b-33ca0a0d7ef7");
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        when(mockHsaMallBaseOrderMapper.queryByGuid("orderGuid")).thenReturn(hsaMallBaseOrder);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setId(0L);
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setRefundConfirmState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallAfterOrderDetailsVO result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderDetails(
                "892eb9c3-e46c-4bb6-af13-4222a319dcec");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAfterSaleOrderDetails_HsaAfterSaleOrderMapperFindAfterSaleOrderListReturnsNoItems() {
        // Setup
        final MallAfterOrderDetailsVO expectedResult = new MallAfterOrderDetailsVO();
        expectedResult.setOrderDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setIntegralDeductAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPreferentialAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPaidAmount(new BigDecimal("0.00"));
        expectedResult.setAfterTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefuseReason("refuseReason");
        expectedResult.setSurplusAfterTime(0L);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaAfterSaleOrderMapper.selectOne(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setId(0L);
        hsaAfterSaleOrder.setGuid("guid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setOrderNumber("orderNumber");
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefuseReason("refuseReason");
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAfterSaleOrder.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaAfterSaleOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrder);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("875b6fb6-23a0-47cc-a19b-33ca0a0d7ef7");
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        when(mockHsaMallBaseOrderMapper.queryByGuid("orderGuid")).thenReturn(hsaMallBaseOrder);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setId(0L);
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setRefundConfirmState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallAfterOrderDetailsVO result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderDetails(
                "892eb9c3-e46c-4bb6-af13-4222a319dcec");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindAfterSaleOrderDetails_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final MallAfterOrderDetailsVO expectedResult = new MallAfterOrderDetailsVO();
        expectedResult.setOrderDiscountAmount(new BigDecimal("0.00"));
        expectedResult.setIntegralDeductAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPreferentialAmount(new BigDecimal("0.00"));
        expectedResult.setOrderPaidAmount(new BigDecimal("0.00"));
        expectedResult.setAfterTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefuseReason("refuseReason");
        expectedResult.setSurplusAfterTime(0L);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request)).thenReturn(afterSaleOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaAfterSaleOrderMapper.selectOne(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setId(0L);
        hsaAfterSaleOrder.setGuid("guid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setOrderNumber("orderNumber");
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefuseReason("refuseReason");
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAfterSaleOrder.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaAfterSaleOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrder);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("875b6fb6-23a0-47cc-a19b-33ca0a0d7ef7");
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        when(mockHsaMallBaseOrderMapper.queryByGuid("orderGuid")).thenReturn(hsaMallBaseOrder);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setId(0L);
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setRefundConfirmState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallAfterOrderDetailsVO result = hsaAfterSaleOrderServiceImplUnderTest.findAfterSaleOrderDetails(
                "892eb9c3-e46c-4bb6-af13-4222a319dcec");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMallAfterSaleOrderExport() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderCount(...).
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderCount(request1)).thenReturn(0);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request2 = new QueryAfterOrderQO();
        request2.setCurrentPage(0);
        request2.setPageSize(0);
        request2.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request2.setOperSubjectGuid("operSubjectGuid");
        request2.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request2)).thenReturn(afterSaleOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaAfterSaleOrderServiceImplUnderTest.mallAfterSaleOrderExport(request);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testMallAfterSaleOrderExport_HsaAfterSaleOrderMapperFindAfterSaleOrderListReturnsNoItems() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderCount(...).
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderCount(request1)).thenReturn(0);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final QueryAfterOrderQO request2 = new QueryAfterOrderQO();
        request2.setCurrentPage(0);
        request2.setPageSize(0);
        request2.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request2.setOperSubjectGuid("operSubjectGuid");
        request2.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request2)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("productName");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("productUnitPrice");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaAfterSaleOrderServiceImplUnderTest.mallAfterSaleOrderExport(request);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testMallAfterSaleOrderExport_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderCount(...).
        final QueryAfterOrderQO request1 = new QueryAfterOrderQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request1.setOperSubjectGuid("operSubjectGuid");
        request1.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderCount(request1)).thenReturn(0);

        // Configure HsaAfterSaleOrderMapper.findAfterSaleOrderList(...).
        final AfterSaleOrderVO afterSaleOrderVO = new AfterSaleOrderVO();
        afterSaleOrderVO.setGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        afterSaleOrderVO.setOrderGuid("orderGuid");
        afterSaleOrderVO.setAfterOrderNum("afterOrderNum");
        afterSaleOrderVO.setRefundCondition(0);
        afterSaleOrderVO.setOrderNumber("orderNumber");
        afterSaleOrderVO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderVO.setOrderCondition(0);
        afterSaleOrderVO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderVO.setCancel("cancel");
        afterSaleOrderVO.setStoreName("storeName");
        afterSaleOrderVO.setMemberPhoneNum("memberPhoneNum");
        afterSaleOrderVO.setMemberName("memberName");
        afterSaleOrderVO.setRefundType(0);
        afterSaleOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("af86b1bf-068f-4356-8721-dbf7fe1d6531");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("productName");
        productOrderVO.setProductUnitPrice("productUnitPrice");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        afterSaleOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        final List<AfterSaleOrderVO> afterSaleOrderVOS = Arrays.asList(afterSaleOrderVO);
        final QueryAfterOrderQO request2 = new QueryAfterOrderQO();
        request2.setCurrentPage(0);
        request2.setPageSize(0);
        request2.setGuid("892eb9c3-e46c-4bb6-af13-4222a319dcec");
        request2.setOperSubjectGuid("operSubjectGuid");
        request2.setOrderNumber("orderNumber");
        when(mockHsaAfterSaleOrderMapper.findAfterSaleOrderList(request2)).thenReturn(afterSaleOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaAfterSaleOrderServiceImplUnderTest.mallAfterSaleOrderExport(request);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testRefundOrderUpdate() {
        // Setup
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setGuid("ce2d2a87-9d44-4296-8a53-381d012ac34a");
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("orderGuid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");

        // Run the test
        final String result = hsaAfterSaleOrderServiceImplUnderTest.refundOrderUpdate(afterSaleOrderDTO);

        // Verify the results
        assertThat(result).isEqualTo("guid");
        verify(mockPublisher).publish(EventEnum.ORDER_REFUND_CONDITION, "content");
    }

    @Test
    public void testRefundOrderSave() {
        // Setup
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setGuid("ce2d2a87-9d44-4296-8a53-381d012ac34a");
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("orderGuid");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        final String result = hsaAfterSaleOrderServiceImplUnderTest.refundOrderSave(afterSaleOrderDTO, false);

        // Verify the results
        assertThat(result).isEqualTo("guid");
        verify(mockPublisher).publish(EventEnum.ORDER_REFUND_CONDITION, "content");
    }

    @Test
    public void testAggCallbackUpdateRefund() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Run the test
        hsaAfterSaleOrderServiceImplUnderTest.aggCallbackUpdateRefund("orderGuid");

        // Verify the results
        verify(mockPublisher).publish(EventEnum.ORDER_REFUND_CONDITION, "content");
    }

    @Test
    public void testGetRefundApply() {
        // Setup
        final AfterSaleOrderDTO expectedResult = new AfterSaleOrderDTO();
        expectedResult.setGuid("ce2d2a87-9d44-4296-8a53-381d012ac34a");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");

        // Run the test
        final AfterSaleOrderDTO result = hsaAfterSaleOrderServiceImplUnderTest.getRefundApply(
                "e145bddc-1005-40c1-950e-c2a3d232578e");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListRefundApply() {
        // Setup
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setId(0L);
        hsaAfterSaleOrder.setGuid("guid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setOrderNumber("orderNumber");
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefuseReason("refuseReason");
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAfterSaleOrder.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaAfterSaleOrder> expectedResult = Arrays.asList(hsaAfterSaleOrder);

        // Run the test
        final List<HsaAfterSaleOrder> result = hsaAfterSaleOrderServiceImplUnderTest.listRefundApply(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchRefuseRefund() {
        // Setup
        final RefuseRefundDTO refuseRefund = new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0);
        when(mockRedissonClient.getLock("BATCH_REFUSE_REFUND")).thenReturn(null);

        // Run the test
        hsaAfterSaleOrderServiceImplUnderTest.batchRefuseRefund(refuseRefund);

        // Verify the results
        verify(mockOrderAutoConfigService).deleteRefundConfirmTime(Arrays.asList("value"));
        verify(mockHsaAfterSaleOrderMapper).batchRefuseRefund(
                new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0));
        verify(mockPublisher).publish(EventEnum.ORDER_REFUND_CONDITION, "content");
    }

    @Test
    public void testGetLastAfterByOrderGuid() {
        // Setup
        final HsaAfterSaleOrder expectedResult = new HsaAfterSaleOrder();
        expectedResult.setId(0L);
        expectedResult.setGuid("guid");
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setAfterOrderNum("afterOrderNum");
        expectedResult.setRefundCondition(0);
        expectedResult.setOrderNumber("orderNumber");
        expectedResult.setCancel("cancel");
        expectedResult.setRefuseReason("refuseReason");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final HsaAfterSaleOrder result = hsaAfterSaleOrderServiceImplUnderTest.getLastAfterByOrderGuid("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAfterOrderNumSuffix() {
        assertThat(hsaAfterSaleOrderServiceImplUnderTest.afterOrderNumSuffix()).isEqualTo("result");
    }
}
