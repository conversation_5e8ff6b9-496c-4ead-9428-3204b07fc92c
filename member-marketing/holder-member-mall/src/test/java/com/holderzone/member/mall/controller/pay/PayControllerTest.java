package com.holderzone.member.mall.controller.pay;

import com.holderzone.member.common.dto.pay.*;
import com.holderzone.member.mall.service.trade.HsaAggTradeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(PayController.class)
public class PayControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaAggTradeService mockPayService;

    @Test
    public void testAggCallback() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/pay/agg/callback")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaAggTradeService.aggPayCallback(...).
        final AggPayCallbackRspDTO aggPayCallbackDTO = new AggPayCallbackRspDTO();
        final AggPayPollingRespDTO aggPayPollingRespDTO = new AggPayPollingRespDTO();
        aggPayPollingRespDTO.setCode("code");
        aggPayPollingRespDTO.setMsg("msg");
        aggPayPollingRespDTO.setAmount(new BigDecimal("0.00"));
        aggPayPollingRespDTO.setSubject("subject");
        aggPayCallbackDTO.setAggPayPollingRespDTO(aggPayPollingRespDTO);
        verify(mockPayService).aggPayCallback(aggPayCallbackDTO);
    }

    @Test
    public void testAggRefund() throws Exception {
        // Setup
        when(mockPayService.aggRefund(
                new AggRefundDTO("orderGuid", new BigDecimal("0.00"), "reason", "callbackHost", 0)))
                .thenReturn(new AggRefundResult(0, "message"));

        // Run the test and verify the results
        mockMvc.perform(post("/pay/agg/refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAggRefund_HsaAggTradeServiceReturnsFailure() throws Exception {
        // Setup
        when(mockPayService.aggRefund(
                new AggRefundDTO("orderGuid", new BigDecimal("0.00"), "reason", "callbackHost", 0)))
                .thenReturn(AggRefundResult.buildFail("message"));

        // Run the test and verify the results
        mockMvc.perform(post("/pay/agg/refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAggRefundCallback() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/pay/agg/refund/callback")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaAggTradeService.aggRefundCallback(...).
        final AggRefundCallbackRespDTO refundRsp = new AggRefundCallbackRespDTO();
        final AggRefundPollingRespDTO aggRefundPollingRespDTO = new AggRefundPollingRespDTO();
        aggRefundPollingRespDTO.setCode("code");
        aggRefundPollingRespDTO.setMsg("msg");
        aggRefundPollingRespDTO.setMchntOrderNo("mchntOrderNo");
        aggRefundPollingRespDTO.setOrderNo("orderNo");
        refundRsp.setAggRefundPollingRespDTO(aggRefundPollingRespDTO);
        verify(mockPayService).aggRefundCallback(refundRsp);
    }

    @Test
    public void testAggPolling() throws Exception {
        // Setup
        // Configure HsaAggTradeService.aggPayPolling(...).
        final AggPayPollingResultDTO aggPayPollingResultDTO = new AggPayPollingResultDTO("prepayInfo", 0);
        when(mockPayService.aggPayPolling("orderGuid")).thenReturn(aggPayPollingResultDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/pay/agg/polling")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
