package com.holderzone.member.mall.service.logistics.impl;

import com.holderzone.member.common.qo.logistics.LogisticsTemplateQO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaLogisticsTemplateServiceImplTest {

    private HsaLogisticsTemplateServiceImpl hsaLogisticsTemplateServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaLogisticsTemplateServiceImplUnderTest = new HsaLogisticsTemplateServiceImpl();
    }

    @Test
    public void testList() {
        // Setup
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("excludeTemplateGuid");

        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("8d2a0026-3957-43a6-88bd-08d7c91999ec");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setFreightAmount(new BigDecimal("0.00"));
        final List<LogisticsTemplateVO> expectedResult = Arrays.asList(logisticsTemplateVO);

        // Run the test
        final List<LogisticsTemplateVO> result = hsaLogisticsTemplateServiceImplUnderTest.list(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGuidList() {
        // Setup
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("excludeTemplateGuid");

        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("8d2a0026-3957-43a6-88bd-08d7c91999ec");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setFreightAmount(new BigDecimal("0.00"));
        final List<LogisticsTemplateVO> expectedResult = Arrays.asList(logisticsTemplateVO);

        // Run the test
        final List<LogisticsTemplateVO> result = hsaLogisticsTemplateServiceImplUnderTest.guidList(query);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDefaultTemplateGuid() {
        // Setup
        // Run the test
        final String result = hsaLogisticsTemplateServiceImplUnderTest.queryDefaultTemplateGuid("operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testClearDefaultFlag() {
        // Setup
        // Run the test
        hsaLogisticsTemplateServiceImplUnderTest.clearDefaultFlag();

        // Verify the results
    }
}
