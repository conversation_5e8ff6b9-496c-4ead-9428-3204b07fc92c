package com.holderzone.member.mall.service.trade.cache;

import com.holderzone.member.common.dto.base.PaySettingBaseRes;
import com.holderzone.member.common.dto.base.PaySettingDTO;
import com.holderzone.member.common.dto.pay.OrderPollingDTO;
import com.holderzone.member.common.external.ExternalSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AggTradeCacheTest {

    @Mock
    private StringRedisTemplate mockAggTradeRedisTemplate;
    @Mock
    private ExternalSupport mockExternalSupport;

    private AggTradeCache aggTradeCacheUnderTest;

    @Before
    public void setUp() throws Exception {
        aggTradeCacheUnderTest = new AggTradeCache(mockAggTradeRedisTemplate, mockExternalSupport);
    }

    @Test
    public void testGetAggAccountSet() {
        // Setup
        final PaySettingDTO paySettingDTO = new PaySettingDTO();
        paySettingDTO.setAppId("appId");
        paySettingDTO.setStoreId("storeId");
        paySettingDTO.setOperSubjectGuid("operSubjectGuid");

        final PaySettingBaseRes expectedResult = new PaySettingBaseRes();
        expectedResult.setDeveloperKey("developerKey");
        expectedResult.setAppId("appId");
        expectedResult.setAppSecret("appSecret");
        expectedResult.setPayMerchantNum("payMerchantNum");
        expectedResult.setPayMerchantKey("payMerchantKey");

        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);
        when(mockExternalSupport.storeServer(0)).thenReturn(null);

        // Run the test
        final PaySettingBaseRes result = aggTradeCacheUnderTest.getAggAccountSet(paySettingDTO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockAggTradeRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testPutPrePaySuccess() {
        // Setup
        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        aggTradeCacheUnderTest.putPrePaySuccess("orderGuid", "payGuid");

        // Verify the results
        verify(mockAggTradeRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testGetPrePaySuccess() {
        // Setup
        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = aggTradeCacheUnderTest.getPrePaySuccess("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testDeletePrePaySuccess() {
        // Setup
        // Run the test
        aggTradeCacheUnderTest.deletePrePaySuccess("orderGuid");

        // Verify the results
        verify(mockAggTradeRedisTemplate).delete("key");
    }

    @Test
    public void testPutPayPolling() {
        // Setup
        final OrderPollingDTO convertPolling = new OrderPollingDTO();
        convertPolling.setOrderGuid("orderGuid");
        convertPolling.setEnterpriseGuid("enterpriseGuid");
        convertPolling.setPayGuid("payGuid");
        convertPolling.setAppSecret("appSecret");
        convertPolling.setState(0);

        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        aggTradeCacheUnderTest.putPayPolling("orderGuid", convertPolling, 0);

        // Verify the results
        verify(mockAggTradeRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testUpdatePayPolling() {
        // Setup
        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        aggTradeCacheUnderTest.updatePayPolling("orderGuid", 0);

        // Verify the results
        verify(mockAggTradeRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testGetPayPolling() {
        // Setup
        final OrderPollingDTO expectedResult = new OrderPollingDTO();
        expectedResult.setOrderGuid("orderGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setPayGuid("payGuid");
        expectedResult.setAppSecret("appSecret");
        expectedResult.setState(0);

        when(mockAggTradeRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final OrderPollingDTO result = aggTradeCacheUnderTest.getPayPolling("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
