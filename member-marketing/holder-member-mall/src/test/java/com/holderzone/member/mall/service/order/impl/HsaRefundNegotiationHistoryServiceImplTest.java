package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.vo.mall.AppletNegotiationHistoryVO;
import com.holderzone.member.common.vo.mall.RefundNegotiationHistoryVO;
import com.holderzone.member.mall.entity.order.HsaAfterSaleOrder;
import com.holderzone.member.mall.mapper.order.HsaAfterSaleOrderMapper;
import com.holderzone.member.mall.mapper.order.HsaRefundNegotiationHistoryMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaRefundNegotiationHistoryServiceImplTest {

    @Mock
    private HsaRefundNegotiationHistoryMapper mockHsaRefundNegotiationHistoryMapper;
    @Mock
    private HsaAfterSaleOrderMapper mockHsaAfterSaleOrderMapper;

    @InjectMocks
    private HsaRefundNegotiationHistoryServiceImpl hsaRefundNegotiationHistoryServiceImplUnderTest;

    @Test
    public void testSaveNegotiationHistoryByEvent() {
        // Setup
        // Run the test
        hsaRefundNegotiationHistoryServiceImplUnderTest.saveNegotiationHistoryByEvent("content");

        // Verify the results
    }

    @Test
    public void testGetRefundNegotiationHistory() {
        // Setup
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO.setAfterSaleOrderGuid("afterSaleOrderGuid");
        final List<RefundNegotiationHistoryVO> expectedResult = Arrays.asList(refundNegotiationHistoryVO);

        // Run the test
        final List<RefundNegotiationHistoryVO> result = hsaRefundNegotiationHistoryServiceImplUnderTest.getRefundNegotiationHistory(
                "afterSaleOrderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchGetRefundNegotiationHistory() {
        // Setup
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO.setAfterSaleOrderGuid("afterSaleOrderGuid");
        final List<RefundNegotiationHistoryVO> expectedResult = Arrays.asList(refundNegotiationHistoryVO);

        // Run the test
        final List<RefundNegotiationHistoryVO> result = hsaRefundNegotiationHistoryServiceImplUnderTest.batchGetRefundNegotiationHistory(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAppletNegotiationHistory() {
        // Setup
        final AppletNegotiationHistoryVO expectedResult = new AppletNegotiationHistoryVO();
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO.setAfterSaleOrderGuid("afterSaleOrderGuid");
        expectedResult.setLatestHistory(Arrays.asList(refundNegotiationHistoryVO));
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO1 = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO1.setState(0);
        refundNegotiationHistoryVO1.setRoleType(0);
        refundNegotiationHistoryVO1.setReason("reason");
        refundNegotiationHistoryVO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO1.setAfterSaleOrderGuid("afterSaleOrderGuid");
        expectedResult.setAllHistory(Arrays.asList(refundNegotiationHistoryVO1));

        // Configure HsaAfterSaleOrderMapper.selectList(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setId(0L);
        hsaAfterSaleOrder.setGuid("d50a7cd2-91e4-49ba-b674-11d849c641be");
        hsaAfterSaleOrder.setOperSubjectGuid("operSubjectGuid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaAfterSaleOrder> hsaAfterSaleOrders = Arrays.asList(hsaAfterSaleOrder);
        when(mockHsaAfterSaleOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrders);

        // Run the test
        final AppletNegotiationHistoryVO result = hsaRefundNegotiationHistoryServiceImplUnderTest.getAppletNegotiationHistory(
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAppletNegotiationHistory_HsaAfterSaleOrderMapperReturnsNoItems() {
        // Setup
        final AppletNegotiationHistoryVO expectedResult = new AppletNegotiationHistoryVO();
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO.setAfterSaleOrderGuid("afterSaleOrderGuid");
        expectedResult.setLatestHistory(Arrays.asList(refundNegotiationHistoryVO));
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO1 = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO1.setState(0);
        refundNegotiationHistoryVO1.setRoleType(0);
        refundNegotiationHistoryVO1.setReason("reason");
        refundNegotiationHistoryVO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO1.setAfterSaleOrderGuid("afterSaleOrderGuid");
        expectedResult.setAllHistory(Arrays.asList(refundNegotiationHistoryVO1));

        when(mockHsaAfterSaleOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final AppletNegotiationHistoryVO result = hsaRefundNegotiationHistoryServiceImplUnderTest.getAppletNegotiationHistory(
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
