package com.holderzone.member.mall.support;

import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.mall.order.GetFreightAmount;
import com.holderzone.member.common.qo.mall.order.OrderProductAmountQO;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.order.MallOrderFreightAmountVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import com.holderzone.member.mall.service.commodity.HsaDeliverySetService;
import com.holderzone.member.mall.service.logistics.HsaLogisticsChargeService;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LogisticsCacheSupportTest {

    @Mock
    private RedisTemplate<String, String> mockRedisTemplateString;
    @Mock
    private HsaLogisticsTemplateService mockLogisticsTemplateService;
    @Mock
    private HsaLogisticsChargeService mockLogisticsChargeService;
    @Mock
    private MemberCommodityFeign mockMemberCommodityFeign;
    @Mock
    private HsaDeliverySetService mockDeliverySetService;

    private LogisticsCacheSupport logisticsCacheSupportUnderTest;

    @Before
    public void setUp() throws Exception {
        logisticsCacheSupportUnderTest = new LogisticsCacheSupport(mockRedisTemplateString,
                mockLogisticsTemplateService, mockLogisticsChargeService, mockMemberCommodityFeign,
                mockDeliverySetService);
    }

    @Test
    public void testGetFreightAmount1() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount1_RedisTemplateHasKeyReturnsNull() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(null);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount1_HsaLogisticsChargeServiceReturnsNoItems() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(Collections.emptyList());
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount2() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode", 0, "province",
                "city");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount2_RedisTemplateHasKeyReturnsNull() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(null);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode", 0, "province",
                "city");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount2_HsaLogisticsChargeServiceReturnsNoItems() {
        // Setup
        when(mockMemberCommodityFeign.getTemplateGuidByCommodityCodeByCache("commodityCode"))
                .thenReturn("templateGuid");
        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(Collections.emptyList());
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        final BigDecimal result = logisticsCacheSupportUnderTest.getFreightAmount("commodityCode", 0, "province",
                "city");

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetOrderFreightAmount() {
        // Setup
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductNum(0);
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));

        final MallOrderFreightAmountVO expectedResult = new MallOrderFreightAmountVO();
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setUnCommodityCodes(Arrays.asList("value"));

        when(mockMemberCommodityFeign.getTemplateGuidsByCommodityCodesByCache(Arrays.asList("value")))
                .thenReturn(new HashMap<>());

        // Configure HsaDeliverySetService.getDeliverySet(...).
        final DeliverySetVO deliverySetVO = new DeliverySetVO();
        deliverySetVO.setGuid("bc81980f-2709-47a0-a97f-69fc65601426");
        deliverySetVO.setIsExpressDelivery(0);
        deliverySetVO.setIsSelectAll(0);
        deliverySetVO.setLogistics(Arrays.asList("value"));
        deliverySetVO.setFreightMode(0);
        when(mockDeliverySetService.getDeliverySet("operSubjectGuid")).thenReturn(deliverySetVO);

        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final MallOrderFreightAmountVO result = logisticsCacheSupportUnderTest.getOrderFreightAmount(orderInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderFreightAmount_RedisTemplateHasKeyReturnsNull() {
        // Setup
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductNum(0);
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));

        final MallOrderFreightAmountVO expectedResult = new MallOrderFreightAmountVO();
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setUnCommodityCodes(Arrays.asList("value"));

        when(mockMemberCommodityFeign.getTemplateGuidsByCommodityCodesByCache(Arrays.asList("value")))
                .thenReturn(new HashMap<>());

        // Configure HsaDeliverySetService.getDeliverySet(...).
        final DeliverySetVO deliverySetVO = new DeliverySetVO();
        deliverySetVO.setGuid("bc81980f-2709-47a0-a97f-69fc65601426");
        deliverySetVO.setIsExpressDelivery(0);
        deliverySetVO.setIsSelectAll(0);
        deliverySetVO.setLogistics(Arrays.asList("value"));
        deliverySetVO.setFreightMode(0);
        when(mockDeliverySetService.getDeliverySet("operSubjectGuid")).thenReturn(deliverySetVO);

        when(mockRedisTemplateString.hasKey("key")).thenReturn(null);
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("85e496e2-fa82-4e69-9f43-5807cfdd1284");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(hsaLogisticsCharges);

        // Run the test
        final MallOrderFreightAmountVO result = logisticsCacheSupportUnderTest.getOrderFreightAmount(orderInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderFreightAmount_HsaLogisticsChargeServiceReturnsNoItems() {
        // Setup
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductNum(0);
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));

        final MallOrderFreightAmountVO expectedResult = new MallOrderFreightAmountVO();
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setUnCommodityCodes(Arrays.asList("value"));

        when(mockMemberCommodityFeign.getTemplateGuidsByCommodityCodesByCache(Arrays.asList("value")))
                .thenReturn(new HashMap<>());

        // Configure HsaDeliverySetService.getDeliverySet(...).
        final DeliverySetVO deliverySetVO = new DeliverySetVO();
        deliverySetVO.setGuid("bc81980f-2709-47a0-a97f-69fc65601426");
        deliverySetVO.setIsExpressDelivery(0);
        deliverySetVO.setIsSelectAll(0);
        deliverySetVO.setLogistics(Arrays.asList("value"));
        deliverySetVO.setFreightMode(0);
        when(mockDeliverySetService.getDeliverySet("operSubjectGuid")).thenReturn(deliverySetVO);

        when(mockRedisTemplateString.hasKey("key")).thenReturn(false);

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        hsaLogisticsTemplate.setTemplateName("templateName");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        when(mockLogisticsChargeService.findByTemplateGuid("templateGuid")).thenReturn(Collections.emptyList());
        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        final MallOrderFreightAmountVO result = logisticsCacheSupportUnderTest.getOrderFreightAmount(orderInfo);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSetTemplate() {
        // Setup
        final LogisticsTemplateDetailsVO detailsVO = new LogisticsTemplateDetailsVO();
        detailsVO.setGuid("1370a78b-32a1-4e28-afc3-59926bf024eb");
        detailsVO.setTemplateName("templateName");
        detailsVO.setChargeType(0);
        detailsVO.setFreightAmount(new BigDecimal("0.00"));
        detailsVO.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        innerCharge.setWeight(new BigDecimal("0.00"));
        innerCharge.setPrice(new BigDecimal("0.00"));
        innerCharge.setSecondWeight(new BigDecimal("0.00"));
        innerCharge.setSecondPrice(new BigDecimal("0.00"));
        detailsVO.setCharges(Arrays.asList(innerCharge));

        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        logisticsCacheSupportUnderTest.setTemplate(detailsVO);

        // Verify the results
    }

    @Test
    public void testRemoveTemplate() {
        // Setup
        // Run the test
        logisticsCacheSupportUnderTest.removeTemplate("templateGuid");

        // Verify the results
        verify(mockRedisTemplateString).delete("key");
    }
}
