package com.holderzone.member.mall.controller.commodity;

import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.commodity.CategoryCommodityQO;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.mall.CommodityPageQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.vo.commodity.CommodityDataVO;
import com.holderzone.member.common.vo.commodity.CommodityShareFormatVO;
import com.holderzone.member.common.vo.tool.CategoryCommodityVo;
import com.holderzone.member.common.vo.tool.CategoryVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.LinkUniteVO;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(CommodityController.class)
public class CommodityControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MemberCommodityFeign mockMemberCommodityFeign;
    @MockBean
    private HsaCommoditySetService mockHsaCommoditySetService;
    @MockBean
    private LogisticsCacheSupport mockLogisticsCacheSupport;

    @Test
    public void testGetCommodityCategory() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.getCommodityCategory(...).
        final CategoryVO categoryVO = new CategoryVO();
        categoryVO.setCategory_name("category_name");
        categoryVO.setCommodity_number(0);
        final List<CategoryVO> categoryVOS = Arrays.asList(categoryVO);
        when(mockMemberCommodityFeign.getCommodityCategory()).thenReturn(categoryVOS);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/get/commodity/category")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetCommodityCategory_MemberCommodityFeignReturnsNoItems() throws Exception {
        // Setup
        when(mockMemberCommodityFeign.getCommodityCategory()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/get/commodity/category")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetCommodityDetails() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsVO commodityDetailsVO = new CommodityDetailsVO();
        commodityDetailsVO.setS_id(0);
        commodityDetailsVO.setS_name("s_name");
        commodityDetailsVO.setStore_list(Arrays.asList(0));
        commodityDetailsVO.setCommodity_code("commodity_code");
        commodityDetailsVO.setFreight(new BigDecimal("0.00"));
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setStoreIdList(Arrays.asList(0L));
        when(mockMemberCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(commodityDetailsVO);

        when(mockLogisticsCacheSupport.getFreightAmount("commodity_code")).thenReturn(new BigDecimal("0.00"));

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/get/commodity_details")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPageCommodity() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.pageCommodity(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setType(0);
        when(mockMemberCommodityFeign.pageCommodity(request)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/page_commodity")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCategory() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategory(...).
        final LinkUniteVO linkUniteVO = new LinkUniteVO();
        linkUniteVO.setGuid("ea44c15d-0803-4c4c-86f1-18b56efa79b7");
        linkUniteVO.setName("name");
        linkUniteVO.setCommodityNumber(0);
        final List<LinkUniteVO> linkUniteVOS = Arrays.asList(linkUniteVO);
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setType(0);
        when(mockMemberCommodityFeign.listCategory(request)).thenReturn(linkUniteVOS);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCategory_MemberCommodityFeignReturnsNoItems() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategory(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setType(0);
        when(mockMemberCommodityFeign.listCategory(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListCategoryPure() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategoryPure(...).
        final CommodityDataVO commodityDataVO = new CommodityDataVO();
        commodityDataVO.setId(0);
        commodityDataVO.setCommodityCode("commodityCode");
        commodityDataVO.setCommodityName("commodityName");
        commodityDataVO.setCommodityTitle("commodityTitle");
        commodityDataVO.setCommodityImg("commodityImg");
        final List<CategoryCommodityVo> categoryCommodityVos = Arrays.asList(
                new CategoryCommodityVo(Arrays.asList(commodityDataVO), "d33e3038-1af1-4da3-b8ce-2297617892ae",
                        "name"));
        final CategoryCommodityQO request = new CategoryCommodityQO();
        request.setStoreId("storeId");
        request.setMemberInfoGuid("memberInfoGuid");
        request.setLongitude("longitude");
        request.setLatitude("latitude");
        when(mockMemberCommodityFeign.listCategoryPure(request)).thenReturn(categoryCommodityVos);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category_pure")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCategoryPure_MemberCommodityFeignReturnsNoItems() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategoryPure(...).
        final CategoryCommodityQO request = new CategoryCommodityQO();
        request.setStoreId("storeId");
        request.setMemberInfoGuid("memberInfoGuid");
        request.setLongitude("longitude");
        request.setLatitude("latitude");
        when(mockMemberCommodityFeign.listCategoryPure(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category_pure")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testListCategoryCommodity() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategoryCommodity(...).
        final CommodityDataVO commodityDataVO = new CommodityDataVO();
        commodityDataVO.setId(0);
        commodityDataVO.setCommodityCode("commodityCode");
        commodityDataVO.setCommodityName("commodityName");
        commodityDataVO.setCommodityTitle("commodityTitle");
        commodityDataVO.setCommodityImg("commodityImg");
        final List<CategoryCommodityVo> categoryCommodityVos = Arrays.asList(
                new CategoryCommodityVo(Arrays.asList(commodityDataVO), "d33e3038-1af1-4da3-b8ce-2297617892ae",
                        "name"));
        final CategoryCommodityQO conditionQO = new CategoryCommodityQO();
        conditionQO.setStoreId("storeId");
        conditionQO.setMemberInfoGuid("memberInfoGuid");
        conditionQO.setLongitude("longitude");
        conditionQO.setLatitude("latitude");
        when(mockMemberCommodityFeign.listCategoryCommodity(conditionQO)).thenReturn(categoryCommodityVos);

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category_commodity")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListCategoryCommodity_MemberCommodityFeignReturnsNoItems() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.listCategoryCommodity(...).
        final CategoryCommodityQO conditionQO = new CategoryCommodityQO();
        conditionQO.setStoreId("storeId");
        conditionQO.setMemberInfoGuid("memberInfoGuid");
        conditionQO.setLongitude("longitude");
        conditionQO.setLatitude("latitude");
        when(mockMemberCommodityFeign.listCategoryCommodity(conditionQO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/list_category_commodity")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testFindMallBaseProductPage() throws Exception {
        // Setup
        // Configure MemberCommodityFeign.findMallBaseProductPage(...).
        final CommodityPageQO query = new CommodityPageQO();
        query.setLongitude("longitude");
        query.setLatitude("latitude");
        query.setMemberInfoGuid("memberInfoGuid");
        query.setKeyword("keyword");
        query.setSortType(0);
        when(mockMemberCommodityFeign.findMallBaseProductPage(query)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/commodity/get/commodity")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetShareFormat() throws Exception {
        // Setup
        // Configure HsaCommoditySetService.getShareFormat(...).
        final CommodityShareFormatVO commodityShareFormatVO = new CommodityShareFormatVO();
        commodityShareFormatVO.setGuid("dac95e27-ab27-4a80-955b-83bd025e3619");
        commodityShareFormatVO.setShareFormat(0);
        commodityShareFormatVO.setStyle(0);
        when(mockHsaCommoditySetService.getShareFormat("operSubjectGuid")).thenReturn(commodityShareFormatVO);

        // Run the test and verify the results
        mockMvc.perform(get("/commodity/get_share_format")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
