package com.holderzone.member.mall.controller;

import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.dto.excel.FileDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(MemberFileController.class)
public class MemberFileControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileOssService mockBaseService;

    @Test
    public void testMemberUploadExcel() throws Exception {
        // Setup
        when(mockBaseService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(multipart("/file/member/memberUploadExcel")
                        .file(new MockMultipartFile("file", "originalFilename", MediaType.APPLICATION_JSON_VALUE,
                                "content".getBytes()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUploadImage() throws Exception {
        // Setup
        when(mockBaseService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(multipart("/file/member/uploadImage")
                        .file(new MockMultipartFile("file", "originalFilename", MediaType.APPLICATION_JSON_VALUE,
                                "content".getBytes()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUploadMusic() throws Exception {
        // Setup
        when(mockBaseService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(multipart("/file/member/uploadMusic")
                        .file(new MockMultipartFile("file", "originalFilename", MediaType.APPLICATION_JSON_VALUE,
                                "content".getBytes()))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
