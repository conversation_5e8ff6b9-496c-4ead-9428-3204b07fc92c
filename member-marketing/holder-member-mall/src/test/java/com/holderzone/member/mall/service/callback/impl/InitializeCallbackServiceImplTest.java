package com.holderzone.member.mall.service.callback.impl;

import com.holderzone.member.mall.manage.LogisticsTemplateManage;
import com.holderzone.member.mall.service.commodity.HsaCommoditySetService;
import com.holderzone.member.mall.service.commodity.HsaDeliverySetService;
import com.holderzone.member.mall.service.set.HsaCommonSetService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;

import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InitializeCallbackServiceImplTest {

    @Mock
    private HsaCommoditySetService mockHsaCommoditySetService;
    @Mock
    private HsaDeliverySetService mockHsaDeliverySetService;
    @Mock
    private HsaCommonSetService mockCommonSetService;
    @Mock
    private LogisticsTemplateManage mockLogisticsTemplateManage;
    @Mock
    private RedissonClient mockRedissonClient;

    @InjectMocks
    private InitializeCallbackServiceImpl initializeCallbackServiceImplUnderTest;

    @Test
    public void testInitializeSubjectData() {
        // Setup
        when(mockRedissonClient.getLock("MALL_INITIALIZE_SUBJECT_DATA")).thenReturn(null);

        // Run the test
        initializeCallbackServiceImplUnderTest.initializeSubjectData(Arrays.asList("value"));

        // Verify the results
        verify(mockHsaCommoditySetService).initShareFormat(Arrays.asList("value"));
        verify(mockHsaDeliverySetService).initDeliverySet(Arrays.asList("value"));
        verify(mockLogisticsTemplateManage).initLogisticsTemplate(Arrays.asList("value"));
        verify(mockCommonSetService).init(Arrays.asList("value"));
    }

    @Test
    public void testInitializeSubjectData_RedissonClientReturnsNull() {
        // Setup
        when(mockRedissonClient.getLock("MALL_INITIALIZE_SUBJECT_DATA")).thenReturn(null);

        // Run the test
        initializeCallbackServiceImplUnderTest.initializeSubjectData(Arrays.asList("value"));

        // Verify the results
        verify(mockHsaCommoditySetService).initShareFormat(Arrays.asList("value"));
        verify(mockHsaDeliverySetService).initDeliverySet(Arrays.asList("value"));
        verify(mockLogisticsTemplateManage).initLogisticsTemplate(Arrays.asList("value"));
        verify(mockCommonSetService).init(Arrays.asList("value"));
    }
}
