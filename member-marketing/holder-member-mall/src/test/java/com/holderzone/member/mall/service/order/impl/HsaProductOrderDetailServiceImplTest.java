package com.holderzone.member.mall.service.order.impl;

import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.order.HsaProductOrderDetail;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class HsaProductOrderDetailServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;

    @InjectMocks
    private HsaProductOrderDetailServiceImpl hsaProductOrderDetailServiceImplUnderTest;

    @Test
    public void testListByOrderGuid() {
        // Setup
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setId(0L);
        hsaProductOrderDetail.setGuid("20dae0fd-**************-10bb11138d33");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        final List<HsaProductOrderDetail> expectedResult = Arrays.asList(hsaProductOrderDetail);

        // Run the test
        final List<HsaProductOrderDetail> result = hsaProductOrderDetailServiceImplUnderTest.listByOrderGuid(
                "orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
