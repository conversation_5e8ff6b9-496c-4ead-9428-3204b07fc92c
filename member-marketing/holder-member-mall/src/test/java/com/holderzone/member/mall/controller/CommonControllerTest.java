package com.holderzone.member.mall.controller;

import com.holderzone.member.common.dto.crm.CrmCommodityDTO;
import com.holderzone.member.mall.manage.CommonManage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(CommonController.class)
public class CommonControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CommonManage mockCommonManage;

    @Test
    public void testGetCommodityUrl() throws Exception {
        // Setup
        // Configure CommonManage.getCommodityUrl(...).
        final CrmCommodityDTO commodityDTO = new CrmCommodityDTO();
        commodityDTO.setUserAccount("userAccount");
        commodityDTO.setCompanyId("companyId");
        when(mockCommonManage.getCommodityUrl(commodityDTO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/common/get_commodity_url")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
