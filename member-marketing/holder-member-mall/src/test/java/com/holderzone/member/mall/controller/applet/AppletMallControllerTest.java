package com.holderzone.member.mall.controller.applet;

import com.holderzone.member.common.qo.mall.order.OrderReceiverAddressQO;
import com.holderzone.member.common.vo.mall.OrderReceiverAddressVO;
import com.holderzone.member.mall.service.order.HsaOrderReceiverAddressService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AppletMallController.class)
public class AppletMallControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaOrderReceiverAddressService mockHsaOrderReceiverAddressService;

    @Test
    public void testSaveOrUpdate() throws Exception {
        // Setup
        // Configure HsaOrderReceiverAddressService.saveOrUpdate(...).
        final OrderReceiverAddressQO request = new OrderReceiverAddressQO();
        request.setGuid("f3d5678d-1eec-4ab1-950d-cc49f9abab9a");
        request.setMemberGuid("memberGuid");
        request.setCode("code");
        request.setRegion("region");
        request.setReceiverName("receiverName");
        when(mockHsaOrderReceiverAddressService.saveOrUpdate(request)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/applets/saveReceiverAddress")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetByMemberGuid() throws Exception {
        // Setup
        // Configure HsaOrderReceiverAddressService.getByMemberGuid(...).
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("730bba66-e41b-4ccb-9b4f-06739c319c11");
        orderReceiverAddressVO.setMemberGuid("memberGuid");
        orderReceiverAddressVO.setMemberAccount("memberAccount");
        orderReceiverAddressVO.setReceiverName("receiverName");
        orderReceiverAddressVO.setCode("code");
        final List<OrderReceiverAddressVO> orderReceiverAddressVOS = Arrays.asList(orderReceiverAddressVO);
        when(mockHsaOrderReceiverAddressService.getByMemberGuid("memberGuid")).thenReturn(orderReceiverAddressVOS);

        // Run the test and verify the results
        mockMvc.perform(get("/applets/getReceiverAddress")
                        .param("memberGuid", "memberGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetByMemberGuid_HsaOrderReceiverAddressServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaOrderReceiverAddressService.getByMemberGuid("memberGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/applets/getReceiverAddress")
                        .param("memberGuid", "memberGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDeleteByGuid() throws Exception {
        // Setup
        when(mockHsaOrderReceiverAddressService.deleteByGuid("4ecf68c4-a08e-4aa2-9a66-8c310e625b3a")).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(get("/applets/deleteByGuid")
                        .param("guid", "4ecf68c4-a08e-4aa2-9a66-8c310e625b3a")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
