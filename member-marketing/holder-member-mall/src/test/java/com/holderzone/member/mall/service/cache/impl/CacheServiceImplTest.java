package com.holderzone.member.mall.service.cache.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CacheServiceImplTest {

    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;

    @InjectMocks
    private CacheServiceImpl cacheServiceImplUnderTest;

    @Test
    public void testSetMallOrderCache() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.setMallOrderCache("mallOrderKey", "value", 0);

        // Verify the results
    }

    @Test
    public void testGetMallOrderCache() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = cacheServiceImplUnderTest.getMallOrderCache("mallOrderKey");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testCacheValueQuery() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = cacheServiceImplUnderTest.cacheValueQuery("key");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testCacheValueSetHours() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.cacheValueSetHours("key", "value", 0L);

        // Verify the results
    }

    @Test
    public void testCacheValueSet() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        cacheServiceImplUnderTest.cacheValueSet("key", "value");

        // Verify the results
    }

    @Test
    public void testCacheValueDelete() {
        // Setup
        // Run the test
        cacheServiceImplUnderTest.cacheValueDelete("key");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }
}
