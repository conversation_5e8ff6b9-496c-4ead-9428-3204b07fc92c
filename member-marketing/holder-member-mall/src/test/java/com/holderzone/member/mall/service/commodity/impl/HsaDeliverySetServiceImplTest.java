package com.holderzone.member.mall.service.commodity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.mall.entity.commodity.HsaDeliverySet;
import com.holderzone.member.mall.mapper.commodity.HsaDeliverySetMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaDeliverySetServiceImplTest {

    @Mock
    private HsaDeliverySetMapper mockHsaDeliverySetMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplateString;

    @InjectMocks
    private HsaDeliverySetServiceImpl hsaDeliverySetServiceImplUnderTest;

    @Test
    public void testGetDeliverySet() {
        // Setup
        final DeliverySetVO expectedResult = new DeliverySetVO();
        expectedResult.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        expectedResult.setIsExpressDelivery(0);
        expectedResult.setIsSelectAll(0);
        expectedResult.setLogistics(Arrays.asList("value"));
        expectedResult.setFreightMode(0);

        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaDeliverySetMapper.selectOne(...).
        final HsaDeliverySet hsaDeliverySet = new HsaDeliverySet();
        hsaDeliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        hsaDeliverySet.setOperSubjectGuid("operSubjectGuid");
        hsaDeliverySet.setIsExpressDelivery(0);
        hsaDeliverySet.setIsSelectAll(0);
        hsaDeliverySet.setLogistics("logistics");
        hsaDeliverySet.setFreightMode(0);
        hsaDeliverySet.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaDeliverySet);

        // Run the test
        final DeliverySetVO result = hsaDeliverySetServiceImplUnderTest.getDeliverySet("operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateExpressState() {
        // Setup
        final DeliverySetVO vo = new DeliverySetVO();
        vo.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        vo.setIsExpressDelivery(0);
        vo.setIsSelectAll(0);
        vo.setLogistics(Arrays.asList("value"));
        vo.setFreightMode(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");

        // Configure HsaDeliverySetMapper.queryByGuid(...).
        final HsaDeliverySet hsaDeliverySet = new HsaDeliverySet();
        hsaDeliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        hsaDeliverySet.setOperSubjectGuid("operSubjectGuid");
        hsaDeliverySet.setIsExpressDelivery(0);
        hsaDeliverySet.setIsSelectAll(0);
        hsaDeliverySet.setLogistics("logistics");
        hsaDeliverySet.setFreightMode(0);
        hsaDeliverySet.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.queryByGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24")).thenReturn(hsaDeliverySet);

        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Run the test
        hsaDeliverySetServiceImplUnderTest.updateExpressState(vo);

        // Verify the results
    }

    @Test
    public void testUpdateDeliverySet() {
        // Setup
        final DeliverySetVO deliverySet = new DeliverySetVO();
        deliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        deliverySet.setIsExpressDelivery(0);
        deliverySet.setIsSelectAll(0);
        deliverySet.setLogistics(Arrays.asList("value"));
        deliverySet.setFreightMode(0);

        // Configure HsaDeliverySetMapper.queryByGuid(...).
        final HsaDeliverySet hsaDeliverySet = new HsaDeliverySet();
        hsaDeliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        hsaDeliverySet.setOperSubjectGuid("operSubjectGuid");
        hsaDeliverySet.setIsExpressDelivery(0);
        hsaDeliverySet.setIsSelectAll(0);
        hsaDeliverySet.setLogistics("logistics");
        hsaDeliverySet.setFreightMode(0);
        hsaDeliverySet.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.queryByGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24")).thenReturn(hsaDeliverySet);

        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaDeliverySetMapper.updateByGuid(...).
        final HsaDeliverySet t = new HsaDeliverySet();
        t.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setIsExpressDelivery(0);
        t.setIsSelectAll(0);
        t.setLogistics("logistics");
        t.setFreightMode(0);
        t.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.updateByGuid(t)).thenReturn(false);

        // Run the test
        final Boolean result = hsaDeliverySetServiceImplUnderTest.updateDeliverySet(deliverySet);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateDeliverySet_HsaDeliverySetMapperUpdateByGuidReturnsTrue() {
        // Setup
        final DeliverySetVO deliverySet = new DeliverySetVO();
        deliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        deliverySet.setIsExpressDelivery(0);
        deliverySet.setIsSelectAll(0);
        deliverySet.setLogistics(Arrays.asList("value"));
        deliverySet.setFreightMode(0);

        // Configure HsaDeliverySetMapper.queryByGuid(...).
        final HsaDeliverySet hsaDeliverySet = new HsaDeliverySet();
        hsaDeliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        hsaDeliverySet.setOperSubjectGuid("operSubjectGuid");
        hsaDeliverySet.setIsExpressDelivery(0);
        hsaDeliverySet.setIsSelectAll(0);
        hsaDeliverySet.setLogistics("logistics");
        hsaDeliverySet.setFreightMode(0);
        hsaDeliverySet.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.queryByGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24")).thenReturn(hsaDeliverySet);

        when(mockRedisTemplateString.opsForValue()).thenReturn(null);

        // Configure HsaDeliverySetMapper.updateByGuid(...).
        final HsaDeliverySet t = new HsaDeliverySet();
        t.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setIsExpressDelivery(0);
        t.setIsSelectAll(0);
        t.setLogistics("logistics");
        t.setFreightMode(0);
        t.setOperatorName("管理员");
        when(mockHsaDeliverySetMapper.updateByGuid(t)).thenReturn(true);

        // Run the test
        final Boolean result = hsaDeliverySetServiceImplUnderTest.updateDeliverySet(deliverySet);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testInitDeliverySet() {
        // Setup
        // Configure HsaDeliverySetMapper.selectList(...).
        final HsaDeliverySet hsaDeliverySet = new HsaDeliverySet();
        hsaDeliverySet.setGuid("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");
        hsaDeliverySet.setOperSubjectGuid("operSubjectGuid");
        hsaDeliverySet.setIsExpressDelivery(0);
        hsaDeliverySet.setIsSelectAll(0);
        hsaDeliverySet.setLogistics("logistics");
        hsaDeliverySet.setFreightMode(0);
        hsaDeliverySet.setOperatorName("管理员");
        final List<HsaDeliverySet> hsaDeliverySets = Arrays.asList(hsaDeliverySet);
        when(mockHsaDeliverySetMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaDeliverySets);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");

        // Run the test
        hsaDeliverySetServiceImplUnderTest.initDeliverySet(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testInitDeliverySet_HsaDeliverySetMapperReturnsNoItems() {
        // Setup
        when(mockHsaDeliverySetMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("08bdce30-7ca3-4e5e-a0cb-ab8f3d62dc24");

        // Run the test
        hsaDeliverySetServiceImplUnderTest.initDeliverySet(Arrays.asList("value"));

        // Verify the results
    }
}
