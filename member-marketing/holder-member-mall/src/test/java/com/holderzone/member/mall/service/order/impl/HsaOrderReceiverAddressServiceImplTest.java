package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.mall.order.OrderReceiverAddressQO;
import com.holderzone.member.common.qo.tool.CheckContentQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.feign.CrmFeignVo;
import com.holderzone.member.common.vo.mall.OrderReceiverAddressVO;
import com.holderzone.member.common.vo.tool.CheckContentVO;
import com.holderzone.member.mall.entity.order.HsaOrderReceiverAddress;
import com.holderzone.member.mall.mapper.order.HsaOrderReceiverAddressMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaOrderReceiverAddressServiceImplTest {

    @Mock
    private HsaOrderReceiverAddressMapper mockHsaOrderReceiverAddressMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private RedisTemplate<String, String> mockStringRedisTemplate;
    @Mock
    private CrmFeign mockCrmFeign;

    @InjectMocks
    private HsaOrderReceiverAddressServiceImpl hsaOrderReceiverAddressServiceImplUnderTest;

    @Test
    public void testSaveOrUpdate() {
        // Setup
        final OrderReceiverAddressQO request = new OrderReceiverAddressQO();
        request.setGuid("guid");
        request.setMemberGuid("memberGuid");
        request.setReceiverName("receiverName");
        request.setAddressType(0);
        request.setReceiverAddress("receiverAddress");
        request.setHouseNumber("houseNumber");
        request.setAddressLabel("addressLabel");
        request.setStore_id("store_id");

        // Configure CrmFeign.checkKeyWords(...).
        final CrmFeignVo<CheckContentVO> checkContentVOCrmFeignVo = new CrmFeignVo<>();
        checkContentVOCrmFeignVo.setReturnCode(0);
        checkContentVOCrmFeignVo.setReturnMessage("returnMessage");
        final CheckContentVO checkContentVO = new CheckContentVO();
        checkContentVO.setName("name");
        checkContentVO.setKey_list(Arrays.asList("value"));
        checkContentVOCrmFeignVo.setData(checkContentVO);
        final CheckContentQO checkContentQO = new CheckContentQO();
        checkContentQO.setSearch_word("search_word");
        checkContentQO.setSensitive_disable(false);
        checkContentQO.setLongitude("longitude");
        checkContentQO.setLatitude("latitude");
        when(mockCrmFeign.checkKeyWords(checkContentQO)).thenReturn(checkContentVOCrmFeignVo);

        // Configure HsaOrderReceiverAddressMapper.selectOne(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("guid");
        hsaOrderReceiverAddress.setOperSubjectGuid("operSubjectGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setReceiverType(0);
        hsaOrderReceiverAddress.setReceiverSex(0);
        hsaOrderReceiverAddress.setAddressLabel("addressLabel");
        hsaOrderReceiverAddress.setDefaultAddress(0);
        hsaOrderReceiverAddress.setReceiverJson("receiverJson");
        hsaOrderReceiverAddress.setStoreId("store_id");
        hsaOrderReceiverAddress.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaOrderReceiverAddressMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddress);

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress1 = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress1.setGuid("guid");
        hsaOrderReceiverAddress1.setOperSubjectGuid("operSubjectGuid");
        hsaOrderReceiverAddress1.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress1.setReceiverType(0);
        hsaOrderReceiverAddress1.setReceiverSex(0);
        hsaOrderReceiverAddress1.setAddressLabel("addressLabel");
        hsaOrderReceiverAddress1.setDefaultAddress(0);
        hsaOrderReceiverAddress1.setReceiverJson("receiverJson");
        hsaOrderReceiverAddress1.setStoreId("store_id");
        hsaOrderReceiverAddress1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("guid")).thenReturn(hsaOrderReceiverAddress1);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("guid");

        // Run the test
        final String result = hsaOrderReceiverAddressServiceImplUnderTest.saveOrUpdate(request);

        // Verify the results
        assertThat(result).isEqualTo("guid");

        // Confirm HsaOrderReceiverAddressMapper.updateByGuid(...).
        final HsaOrderReceiverAddress t = new HsaOrderReceiverAddress();
        t.setGuid("guid");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setMemberGuid("memberGuid");
        t.setReceiverType(0);
        t.setReceiverSex(0);
        t.setAddressLabel("addressLabel");
        t.setDefaultAddress(0);
        t.setReceiverJson("receiverJson");
        t.setStoreId("store_id");
        t.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockHsaOrderReceiverAddressMapper).updateByGuid(t);

        // Confirm HsaOrderReceiverAddressMapper.insert(...).
        final HsaOrderReceiverAddress t1 = new HsaOrderReceiverAddress();
        t1.setGuid("guid");
        t1.setOperSubjectGuid("operSubjectGuid");
        t1.setMemberGuid("memberGuid");
        t1.setReceiverType(0);
        t1.setReceiverSex(0);
        t1.setAddressLabel("addressLabel");
        t1.setDefaultAddress(0);
        t1.setReceiverJson("receiverJson");
        t1.setStoreId("store_id");
        t1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockHsaOrderReceiverAddressMapper).insert(t1);
    }

    @Test
    public void testGetByMemberGuid() {
        // Setup
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("174328d3-257d-41c2-864b-2a53407ed06a");
        orderReceiverAddressVO.setReceiverSex(0);
        orderReceiverAddressVO.setReceiverJson("receiverJson");
        orderReceiverAddressVO.setReceiverType(0);
        orderReceiverAddressVO.setStoreId("store_id");
        final List<OrderReceiverAddressVO> expectedResult = Arrays.asList(orderReceiverAddressVO);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("guid");
        hsaOrderReceiverAddress.setOperSubjectGuid("operSubjectGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setReceiverType(0);
        hsaOrderReceiverAddress.setReceiverSex(0);
        hsaOrderReceiverAddress.setAddressLabel("addressLabel");
        hsaOrderReceiverAddress.setDefaultAddress(0);
        hsaOrderReceiverAddress.setReceiverJson("receiverJson");
        hsaOrderReceiverAddress.setStoreId("store_id");
        hsaOrderReceiverAddress.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final List<OrderReceiverAddressVO> result = hsaOrderReceiverAddressServiceImplUnderTest.getByMemberGuid(
                "memberGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetByMemberGuid_HsaOrderReceiverAddressMapperReturnsNoItems() {
        // Setup
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderReceiverAddressVO> result = hsaOrderReceiverAddressServiceImplUnderTest.getByMemberGuid(
                "memberGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteByGuid() {
        // Setup
        when(mockHsaOrderReceiverAddressMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Run the test
        final boolean result = hsaOrderReceiverAddressServiceImplUnderTest.deleteByGuid(
                "769dc8bb-351f-4736-9f1d-c2260402bbdc");

        // Verify the results
        assertThat(result).isFalse();
        verify(mockStringRedisTemplate).delete("key");
    }

    @Test
    public void testGetByGuid() {
        // Setup
        final OrderReceiverAddressVO expectedResult = new OrderReceiverAddressVO();
        expectedResult.setGuid("174328d3-257d-41c2-864b-2a53407ed06a");
        expectedResult.setReceiverSex(0);
        expectedResult.setReceiverJson("receiverJson");
        expectedResult.setReceiverType(0);
        expectedResult.setStoreId("store_id");

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final OrderReceiverAddressVO result = hsaOrderReceiverAddressServiceImplUnderTest.getByGuid(
                "08201f0b-00b7-49cf-ac38-f9311ff06af7");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
