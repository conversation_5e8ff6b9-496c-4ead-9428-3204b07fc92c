package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.mall.entity.order.HsaMallOrderTimeRule;
import com.holderzone.member.mall.mapper.order.HsaOrderAutoConfigMapper;
import com.holderzone.member.mall.service.order.HsaMallOrderTimeRuleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaOrderAutoConfigServiceImplTest {

    @Mock
    private HsaMallOrderTimeRuleService mockOrderTimeRuleService;
    @Mock
    private HsaOrderAutoConfigMapper mockOrderAutoConfigMapper;

    private HsaOrderAutoConfigServiceImpl hsaOrderAutoConfigServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaOrderAutoConfigServiceImplUnderTest = new HsaOrderAutoConfigServiceImpl(mockOrderTimeRuleService,
                mockOrderAutoConfigMapper);
    }

    @Test
    public void testSaveOrderAutoConfigByEvent() {
        // Setup
        // Configure HsaMallOrderTimeRuleService.getOne(...).
        final HsaMallOrderTimeRule hsaMallOrderTimeRule = new HsaMallOrderTimeRule();
        hsaMallOrderTimeRule.setId(0L);
        hsaMallOrderTimeRule.setGuid("dcecc181-2b51-480a-b349-1ae922ba5432");
        hsaMallOrderTimeRule.setOperSubjectGuid("operSubjectGuid");
        hsaMallOrderTimeRule.setPayTimeValue(0);
        hsaMallOrderTimeRule.setTakeTimeValue(0);
        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallOrderTimeRule);

        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderAutoConfigByEvent("content");

        // Verify the results
    }

    @Test
    public void testSaveOrderAutoConfigByEvent_HsaMallOrderTimeRuleServiceReturnsNull() {
        // Setup
        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderAutoConfigByEvent("content");

        // Verify the results
    }

    @Test
    public void testSaveOrderAutoCancelConfig() {
        // Setup
        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderAutoCancelConfig(0, "orderGuid");

        // Verify the results
    }

    @Test
    public void testSaveOrderAutoReceiveConfig() {
        // Setup
        // Configure HsaMallOrderTimeRuleService.getOne(...).
        final HsaMallOrderTimeRule hsaMallOrderTimeRule = new HsaMallOrderTimeRule();
        hsaMallOrderTimeRule.setId(0L);
        hsaMallOrderTimeRule.setGuid("dcecc181-2b51-480a-b349-1ae922ba5432");
        hsaMallOrderTimeRule.setOperSubjectGuid("operSubjectGuid");
        hsaMallOrderTimeRule.setPayTimeValue(0);
        hsaMallOrderTimeRule.setTakeTimeValue(0);
        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallOrderTimeRule);

        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderAutoReceiveConfig("operSubjectGuid", "orderGuid");

        // Verify the results
    }

    @Test
    public void testSaveOrderAutoReceiveConfig_HsaMallOrderTimeRuleServiceReturnsNull() {
        // Setup
        when(mockOrderTimeRuleService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderAutoReceiveConfig("operSubjectGuid", "orderGuid");

        // Verify the results
    }

    @Test
    public void testSaveOrderRefundConfirmConfig() {
        // Setup
        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.saveOrderRefundConfirmConfig(0, "orderGuid");

        // Verify the results
    }

    @Test
    public void testDeleteRefundConfirmTime() {
        // Setup
        // Run the test
        hsaOrderAutoConfigServiceImplUnderTest.deleteRefundConfirmTime(Arrays.asList("value"));

        // Verify the results
        verify(mockOrderAutoConfigMapper).deleteRefundConfirmTime(Arrays.asList("value"));
    }
}
