package com.holderzone.member.mall.service.order.aggregation.impl;

import com.holderzone.member.common.dto.order.aggregation.OrderGuidsDTO;
import com.holderzone.member.common.dto.order.aggregation.StoreOrderDTO;
import com.holderzone.member.common.dto.order.aggregation.StoreOrderItemDTO;
import com.holderzone.member.common.feign.SaasStoreFeign;
import com.holderzone.member.common.qo.order.AggregationOrderDetailResponseVO;
import com.holderzone.member.common.qo.order.AggregationOrderResponseVO;
import com.holderzone.member.mall.entity.order.HsaAggregationOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreOrderServiceImplTest {

    @Mock
    private SaasStoreFeign mockSaasStoreFeign;

    private StoreOrderServiceImpl storeOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        storeOrderServiceImplUnderTest = new StoreOrderServiceImpl(mockSaasStoreFeign);
    }

    @Test
    public void testList() {
        // Setup
        final AggregationOrderResponseVO aggregationOrderResponseVO = new AggregationOrderResponseVO();
        aggregationOrderResponseVO.setGuid("63422b2f-333b-4078-9125-6eb9f0c0ed4e");
        aggregationOrderResponseVO.setThirdOrderGuid("thirdOrderGuid");
        final AggregationOrderResponseVO.InnerSku innerSku = new AggregationOrderResponseVO.InnerSku();
        innerSku.setImg("");
        innerSku.setName("itemName");
        aggregationOrderResponseVO.setSkuList(Arrays.asList(innerSku));
        final List<AggregationOrderResponseVO> expectedResult = Arrays.asList(aggregationOrderResponseVO);

        // Configure SaasStoreFeign.getStoreOrderItems(...).
        final StoreOrderItemDTO storeOrderItemDTO = new StoreOrderItemDTO();
        storeOrderItemDTO.setOrderGuid(0L);
        storeOrderItemDTO.setItemName("itemName");
        storeOrderItemDTO.setSkuName("itemName");
        storeOrderItemDTO.setPrice(new BigDecimal("0.00"));
        storeOrderItemDTO.setCurrentCount(new BigDecimal("0.00"));
        storeOrderItemDTO.setFreeCount(new BigDecimal("0.00"));
        storeOrderItemDTO.setSmallPicture("smallPicture");
        storeOrderItemDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final StoreOrderItemDTO.InnerItemAttr innerItemAttr = new StoreOrderItemDTO.InnerItemAttr();
        innerItemAttr.setAttrName("attrName");
        innerItemAttr.setAttrPrice(new BigDecimal("0.00"));
        storeOrderItemDTO.setItemAttr(Arrays.asList(innerItemAttr));
        final List<StoreOrderItemDTO> storeOrderItemDTOS = Arrays.asList(storeOrderItemDTO);
        when(mockSaasStoreFeign.getStoreOrderItems(
                new OrderGuidsDTO(Arrays.asList("value"), "orderGuid", "enterpriseGuid")))
                .thenReturn(storeOrderItemDTOS);

        // Run the test
        final List<AggregationOrderResponseVO> result = storeOrderServiceImplUnderTest.list("enterpriseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testList_SaasStoreFeignReturnsNoItems() {
        // Setup
        when(mockSaasStoreFeign.getStoreOrderItems(
                new OrderGuidsDTO(Arrays.asList("value"), "orderGuid", "enterpriseGuid")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<AggregationOrderResponseVO> result = storeOrderServiceImplUnderTest.list("enterpriseGuid",
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetDetail() {
        // Setup
        final HsaAggregationOrder aggregationOrder = new HsaAggregationOrder();
        aggregationOrder.setId(0L);
        aggregationOrder.setGuid("d96b9011-bd37-44d9-bc9f-e5a9edc02f9f");
        aggregationOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        aggregationOrder.setThirdEnterpriseGuid("enterpriseGuid");
        aggregationOrder.setThirdOrderGuid("orderGuid");

        final AggregationOrderDetailResponseVO expectedResult = new AggregationOrderDetailResponseVO();
        expectedResult.setGuid("0a2e9d7b-1880-42ac-bd6a-7a27a92c3339");
        expectedResult.setDiningTableName("diningTableName");
        expectedResult.setGuestCount(0);
        expectedResult.setOrderNo("orderNo");
        expectedResult.setItemCount(0);
        final AggregationOrderDetailResponseVO.InnerSku innerSku = new AggregationOrderDetailResponseVO.InnerSku();
        innerSku.setImg("img");
        innerSku.setName("itemName");
        innerSku.setSkuName("itemName");
        innerSku.setAttrPrice(new BigDecimal("0.00"));
        innerSku.setCount(new BigDecimal("0.00"));
        innerSku.setPrice(new BigDecimal("0.00"));
        expectedResult.setSkuList(Arrays.asList(innerSku));
        final AggregationOrderDetailResponseVO.InnerAppendFee innerAppendFee = new AggregationOrderDetailResponseVO.InnerAppendFee();
        innerAppendFee.setName("name");
        innerAppendFee.setPrice(new BigDecimal("0.00"));
        expectedResult.setAppendFeeList(Arrays.asList(innerAppendFee));
        final AggregationOrderDetailResponseVO.InnerDiscount innerDiscount = new AggregationOrderDetailResponseVO.InnerDiscount();
        innerDiscount.setType(0);
        innerDiscount.setName("discountName");
        innerDiscount.setPrice(new BigDecimal("0.00"));
        expectedResult.setDiscountList(Arrays.asList(innerDiscount));
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        expectedResult.setDiscountFee(new BigDecimal("0.00"));
        expectedResult.setAppendFee(new BigDecimal("0.00"));
        expectedResult.setItemTotalFee(new BigDecimal("0.00"));

        // Configure SaasStoreFeign.getStoreOrderDetail(...).
        final StoreOrderDTO storeOrderDTO = new StoreOrderDTO();
        storeOrderDTO.setOrderRecordGuid("orderNo");
        final StoreOrderDTO.InnerOrder orderDTO = new StoreOrderDTO.InnerOrder();
        orderDTO.setGuid("0a2e9d7b-1880-42ac-bd6a-7a27a92c3339");
        orderDTO.setAppendFee(new BigDecimal("0.00"));
        orderDTO.setDiningTableName("diningTableName");
        orderDTO.setGuestCount(0);
        storeOrderDTO.setOrderDTO(orderDTO);
        final StoreOrderItemDTO storeOrderItemDTO = new StoreOrderItemDTO();
        storeOrderItemDTO.setOrderGuid(0L);
        storeOrderItemDTO.setItemName("itemName");
        storeOrderItemDTO.setSkuName("itemName");
        storeOrderItemDTO.setPrice(new BigDecimal("0.00"));
        storeOrderItemDTO.setCurrentCount(new BigDecimal("0.00"));
        storeOrderItemDTO.setFreeCount(new BigDecimal("0.00"));
        storeOrderItemDTO.setSmallPicture("smallPicture");
        storeOrderItemDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final StoreOrderItemDTO.InnerItemAttr innerItemAttr = new StoreOrderItemDTO.InnerItemAttr();
        innerItemAttr.setAttrName("attrName");
        innerItemAttr.setAttrPrice(new BigDecimal("0.00"));
        storeOrderItemDTO.setItemAttr(Arrays.asList(innerItemAttr));
        storeOrderDTO.setOrderItemDTOList(Arrays.asList(storeOrderItemDTO));
        final StoreOrderDTO.InnerDiscount innerDiscount1 = new StoreOrderDTO.InnerDiscount();
        innerDiscount1.setDiscountName("discountName");
        innerDiscount1.setDiscountType(0);
        innerDiscount1.setDiscountFee(new BigDecimal("0.00"));
        storeOrderDTO.setDiscountInfoList(Arrays.asList(innerDiscount1));
        final StoreOrderDTO.InnerAppendFee innerAppendFee1 = new StoreOrderDTO.InnerAppendFee();
        innerAppendFee1.setAmount(new BigDecimal("0.00"));
        innerAppendFee1.setName("name");
        storeOrderDTO.setAppendFeeDetailList(Arrays.asList(innerAppendFee1));
        when(mockSaasStoreFeign.getStoreOrderDetail(
                new OrderGuidsDTO(Arrays.asList("value"), "orderGuid", "enterpriseGuid"))).thenReturn(storeOrderDTO);

        // Run the test
        final AggregationOrderDetailResponseVO result = storeOrderServiceImplUnderTest.getDetail(aggregationOrder);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
