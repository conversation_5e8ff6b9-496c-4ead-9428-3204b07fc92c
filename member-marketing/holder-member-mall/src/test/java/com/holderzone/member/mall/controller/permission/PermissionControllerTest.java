package com.holderzone.member.mall.controller.permission;

import com.holderzone.member.common.dto.member.FunctionModelDTO;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.service.permission.HsaSubjectPermissionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(PermissionController.class)
public class PermissionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaSubjectPermissionService mockHsaSubjectPermissionService;

    @Test
    public void testGetAccountPermission() throws Exception {
        // Setup
        // Configure HsaSubjectPermissionService.getAccountPermission(...).
        final MemberSystemPermissionVO memberSystemPermissionVO = new MemberSystemPermissionVO();
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setId(0);
        memberSystemPermissionDTO.setName("name");
        memberSystemPermissionDTO.setIsFormPermissions(0);
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        memberSystemPermissionVO.setMemberSystemPermissionDTOs(Arrays.asList(memberSystemPermissionDTO));
        when(mockHsaSubjectPermissionService.getAccountPermission("des")).thenReturn(memberSystemPermissionVO);

        // Run the test and verify the results
        mockMvc.perform(get("/member_permission/get_account_permission")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetMarketingOperSubjectPermission() throws Exception {
        // Setup
        // Configure HsaSubjectPermissionService.getSubjectPermissionList(...).
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("operSubjectGuid");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        hsaOperSubjectPermissionVO.setLogo("logo");
        final List<HsaOperSubjectPermissionVO> hsaOperSubjectPermissionVOS = Arrays.asList(hsaOperSubjectPermissionVO);
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("roleId");
        request.setIsRole(0);
        request.setTeamId("teamId");
        request.setSourceType(0);
        request.setIsAll(0);
        when(mockHsaSubjectPermissionService.getSubjectPermissionList(request)).thenReturn(hsaOperSubjectPermissionVOS);

        // Run the test and verify the results
        mockMvc.perform(post("/member_permission/get_mall_oper_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetMarketingOperSubjectPermission_HsaSubjectPermissionServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaSubjectPermissionService.getSubjectPermissionList(...).
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("roleId");
        request.setIsRole(0);
        request.setTeamId("teamId");
        request.setSourceType(0);
        request.setIsAll(0);
        when(mockHsaSubjectPermissionService.getSubjectPermissionList(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/member_permission/get_mall_oper_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateMarketingOperSubjectPermission() throws Exception {
        // Setup
        // Configure HsaSubjectPermissionService.updateSubjectPermission(...).
        final HsaOperSubjectPermissionQO request = new HsaOperSubjectPermissionQO();
        request.setSourceType(0);
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("operSubjectGuid");
        hsaOperSubjectPermissionVO.setName("name");
        request.setHolderPermission(Arrays.asList(hsaOperSubjectPermissionVO));
        when(mockHsaSubjectPermissionService.updateSubjectPermission(request)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/member_permission/update_mall_oper_subject")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetOperatingSubjectInfo() throws Exception {
        // Setup
        // Configure HsaSubjectPermissionService.getOperatingSubject(...).
        final OperSubjectInfoVO operSubjectInfoVO = new OperSubjectInfoVO();
        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        operSubjectInfoVO.setOperSubjectInfos(Arrays.asList(operSubjectInfo));
        operSubjectInfoVO.setEnterpriseGuid("enterpriseGuid");
        when(mockHsaSubjectPermissionService.getOperatingSubject()).thenReturn(operSubjectInfoVO);

        // Run the test and verify the results
        mockMvc.perform(get("/member_permission/getOperatingSubjectInfo")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
