package com.holderzone.member.mall.controller.order;

import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.CrmFeign;
import com.holderzone.member.common.qo.mall.order.*;
import com.holderzone.member.common.vo.mall.AppletNegotiationHistoryVO;
import com.holderzone.member.common.vo.mall.PayOrderVO;
import com.holderzone.member.common.vo.mall.RefundNegotiationHistoryVO;
import com.holderzone.member.common.vo.order.*;
import com.holderzone.member.mall.service.order.*;
import com.holderzone.member.mall.support.UserValidateSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(MallOrderController.class)
public class MallOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaMallBaseOrderService mockHsaMallBaseOrderService;
    @MockBean
    private HsaMallOrderTimeRuleService mockHsaMallOrderTimeRuleService;
    @MockBean
    private HsaAfterSaleOrderService mockHsaAfterSaleOrderService;
    @MockBean
    private HsaCancelOrderRuleService mockHsaCancelOrderRuleService;
    @MockBean
    private HsaRefundNegotiationHistoryService mockHsaRefundNegotiationHistoryService;
    @MockBean
    private UserValidateSupport mockUserValidateSupport;
    @MockBean
    private CrmFeign mockCrmFeign;

    @Test
    public void testFindMallBaseOrderPage() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.findMallBaseOrderPage(...).
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");
        queryMallOrderQO.setMemberName("memberName");
        queryMallOrderQO.setMemberPhone("memberPhone");
        when(mockHsaMallBaseOrderService.findMallBaseOrderPage(queryMallOrderQO)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/find_mall_order_page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindMallBaseOrderDetails() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.findMallBaseOrderDetails(...).
        final MallBaseOrderDetailsVO mallBaseOrderDetailsVO = new MallBaseOrderDetailsVO();
        mallBaseOrderDetailsVO.setGuid("e49e1249-644d-403c-aa7f-3429e0a6dd54");
        mallBaseOrderDetailsVO.setOrderNumber("orderNumber");
        mallBaseOrderDetailsVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderDetailsVO.setOrderCondition(0);
        mallBaseOrderDetailsVO.setSurplusAfterTime(0L);
        when(mockHsaMallBaseOrderService.findMallBaseOrderDetails("orderGuid")).thenReturn(mallBaseOrderDetailsVO);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/find_mall_order_details")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindMallBaseOrderProduct() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.findMallBaseOrderProduct(...).
        final ResOrderCommodity resOrderCommodity = new ResOrderCommodity();
        resOrderCommodity.setOrder_code("order_code");
        resOrderCommodity.setProductId(0);
        resOrderCommodity.setProductCode("productCode");
        resOrderCommodity.setProduct_name("product_name");
        resOrderCommodity.setCommodity_img("commodity_img");
        final List<ResOrderCommodity> resOrderCommodities = Arrays.asList(resOrderCommodity);
        when(mockHsaMallBaseOrderService.findMallBaseOrderProduct("orderNum")).thenReturn(resOrderCommodities);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/find_mall_order_product")
                        .param("orderNum", "orderNum")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindMallBaseOrderProduct_HsaMallBaseOrderServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaMallBaseOrderService.findMallBaseOrderProduct("orderNum")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/find_mall_order_product")
                        .param("orderNum", "orderNum")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testMallBaseOrderExport() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.mallBaseOrderExport(...).
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");
        queryMallOrderQO.setMemberName("memberName");
        queryMallOrderQO.setMemberPhone("memberPhone");
        when(mockHsaMallBaseOrderService.mallBaseOrderExport(queryMallOrderQO)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/mall_order_export")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveMallOrderTimeRule() throws Exception {
        // Setup
        // Configure HsaMallOrderTimeRuleService.saveMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("854c1bbc-60bf-4b0d-932a-d2f772789ac4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.saveMallOrderTimeRule(mallOrderTimeRuleVO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/saveMallOrderTimeRule")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetMallOrderTimeRule() throws Exception {
        // Setup
        // Configure HsaMallOrderTimeRuleService.getMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("854c1bbc-60bf-4b0d-932a-d2f772789ac4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.getMallOrderTimeRule()).thenReturn(mallOrderTimeRuleVO);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/getMallOrderTimeRule")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCheckOrder() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.checkOrder(...).
        final AppletMallOrderVO appletMallOrderVO = new AppletMallOrderVO();
        appletMallOrderVO.setMemberGuid("memberGuid");
        appletMallOrderVO.setFreight(new BigDecimal("0.00"));
        appletMallOrderVO.setIsCheck(0);
        appletMallOrderVO.setCheckType(0);
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        appletMallOrderVO.setProductAmountQOS(Arrays.asList(orderProductAmountQO));
        final AppletMallOrderQO request = new AppletMallOrderQO();
        request.setStoreGuid("storeGuid");
        request.setStoreName("storeName");
        request.setSource("source");
        request.setMemberGuid("memberGuid");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        request.setProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockHsaMallBaseOrderService.checkOrder(request)).thenReturn(appletMallOrderVO);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/checkOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPayMallOrder1() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.savePayMallOrder(...).
        final PayOrderVO payOrderVO = new PayOrderVO();
        payOrderVO.setMallOrderGuid("mallOrderGuid");
        payOrderVO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        payOrderVO.setOrderNumber("orderNumber");
        payOrderVO.setIsCheck(0);
        payOrderVO.setCheckType(0);
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setGuid("1de6c082-2205-4565-813f-d0b92a86a03a");
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhoneNum");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        when(mockHsaMallBaseOrderService.savePayMallOrder(savePayOrderQO)).thenReturn(payOrderVO);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/createOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetSingleFreightAmount() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.getSingleFreightAmount(...).
        final GetFreightAmount getFreightAmount = new GetFreightAmount();
        getFreightAmount.setProvince("province");
        getFreightAmount.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("productName");
        getFreightAmount.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        when(mockHsaMallBaseOrderService.getSingleFreightAmount(getFreightAmount)).thenReturn(new BigDecimal("0.00"));

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/getFreightAmount")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetFreightAmount() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.getFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount getFreightAmount = new GetFreightAmount();
        getFreightAmount.setProvince("province");
        getFreightAmount.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("productName");
        getFreightAmount.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        when(mockHsaMallBaseOrderService.getFreightAmount(getFreightAmount)).thenReturn(mallOrderFreightAmountVO);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/freight_amount")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testPayMallOrder2() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.payMallOrder(...).
        final PayOrderVO payOrderVO = new PayOrderVO();
        payOrderVO.setMallOrderGuid("mallOrderGuid");
        payOrderVO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        payOrderVO.setOrderNumber("orderNumber");
        payOrderVO.setIsCheck(0);
        payOrderVO.setCheckType(0);
        final PayOrderQO savePayOrderQO = new PayOrderQO();
        savePayOrderQO.setGuid("838cba7c-3031-4ef3-8fa3-15fb94132417");
        savePayOrderQO.setOpenId("openId");
        savePayOrderQO.setAppId("appId");
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setIsCredit(0);
        when(mockHsaMallBaseOrderService.payMallOrder(savePayOrderQO)).thenReturn(payOrderVO);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/payOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderShipDea() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.orderShipDea(...).
        final OrderShipQO orderShipQO = new OrderShipQO();
        orderShipQO.setOrderGuid("orderGuid");
        orderShipQO.setDistributionGuid("distributionGuid");
        orderShipQO.setLogistic("logistic");
        orderShipQO.setLogisticNum("logisticNum");
        when(mockHsaMallBaseOrderService.orderShipDea(orderShipQO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/orderShipDea")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMemberSynOrder() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/memberSynOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaMallBaseOrderService.memberSynOrder(...).
        final MemberSynOrderQO memberSynOrderQO = new MemberSynOrderQO();
        memberSynOrderQO.setMemberInfoGuid("memberInfoGuid");
        memberSynOrderQO.setMemberPhoneNum("memberPhoneNum");
        memberSynOrderQO.setMemberName("memberName");
        verify(mockHsaMallBaseOrderService).memberSynOrder(memberSynOrderQO);
    }

    @Test
    public void testUpdateOrderCondition() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/updateOrderCondition")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockHsaMallBaseOrderService).updateOrderCondition();
    }

    @Test
    public void testFindAfterSaleOrderPage() throws Exception {
        // Setup
        // Configure HsaAfterSaleOrderService.findAfterSaleOrderPage(...).
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setGuid("8d09ade7-bc4d-451c-8974-41b5e6446063");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");
        request.setAfterOrderNum("afterOrderNum");
        request.setRefundType(0);
        when(mockHsaAfterSaleOrderService.findAfterSaleOrderPage(request)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/findAfterSaleOrderPage")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testFindAfterSaleOrderDetails() throws Exception {
        // Setup
        // Configure HsaAfterSaleOrderService.findAfterSaleOrderDetails(...).
        final MallAfterOrderDetailsVO mallAfterOrderDetailsVO = new MallAfterOrderDetailsVO();
        mallAfterOrderDetailsVO.setGuid("8a0d18f6-7cae-4c4d-bf5c-34ed643d9c9c");
        mallAfterOrderDetailsVO.setOrderGuid("orderGuid");
        mallAfterOrderDetailsVO.setAfterOrderNum("afterOrderNum");
        mallAfterOrderDetailsVO.setRefundCondition(0);
        mallAfterOrderDetailsVO.setActualRefundAmount(new BigDecimal("0.00"));
        when(mockHsaAfterSaleOrderService.findAfterSaleOrderDetails("dd86095a-e73e-43ba-add1-5ef501989997"))
                .thenReturn(mallAfterOrderDetailsVO);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/findAfterSaleOrderDetails")
                        .param("guid", "dd86095a-e73e-43ba-add1-5ef501989997")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testMallAfterSaleOrderExport() throws Exception {
        // Setup
        // Configure HsaAfterSaleOrderService.mallAfterSaleOrderExport(...).
        final QueryAfterOrderQO request = new QueryAfterOrderQO();
        request.setGuid("8d09ade7-bc4d-451c-8974-41b5e6446063");
        request.setOperSubjectGuid("operSubjectGuid");
        request.setOrderNumber("orderNumber");
        request.setAfterOrderNum("afterOrderNum");
        request.setRefundType(0);
        when(mockHsaAfterSaleOrderService.mallAfterSaleOrderExport(request)).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/afterSaleOrderExport")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAddOrUpdate() throws Exception {
        // Setup
        // Configure HsaCancelOrderRuleService.addOrUpdate(...).
        final CancelOrderRuleQO cancelOrderRuleQO = new CancelOrderRuleQO();
        cancelOrderRuleQO.setGuid("6dc1cbba-53a7-46c4-b970-b38338867207");
        cancelOrderRuleQO.setCancel("cancel");
        cancelOrderRuleQO.setCancelType("cancelType");
        cancelOrderRuleQO.setCustomInput(0);
        cancelOrderRuleQO.setIsDelete(0);
        when(mockHsaCancelOrderRuleService.addOrUpdate(cancelOrderRuleQO)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/addOrUpdate")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetCancelOrderRule() throws Exception {
        // Setup
        // Configure HsaCancelOrderRuleService.getCancelOrderRule(...).
        final CancelOrderRuleQO cancelOrderRuleQO = new CancelOrderRuleQO();
        cancelOrderRuleQO.setGuid("6dc1cbba-53a7-46c4-b970-b38338867207");
        cancelOrderRuleQO.setCancel("cancel");
        cancelOrderRuleQO.setCancelType("cancelType");
        cancelOrderRuleQO.setCustomInput(0);
        cancelOrderRuleQO.setIsDelete(0);
        final List<CancelOrderRuleQO> cancelOrderRuleQOS = Arrays.asList(cancelOrderRuleQO);
        when(mockHsaCancelOrderRuleService.getCancelOrderRule()).thenReturn(cancelOrderRuleQOS);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/getCancelOrderRule")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetCancelOrderRule_HsaCancelOrderRuleServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaCancelOrderRuleService.getCancelOrderRule()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/getCancelOrderRule")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDeleteCancelOrderRule() throws Exception {
        // Setup
        when(mockHsaCancelOrderRuleService.deleteCancelOrderRule("3df8bb25-bf91-4d74-8626-c37c2822838c"))
                .thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/deleteCancelOrderRule")
                        .param("guid", "3df8bb25-bf91-4d74-8626-c37c2822838c")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testOrderDelivery() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.orderDelivery(...).
        final MallOrderDeliveryQO request = new MallOrderDeliveryQO();
        request.setOrderGuid("orderGuid");
        request.setDeliveryType(0);
        request.setDistributionGuid("distributionGuid");
        request.setLogisticsType(0);
        request.setLogisticsName("logisticsName");
        when(mockHsaMallBaseOrderService.orderDelivery(request)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/orderDelivery")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBusinessOrderRefund() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/business/order_refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockUserValidateSupport).validatePasswordCode("successCode");

        // Confirm HsaMallBaseOrderService.orderBusinessRefund(...).
        final OrderRefundQO request = new OrderRefundQO();
        request.setOrderGuid("orderGuid");
        request.setReason("reason");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setSuccessCode("successCode");
        request.setRefundType(0);
        verify(mockHsaMallBaseOrderService).orderBusinessRefund(request);
    }

    @Test
    public void testUserOrderCancel() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/order_cancel/{cancelType}", 0)
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockHsaMallBaseOrderService).orderCancel("orderGuid", "reason", 0);
    }

    @Test
    public void testListReason() throws Exception {
        // Setup
        // Configure HsaCancelOrderRuleService.listReason(...).
        final OrderReasonVO orderReasonVO = new OrderReasonVO();
        orderReasonVO.setCancel("cancel");
        orderReasonVO.setCustomInput((byte) 0b0);
        final List<OrderReasonVO> orderReasonVOS = Arrays.asList(orderReasonVO);
        when(mockHsaCancelOrderRuleService.listReason("type")).thenReturn(orderReasonVOS);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/list_reason/{type}", "type")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testListReason_HsaCancelOrderRuleServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaCancelOrderRuleService.listReason("type")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/list_reason/{type}", "type")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testValidateUserPassword() throws Exception {
        // Setup
        when(mockUserValidateSupport.validateUserPassword("tel", "password")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/validate_user_password")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetRefundNegotiationHistory() throws Exception {
        // Setup
        // Configure HsaRefundNegotiationHistoryService.getRefundNegotiationHistory(...).
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        refundNegotiationHistoryVO.setAfterSaleOrderGuid("afterSaleOrderGuid");
        final List<RefundNegotiationHistoryVO> refundNegotiationHistoryVOS = Arrays.asList(refundNegotiationHistoryVO);
        when(mockHsaRefundNegotiationHistoryService.getRefundNegotiationHistory("afterSaleOrderGuid"))
                .thenReturn(refundNegotiationHistoryVOS);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/getRefundNegotiationHistory")
                        .param("afterSaleOrderGuid", "afterSaleOrderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetRefundNegotiationHistory_HsaRefundNegotiationHistoryServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaRefundNegotiationHistoryService.getRefundNegotiationHistory("afterSaleOrderGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/getRefundNegotiationHistory")
                        .param("afterSaleOrderGuid", "afterSaleOrderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetAppletNegotiationHistory() throws Exception {
        // Setup
        // Configure HsaRefundNegotiationHistoryService.getAppletNegotiationHistory(...).
        final AppletNegotiationHistoryVO appletNegotiationHistoryVO = new AppletNegotiationHistoryVO();
        final RefundNegotiationHistoryVO refundNegotiationHistoryVO = new RefundNegotiationHistoryVO();
        refundNegotiationHistoryVO.setState(0);
        refundNegotiationHistoryVO.setRoleType(0);
        refundNegotiationHistoryVO.setReason("reason");
        refundNegotiationHistoryVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        appletNegotiationHistoryVO.setLatestHistory(Arrays.asList(refundNegotiationHistoryVO));
        when(mockHsaRefundNegotiationHistoryService.getAppletNegotiationHistory("orderGuid"))
                .thenReturn(appletNegotiationHistoryVO);

        // Run the test and verify the results
        mockMvc.perform(get("/mall_order/getAppletNegotiationHistory")
                        .param("orderGuid", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAppletOrderQO() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.appletOrderQO(...).
        final CrmMallOrderVO crmMallOrderVO = new CrmMallOrderVO();
        crmMallOrderVO.setOrder_guid("order_guid");
        crmMallOrderVO.setOrder_code("order_code");
        crmMallOrderVO.setStore_id("store_id");
        crmMallOrderVO.setStore_name("store_name");
        crmMallOrderVO.setAmount_paid(new BigDecimal("0.00"));
        final List<CrmMallOrderVO> crmMallOrderVOS = Arrays.asList(crmMallOrderVO);
        final AppletOrderQO request = new AppletOrderQO();
        request.setPage(0);
        request.setPage_size(0);
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMini_program_type("mini_program_type");
        when(mockHsaMallBaseOrderService.appletOrderQO(request)).thenReturn(crmMallOrderVOS);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/appletOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testAppletOrderQO_HsaMallBaseOrderServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure HsaMallBaseOrderService.appletOrderQO(...).
        final AppletOrderQO request = new AppletOrderQO();
        request.setPage(0);
        request.setPage_size(0);
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMini_program_type("mini_program_type");
        when(mockHsaMallBaseOrderService.appletOrderQO(request)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/appletOrder")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testConfirmReceived() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/confirm_received/{orderGuid}", "orderGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockHsaMallBaseOrderService).confirmReceived("orderGuid");
    }

    @Test
    public void testUpdateOrderState() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_order/update_order_state")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaMallBaseOrderService.updateOrderState(...).
        final AppletOrderStateQO approveState = new AppletOrderStateQO();
        approveState.setOrder_name("order_name");
        approveState.setState(0);
        approveState.setCourier_company("courier_company");
        approveState.setCourier_number("courier_number");
        verify(mockHsaMallBaseOrderService).updateOrderState(approveState);
    }
}
