package com.holderzone.member.mall.support;

import com.holderzone.member.common.external.ExternalSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class UserValidateSupportTest {

    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private StringRedisTemplate mockRedisTemplate;
    @Mock
    private RedissonClient mockRedissonClient;

    private UserValidateSupport userValidateSupportUnderTest;

    @Before
    public void setUp() throws Exception {
        userValidateSupportUnderTest = new UserValidateSupport(mockExternalSupport, mockRedisTemplate,
                mockRedissonClient);
    }

    @Test
    public void testValidateUserPassword() {
        // Setup
        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = userValidateSupportUnderTest.validateUserPassword("tel", "password");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testValidatePasswordCode() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        userValidateSupportUnderTest.validatePasswordCode("code");

        // Verify the results
        verify(mockRedisTemplate).delete("key");
    }
}
