package com.holderzone.member.mall.adpter;

import com.holderzone.member.mall.service.order.aggregation.AggregationBaseOrderService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AggregationOrderFactoryTest {

    @Mock
    private AggregationBaseOrderService mockStoreOrderServiceImpl;

    private AggregationOrderFactory aggregationOrderFactoryUnderTest;

    @Before
    public void setUp() {
        aggregationOrderFactoryUnderTest = new AggregationOrderFactory(mockStoreOrderServiceImpl);
    }

    @Test
    public void testBuild() {
        // Setup
        // Run the test
        final AggregationBaseOrderService result = aggregationOrderFactoryUnderTest.build("businessType");

        // Verify the results
    }
}
