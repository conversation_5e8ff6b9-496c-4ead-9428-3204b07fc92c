package com.holderzone.member.mall.adpter;

import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.order.AggregationOrderDetailResponseVO;
import com.holderzone.member.common.qo.order.AggregationOrderQueryVO;
import com.holderzone.member.common.qo.order.AggregationOrderResponseVO;
import com.holderzone.member.mall.entity.order.HsaAggregationOrder;
import com.holderzone.member.mall.service.order.HsaAggregationOrderService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AggregationOrderAdapterTest {

    @Mock
    private HsaAggregationOrderService mockAggregationOrderService;
    @Mock
    private AggregationOrderFactory mockAggregationOrderFactory;

    private AggregationOrderAdapter aggregationOrderAdapterUnderTest;

    @Before
    public void setUp() {
        aggregationOrderAdapterUnderTest = new AggregationOrderAdapter(mockAggregationOrderService,
                mockAggregationOrderFactory);
    }

    @Test
    public void testPageInfo() {
        // Setup
        final AggregationOrderQueryVO queryVO = new AggregationOrderQueryVO();
        queryVO.setOrderSource(0);
        queryVO.setMemberInfoGuid("memberInfoGuid");
        queryVO.setOperSubjectGuid("operSubjectGuid");

        final PageResult<AggregationOrderResponseVO> expectedResult = new PageResult<>(0, 0, 0);

        // Configure HsaAggregationOrderService.pageInfo(...).
        final AggregationOrderQueryVO queryVO1 = new AggregationOrderQueryVO();
        queryVO1.setOrderSource(0);
        queryVO1.setMemberInfoGuid("memberInfoGuid");
        queryVO1.setOperSubjectGuid("operSubjectGuid");
        when(mockAggregationOrderService.pageInfo(queryVO1)).thenReturn(new PageResult<>(0, 0, 0));

        when(mockAggregationOrderFactory.build("businessType")).thenReturn(null);

        // Run the test
        final PageResult<AggregationOrderResponseVO> result = aggregationOrderAdapterUnderTest.pageInfo(queryVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetDetail() {
        // Setup
        final AggregationOrderDetailResponseVO expectedResult = new AggregationOrderDetailResponseVO();
        expectedResult.setGuid("c57a955f-09c7-4962-b95f-29e342e90bdd");
        expectedResult.setStoreName("storeName");
        expectedResult.setDiningTableName("diningTableName");
        expectedResult.setGuestCount(0);
        expectedResult.setState(0);

        // Configure HsaAggregationOrderService.getById(...).
        final HsaAggregationOrder hsaAggregationOrder = new HsaAggregationOrder();
        hsaAggregationOrder.setId(0L);
        hsaAggregationOrder.setGuid("842bdfc9-5133-42a4-a440-8e36363bdb2f");
        hsaAggregationOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAggregationOrder.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaAggregationOrder.setBusinessType("businessType");
        when(mockAggregationOrderService.getById("7680d86b-c3c7-4aa5-b81c-cc9c14f5a9bd"))
                .thenReturn(hsaAggregationOrder);

        when(mockAggregationOrderFactory.build("businessType")).thenReturn(null);

        // Run the test
        final AggregationOrderDetailResponseVO result = aggregationOrderAdapterUnderTest.getDetail(
                "7680d86b-c3c7-4aa5-b81c-cc9c14f5a9bd");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
