package com.holderzone.member.mall.controller.set;

import com.holderzone.member.common.dto.mall.set.CommonSetDTO;
import com.holderzone.member.mall.manage.CommonSetManage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(CommonSetController.class)
public class CommonSetControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CommonSetManage mockCommonSetManage;

    @Test
    public void testUpdate() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/common_set/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockCommonSetManage).updateCommonSet(new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0));
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure CommonSetManage.query(...).
        final CommonSetDTO commonSetDTO = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);
        when(mockCommonSetManage.query("memberInfoGuid")).thenReturn(commonSetDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/common_set/query")
                        .param("memberInfoGuid", "memberInfoGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
