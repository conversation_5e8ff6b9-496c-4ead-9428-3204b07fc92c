package com.holderzone.member.mall.manage;

import com.holderzone.member.common.dto.mall.set.CommonSetDTO;
import com.holderzone.member.mall.service.set.HsaCommonSetService;
import com.holderzone.member.mall.service.shoppingcart.IHsaShoppingCartCommodityService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommonSetManageTest {

    @Mock
    private IHsaShoppingCartCommodityService mockShoppingCartCommodityService;
    @Mock
    private HsaCommonSetService mockCommonSetService;

    private CommonSetManage commonSetManageUnderTest;

    @Before
    public void setUp() {
        commonSetManageUnderTest = new CommonSetManage(mockShoppingCartCommodityService, mockCommonSetService);
    }

    @Test
    public void testQuery() {
        // Setup
        final CommonSetDTO expectedResult = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);

        // Configure HsaCommonSetService.queryByOperSubject(...).
        final CommonSetDTO commonSetDTO = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);
        when(mockCommonSetService.queryByOperSubject()).thenReturn(commonSetDTO);

        when(mockShoppingCartCommodityService.queryShoppingCartCommodityNum("memberInfoGuid",
                "operSubjectGuid")).thenReturn(0);

        // Run the test
        final CommonSetDTO result = commonSetManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateCommonSet() {
        // Setup
        final CommonSetDTO commonSetDTO = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);

        // Run the test
        commonSetManageUnderTest.updateCommonSet(commonSetDTO);

        // Verify the results
        verify(mockCommonSetService).updateCommonSet(new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0));
        verify(mockShoppingCartCommodityService).removeAll("operSubjectGuid");
    }
}
