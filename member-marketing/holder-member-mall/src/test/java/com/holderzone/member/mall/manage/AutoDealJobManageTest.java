package com.holderzone.member.mall.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallOrderRefundDTO;
import com.holderzone.member.common.qo.equities.BaseOrderCancelQO;
import com.holderzone.member.common.vo.mall.OrderReceiverAddressVO;
import com.holderzone.member.mall.entity.order.HsaMallBaseOrder;
import com.holderzone.member.mall.entity.order.HsaOrderAutoConfig;
import com.holderzone.member.mall.entity.order.HsaProductOrderDetail;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.PushOrderEvent;
import com.holderzone.member.mall.event.domain.EventEnum;
import com.holderzone.member.mall.service.order.HsaMallBaseOrderService;
import com.holderzone.member.mall.service.order.HsaOrderAutoConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AutoDealJobManageTest {

    @Mock
    private HsaOrderAutoConfigService mockOrderAutoConfigService;
    @Mock
    private HsaMallBaseOrderService mockOrderService;
    @Mock
    private PushOrderEvent mockPushOrderEvent;
    @Mock
    private MemberMallPublisher mockPublisher;

    private AutoDealJobManage autoDealJobManageUnderTest;

    @Before
    public void setUp() {
        autoDealJobManageUnderTest = new AutoDealJobManage(mockOrderAutoConfigService, mockOrderService,
                mockPushOrderEvent, mockPublisher);
    }

    @Test
    public void testOrderCancel() {
        // Setup
        // Configure HsaOrderAutoConfigService.list(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        hsaOrderAutoConfig.setReceiveState(0);
        hsaOrderAutoConfig.setRefundConfirmState(0);
        final List<HsaOrderAutoConfig> hsaOrderAutoConfigs = Arrays.asList(hsaOrderAutoConfig);
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfigs);

        // Configure HsaMallBaseOrderService.getOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("guid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setOrderNumber("orderNum");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        // Run the test
        autoDealJobManageUnderTest.orderCancel(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        // Confirm HsaOrderAutoConfigService.updateById(...).
        final HsaOrderAutoConfig t = new HsaOrderAutoConfig();
        t.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderGuid("orderGuid");
        t.setCancelState(0);
        t.setReceiveState(0);
        t.setRefundConfirmState(0);
        verify(mockOrderAutoConfigService).updateById(t);

        // Confirm HsaMallBaseOrderService.cancel(...).
        final BaseOrderCancelQO baseOrderCancelQO = new BaseOrderCancelQO();
        baseOrderCancelQO.setMemberInfoGuid("memberInfoGuid");
        baseOrderCancelQO.setOrderNum("orderNum");
        baseOrderCancelQO.setOrderGuid("guid");
        baseOrderCancelQO.setReason("超时未支付自动取消");
        baseOrderCancelQO.setCancelType(0);
        verify(mockOrderService).cancel(baseOrderCancelQO);
    }

    @Test
    public void testOrderCancel_HsaOrderAutoConfigServiceListReturnsNoItems() {
        // Setup
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        autoDealJobManageUnderTest.orderCancel(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    public void testOrderReceive() {
        // Setup
        // Configure HsaOrderAutoConfigService.list(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        hsaOrderAutoConfig.setReceiveState(0);
        hsaOrderAutoConfig.setRefundConfirmState(0);
        final List<HsaOrderAutoConfig> hsaOrderAutoConfigs = Arrays.asList(hsaOrderAutoConfig);
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfigs);

        // Run the test
        autoDealJobManageUnderTest.orderReceive(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        // Confirm HsaOrderAutoConfigService.updateById(...).
        final HsaOrderAutoConfig t = new HsaOrderAutoConfig();
        t.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderGuid("orderGuid");
        t.setCancelState(0);
        t.setReceiveState(0);
        t.setRefundConfirmState(0);
        verify(mockOrderAutoConfigService).updateById(t);
        verify(mockOrderService).confirmReceived("orderGuid");

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("guid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setOrderNumber("orderNum");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setId(0L);
        hsaProductOrderDetail.setGuid("4dd70401-9bc7-4d36-80e3-8e157a8b8be4");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("orderGuid");
        hsaProductOrderDetail.setProductId(0L);
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("eb1a0ccf-fb91-44c0-bf4b-a628640d73b1");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("orderGuid", baseNewOrder, details, hsaOrderReceiverAddress);
    }

    @Test
    public void testOrderReceive_HsaOrderAutoConfigServiceListReturnsNoItems() {
        // Setup
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        autoDealJobManageUnderTest.orderReceive(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }

    @Test
    public void testRefundConfirm() {
        // Setup
        // Configure HsaOrderAutoConfigService.list(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        hsaOrderAutoConfig.setReceiveState(0);
        hsaOrderAutoConfig.setRefundConfirmState(0);
        final List<HsaOrderAutoConfig> hsaOrderAutoConfigs = Arrays.asList(hsaOrderAutoConfig);
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfigs);

        // Configure HsaMallBaseOrderService.getOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("guid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setOrderNumber("orderNum");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        when(mockOrderService.getOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        // Configure HsaMallBaseOrderService.refundToAfterOrder(...).
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("b0b40189-7f1a-4f99-bd38-954d13316f45");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setStoreGuid("storeGuid");
        mallBaseOrderDTO.setStoreName("storeName");
        final MallOrderRefundDTO request = new MallOrderRefundDTO();
        request.setOrderGuid("orderGuid");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setReason("商家自动退款");
        request.setRefundType(0);
        request.setUpdateAfterOrder(false);
        when(mockOrderService.refundToAfterOrder(mallBaseOrderDTO, request)).thenReturn("result");

        // Run the test
        autoDealJobManageUnderTest.refundConfirm(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
        // Confirm HsaOrderAutoConfigService.updateById(...).
        final HsaOrderAutoConfig t = new HsaOrderAutoConfig();
        t.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOrderGuid("orderGuid");
        t.setCancelState(0);
        t.setReceiveState(0);
        t.setRefundConfirmState(0);
        verify(mockOrderAutoConfigService).updateById(t);
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testRefundConfirm_HsaOrderAutoConfigServiceListReturnsNoItems() {
        // Setup
        when(mockOrderAutoConfigService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        autoDealJobManageUnderTest.refundConfirm(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Verify the results
    }
}
