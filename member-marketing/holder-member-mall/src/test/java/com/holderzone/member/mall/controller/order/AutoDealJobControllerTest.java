package com.holderzone.member.mall.controller.order;

import com.holderzone.member.mall.manage.AutoDealJobManage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AutoDealJobController.class)
public class AutoDealJobControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AutoDealJobManage mockAutoDealJobManage;

    @Test
    public void testOrderCancel() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/auto/deal/order/cancel")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAutoDealJobManage).orderCancel(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testOrderReceive() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/auto/deal/order/receive")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAutoDealJobManage).orderReceive(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }

    @Test
    public void testOrderRefundConfirm() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/auto/deal/order/refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAutoDealJobManage).refundConfirm(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
    }
}
