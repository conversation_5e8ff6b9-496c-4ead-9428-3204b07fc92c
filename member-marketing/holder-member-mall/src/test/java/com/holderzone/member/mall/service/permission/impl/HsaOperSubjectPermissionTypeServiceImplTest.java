package com.holderzone.member.mall.service.permission.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.permission.HsaOperSubjectPermissionType;
import com.holderzone.member.mall.mapper.permission.HsaMallOperSubjectPermissionTypeMapper;
import com.holderzone.member.mall.mapper.permission.HsaSubjectPermissionMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaOperSubjectPermissionTypeServiceImplTest {

    @Mock
    private HsaMallOperSubjectPermissionTypeMapper mockHsaMallOperSubjectPermissionTypeMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaSubjectPermissionMapper mockHsaOperSubjectPermissionMapper;

    private HsaOperSubjectPermissionTypeServiceImpl hsaOperSubjectPermissionTypeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaOperSubjectPermissionTypeServiceImplUnderTest = new HsaOperSubjectPermissionTypeServiceImpl(
                mockHsaMallOperSubjectPermissionTypeMapper, mockGuidGeneratorUtil, mockHsaOperSubjectPermissionMapper);
    }

    @Test
    public void testSavaOrUpdate() {
        // Setup
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        // Configure HsaMallOperSubjectPermissionTypeMapper.selectOne(...).
        final HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        hsaOperSubjectPermissionType.setGuid("3387dcbd-c6c6-4499-8ec9-22a8e15008a1");
        hsaOperSubjectPermissionType.setEnterpriseGuid("enterpriseGuid");
        hsaOperSubjectPermissionType.setPositionGuid("positionGuid");
        hsaOperSubjectPermissionType.setSourceType(0);
        hsaOperSubjectPermissionType.setIsRole(0);
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperSubjectPermissionType);

        when(mockGuidGeneratorUtil.getStringGuid("name")).thenReturn("3387dcbd-c6c6-4499-8ec9-22a8e15008a1");

        // Run the test
        final boolean result = hsaOperSubjectPermissionTypeServiceImplUnderTest.savaOrUpdate(request);

        // Verify the results
        assertThat(result).isFalse();
    }
}
