package com.holderzone.member.mall.manage;

import com.holderzone.member.common.dto.crm.CrmCommodityDTO;
import com.holderzone.member.common.external.ExternalSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CommonManageTest {

    @Mock
    private ExternalSupport mockExternalSupport;

    private CommonManage commonManageUnderTest;

    @Before
    public void setUp() {
        commonManageUnderTest = new CommonManage(mockExternalSupport);
    }

    @Test
    public void testGetCommodityUrl() {
        // Setup
        final CrmCommodityDTO commodityDTO = new CrmCommodityDTO();
        commodityDTO.setUserAccount("user_account");
        commodityDTO.setCompanyId("company_id");

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final String result = commonManageUnderTest.getCommodityUrl(commodityDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }
}
