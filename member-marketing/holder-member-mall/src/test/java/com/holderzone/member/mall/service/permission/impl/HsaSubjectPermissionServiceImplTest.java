package com.holderzone.member.mall.service.permission.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.member.FunctionModelDTO;
import com.holderzone.member.common.dto.member.PermissionModelDTO;
import com.holderzone.member.common.dto.permission.MemberSystemPermissionDTO;
import com.holderzone.member.common.dto.permission.RolePermissionMapModel;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.OperSubjectInfo;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.qo.permission.HsaOperSubjectPermissionQO;
import com.holderzone.member.common.qo.permission.OperSubjectPermissionQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.base.OperSubjectInfoVO;
import com.holderzone.member.common.vo.member.MemberSystemPermissionVO;
import com.holderzone.member.common.vo.permission.HsaOperSubjectPermissionVO;
import com.holderzone.member.mall.entity.permission.HsaOperSubjectPermissionType;
import com.holderzone.member.mall.entity.permission.HsaSubjectPermission;
import com.holderzone.member.mall.mapper.permission.HsaMallOperSubjectPermissionTypeMapper;
import com.holderzone.member.mall.mapper.permission.HsaSubjectPermissionMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaSubjectPermissionServiceImplTest {

    @Mock
    private HsaSubjectPermissionMapper mockHsaSubjectPermissionMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private HsaMallOperSubjectPermissionTypeMapper mockHsaMallOperSubjectPermissionTypeMapper;
    @Mock
    private ExternalSupport mockExternalSupport;

    @InjectMocks
    private HsaSubjectPermissionServiceImpl hsaSubjectPermissionServiceImplUnderTest;

    @Test
    public void testGetSubjectPermissionList() {
        // Setup
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("id");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        hsaOperSubjectPermissionVO.setLogo("logo");
        final List<HsaOperSubjectPermissionVO> expectedResult = Arrays.asList(hsaOperSubjectPermissionVO);

        // Configure HsaSubjectPermissionMapper.selectList(...).
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubjectPermissions);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");

        // Run the test
        final List<HsaOperSubjectPermissionVO> result = hsaSubjectPermissionServiceImplUnderTest.getSubjectPermissionList(
                request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockTransactionTemplate).execute(any(TransactionCallback.class));
    }

    @Test
    public void testGetSubjectPermissionList_HsaSubjectPermissionMapperReturnsNoItems() {
        // Setup
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");

        // Run the test
        final List<HsaOperSubjectPermissionVO> result = hsaSubjectPermissionServiceImplUnderTest.getSubjectPermissionList(
                request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockTransactionTemplate).execute(any(TransactionCallback.class));
    }

    @Test
    public void testGetSubjectPermissionList_TransactionTemplateThrowsTransactionException() {
        // Setup
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        // Configure HsaSubjectPermissionMapper.selectList(...).
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubjectPermissions);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(
                () -> hsaSubjectPermissionServiceImplUnderTest.getSubjectPermissionList(request))
                .isInstanceOf(TransactionException.class);
    }

    @Test
    public void testUpdateSubjectHandler() {
        // Setup
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        final List<OperSubjectInfo> operSubjectInfos = Arrays.asList(operSubjectInfo);
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("id");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        hsaOperSubjectPermissionVO.setLogo("logo");
        final List<HsaOperSubjectPermissionVO> expectedResult = Arrays.asList(hsaOperSubjectPermissionVO);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");

        // Run the test
        final List<HsaOperSubjectPermissionVO> result = hsaSubjectPermissionServiceImplUnderTest.updateSubjectHandler(
                hsaSubjectPermissions, request, operSubjectInfos);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockTransactionTemplate).execute(any(TransactionCallback.class));
    }

    @Test
    public void testUpdateSubjectHandler_TransactionTemplateThrowsTransactionException() {
        // Setup
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        final List<OperSubjectInfo> operSubjectInfos = Arrays.asList(operSubjectInfo);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Run the test
        assertThatThrownBy(
                () -> hsaSubjectPermissionServiceImplUnderTest.updateSubjectHandler(hsaSubjectPermissions, request,
                        operSubjectInfos)).isInstanceOf(TransactionException.class);
    }

    @Test
    public void testIsUpdateSubject() {
        // Setup
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaOperSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        final List<OperSubjectInfo> operSubjectInfos = Arrays.asList(operSubjectInfo);

        // Run the test
        final boolean result = hsaSubjectPermissionServiceImplUnderTest.isUpdateSubject(hsaOperSubjectPermissions,
                operSubjectInfos);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSetDataHandler() {
        // Setup
        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        final List<OperSubjectInfo> operSubjectInfos = Arrays.asList(operSubjectInfo);
        final OperSubjectPermissionQO request = new OperSubjectPermissionQO();
        request.setRoleId("positionGuid");
        request.setIsRole(0);
        request.setTeamId("enterpriseGuid");
        request.setSourceType(0);
        request.setIsAll(0);

        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> expectedResult = Arrays.asList(hsaSubjectPermission);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("17fa4f10-444f-4555-8817-c836467ff64d");

        // Run the test
        final List<HsaSubjectPermission> result = hsaSubjectPermissionServiceImplUnderTest.setDataHandler(
                operSubjectInfos, request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateSubjectPermission() {
        // Setup
        final HsaOperSubjectPermissionQO request = new HsaOperSubjectPermissionQO();
        request.setSourceType(0);
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("id");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        request.setHolderPermission(Arrays.asList(hsaOperSubjectPermissionVO));
        request.setTeamId(0);
        request.setRoleId(0);
        request.setIsRole(0);
        request.setIsAll(0);

        // Configure HsaMallOperSubjectPermissionTypeMapper.selectOne(...).
        final HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        hsaOperSubjectPermissionType.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        hsaOperSubjectPermissionType.setEnterpriseGuid("enterpriseGuid");
        hsaOperSubjectPermissionType.setPositionGuid("positionGuid");
        hsaOperSubjectPermissionType.setSourceType(0);
        hsaOperSubjectPermissionType.setIsRole(0);
        hsaOperSubjectPermissionType.setIsAll(0);
        hsaOperSubjectPermissionType.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperSubjectPermissionType);

        when(mockGuidGeneratorUtil.getStringGuid("name")).thenReturn("897b8ec1-d2f3-403a-a04e-a85828fe079f");

        // Configure HsaMallOperSubjectPermissionTypeMapper.insert(...).
        final HsaOperSubjectPermissionType t = new HsaOperSubjectPermissionType();
        t.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setPositionGuid("positionGuid");
        t.setSourceType(0);
        t.setIsRole(0);
        t.setIsAll(0);
        t.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallOperSubjectPermissionTypeMapper.insert(t)).thenReturn(0);

        // Configure HsaMallOperSubjectPermissionTypeMapper.updateByGuid(...).
        final HsaOperSubjectPermissionType t1 = new HsaOperSubjectPermissionType();
        t1.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        t1.setEnterpriseGuid("enterpriseGuid");
        t1.setPositionGuid("positionGuid");
        t1.setSourceType(0);
        t1.setIsRole(0);
        t1.setIsAll(0);
        t1.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallOperSubjectPermissionTypeMapper.updateByGuid(t1)).thenReturn(false);

        // Configure HsaSubjectPermissionMapper.selectList(...).
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubjectPermissions);

        // Run the test
        final boolean result = hsaSubjectPermissionServiceImplUnderTest.updateSubjectPermission(request);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateSubjectPermission_HsaSubjectPermissionMapperReturnsNoItems() {
        // Setup
        final HsaOperSubjectPermissionQO request = new HsaOperSubjectPermissionQO();
        request.setSourceType(0);
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("id");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        request.setHolderPermission(Arrays.asList(hsaOperSubjectPermissionVO));
        request.setTeamId(0);
        request.setRoleId(0);
        request.setIsRole(0);
        request.setIsAll(0);

        // Configure HsaMallOperSubjectPermissionTypeMapper.selectOne(...).
        final HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        hsaOperSubjectPermissionType.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        hsaOperSubjectPermissionType.setEnterpriseGuid("enterpriseGuid");
        hsaOperSubjectPermissionType.setPositionGuid("positionGuid");
        hsaOperSubjectPermissionType.setSourceType(0);
        hsaOperSubjectPermissionType.setIsRole(0);
        hsaOperSubjectPermissionType.setIsAll(0);
        hsaOperSubjectPermissionType.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperSubjectPermissionType);

        // Configure HsaMallOperSubjectPermissionTypeMapper.updateByGuid(...).
        final HsaOperSubjectPermissionType t = new HsaOperSubjectPermissionType();
        t.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setPositionGuid("positionGuid");
        t.setSourceType(0);
        t.setIsRole(0);
        t.setIsAll(0);
        t.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallOperSubjectPermissionTypeMapper.updateByGuid(t)).thenReturn(false);

        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = hsaSubjectPermissionServiceImplUnderTest.updateSubjectPermission(request);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetAccountPermission() {
        // Setup
        final MemberSystemPermissionVO expectedResult = new MemberSystemPermissionVO();
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        expectedResult.setMemberSystemPermissionDTOs(Arrays.asList(memberSystemPermissionDTO));
        final PermissionModelDTO permissionModelDTO1 = new PermissionModelDTO();
        permissionModelDTO1.setId("id");
        permissionModelDTO1.setPermissionName("name");
        permissionModelDTO1.setIsChecked(0);
        expectedResult.setPermissionModelDTOS(Arrays.asList(permissionModelDTO1));
        expectedResult.setIsAll(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Configure HsaMallOperSubjectPermissionTypeMapper.selectList(...).
        final HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        hsaOperSubjectPermissionType.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        hsaOperSubjectPermissionType.setEnterpriseGuid("enterpriseGuid");
        hsaOperSubjectPermissionType.setPositionGuid("positionGuid");
        hsaOperSubjectPermissionType.setSourceType(0);
        hsaOperSubjectPermissionType.setIsRole(0);
        hsaOperSubjectPermissionType.setIsAll(0);
        hsaOperSubjectPermissionType.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperSubjectPermissionType> hsaOperSubjectPermissionTypes = Arrays.asList(
                hsaOperSubjectPermissionType);
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperSubjectPermissionTypes);

        // Run the test
        final MemberSystemPermissionVO result = hsaSubjectPermissionServiceImplUnderTest.getAccountPermission(
                "identification");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAccountPermission_HsaMallOperSubjectPermissionTypeMapperReturnsNoItems() {
        // Setup
        final MemberSystemPermissionVO expectedResult = new MemberSystemPermissionVO();
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        expectedResult.setMemberSystemPermissionDTOs(Arrays.asList(memberSystemPermissionDTO));
        final PermissionModelDTO permissionModelDTO1 = new PermissionModelDTO();
        permissionModelDTO1.setId("id");
        permissionModelDTO1.setPermissionName("name");
        permissionModelDTO1.setIsChecked(0);
        expectedResult.setPermissionModelDTOS(Arrays.asList(permissionModelDTO1));
        expectedResult.setIsAll(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaSubjectPermissionMapper.selectList(...).
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubjectPermissions);

        // Run the test
        final MemberSystemPermissionVO result = hsaSubjectPermissionServiceImplUnderTest.getAccountPermission(
                "identification");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAccountPermission_HsaSubjectPermissionMapperReturnsNoItems() {
        // Setup
        final MemberSystemPermissionVO expectedResult = new MemberSystemPermissionVO();
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        expectedResult.setMemberSystemPermissionDTOs(Arrays.asList(memberSystemPermissionDTO));
        final PermissionModelDTO permissionModelDTO1 = new PermissionModelDTO();
        permissionModelDTO1.setId("id");
        permissionModelDTO1.setPermissionName("name");
        permissionModelDTO1.setIsChecked(0);
        expectedResult.setPermissionModelDTOS(Arrays.asList(permissionModelDTO1));
        expectedResult.setIsAll(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final MemberSystemPermissionVO result = hsaSubjectPermissionServiceImplUnderTest.getAccountPermission(
                "identification");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOperatingSubject() {
        // Setup
        final OperSubjectInfoVO expectedResult = new OperSubjectInfoVO();
        final OperSubjectInfo operSubjectInfo = new OperSubjectInfo();
        operSubjectInfo.setMultiMemberStatus(false);
        operSubjectInfo.setOperSubjectGuid("operSubjectGuid");
        operSubjectInfo.setMultiMemberName("multiMemberName");
        expectedResult.setOperSubjectInfos(Arrays.asList(operSubjectInfo));
        expectedResult.setEnterpriseGuid("enterpriseGuid");

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final OperSubjectInfoVO result = hsaSubjectPermissionServiceImplUnderTest.getOperatingSubject();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryUserInformation() {
        // Setup
        final HeaderUserInfo expectedResult = new HeaderUserInfo();
        expectedResult.setIsAlliance(false);
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setIsCheckToken("isCheckToken");
        expectedResult.setUserGuid("userGuid");

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final HeaderUserInfo result = hsaSubjectPermissionServiceImplUnderTest.queryUserInformation();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetJobAndRoleInfo() {
        // Setup
        final RolePermissionMapModel expectedResult = new RolePermissionMapModel();
        expectedResult.setRoleMap(new HashMap<>());
        expectedResult.setEntRoleMap(new HashMap<>());
        expectedResult.setIsAll(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final RolePermissionMapModel result = hsaSubjectPermissionServiceImplUnderTest.getJobAndRoleInfo(
                "identification", "userGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testExecuteRequest() {
        // Setup
        final RolePermissionMapModel expectedResult = new RolePermissionMapModel();
        expectedResult.setRoleMap(new HashMap<>());
        expectedResult.setEntRoleMap(new HashMap<>());
        expectedResult.setIsAll(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final RolePermissionMapModel result = hsaSubjectPermissionServiceImplUnderTest.executeRequest("identification",
                "userGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOperSubjectPermission() {
        // Setup
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        final List<MemberSystemPermissionDTO> memberSystemPermissions = Arrays.asList(memberSystemPermissionDTO);
        final RolePermissionMapModel rolePermissionMapModel = new RolePermissionMapModel();
        rolePermissionMapModel.setRoleMap(new HashMap<>());
        rolePermissionMapModel.setEntRoleMap(new HashMap<>());
        rolePermissionMapModel.setIsAll(0);

        final PermissionModelDTO permissionModelDTO1 = new PermissionModelDTO();
        permissionModelDTO1.setId("id");
        permissionModelDTO1.setPermissionName("name");
        permissionModelDTO1.setIsFormPermissions(0);
        permissionModelDTO1.setIsChecked(0);
        final List<PermissionModelDTO> expectedResult = Arrays.asList(permissionModelDTO1);

        // Configure HsaMallOperSubjectPermissionTypeMapper.selectList(...).
        final HsaOperSubjectPermissionType hsaOperSubjectPermissionType = new HsaOperSubjectPermissionType();
        hsaOperSubjectPermissionType.setGuid("897b8ec1-d2f3-403a-a04e-a85828fe079f");
        hsaOperSubjectPermissionType.setEnterpriseGuid("enterpriseGuid");
        hsaOperSubjectPermissionType.setPositionGuid("positionGuid");
        hsaOperSubjectPermissionType.setSourceType(0);
        hsaOperSubjectPermissionType.setIsRole(0);
        hsaOperSubjectPermissionType.setIsAll(0);
        hsaOperSubjectPermissionType.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaOperSubjectPermissionType> hsaOperSubjectPermissionTypes = Arrays.asList(
                hsaOperSubjectPermissionType);
        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOperSubjectPermissionTypes);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final List<PermissionModelDTO> result = hsaSubjectPermissionServiceImplUnderTest.getOperSubjectPermission(
                memberSystemPermissions, 0, rolePermissionMapModel);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOperSubjectPermission_HsaMallOperSubjectPermissionTypeMapperReturnsNoItems() {
        // Setup
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        final List<MemberSystemPermissionDTO> memberSystemPermissions = Arrays.asList(memberSystemPermissionDTO);
        final RolePermissionMapModel rolePermissionMapModel = new RolePermissionMapModel();
        rolePermissionMapModel.setRoleMap(new HashMap<>());
        rolePermissionMapModel.setEntRoleMap(new HashMap<>());
        rolePermissionMapModel.setIsAll(0);

        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaSubjectPermissionMapper.selectList(...).
        final HsaSubjectPermission hsaSubjectPermission = new HsaSubjectPermission();
        hsaSubjectPermission.setGuid("17fa4f10-444f-4555-8817-c836467ff64d");
        hsaSubjectPermission.setEnterpriseGuid("enterpriseGuid");
        hsaSubjectPermission.setPositionGuid("positionGuid");
        hsaSubjectPermission.setOperSubjectGuid("operSubjectGuid");
        hsaSubjectPermission.setMultiMemberName("multiMemberName");
        hsaSubjectPermission.setMultiMemberStatus(0);
        hsaSubjectPermission.setIsChecked(0);
        hsaSubjectPermission.setSourceType(0);
        hsaSubjectPermission.setIsRole(0);
        hsaSubjectPermission.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaSubjectPermission> hsaSubjectPermissions = Arrays.asList(hsaSubjectPermission);
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaSubjectPermissions);

        // Run the test
        final List<PermissionModelDTO> result = hsaSubjectPermissionServiceImplUnderTest.getOperSubjectPermission(
                memberSystemPermissions, 0, rolePermissionMapModel);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetOperSubjectPermission_HsaSubjectPermissionMapperReturnsNoItems() {
        // Setup
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        final List<MemberSystemPermissionDTO> memberSystemPermissions = Arrays.asList(memberSystemPermissionDTO);
        final RolePermissionMapModel rolePermissionMapModel = new RolePermissionMapModel();
        rolePermissionMapModel.setRoleMap(new HashMap<>());
        rolePermissionMapModel.setEntRoleMap(new HashMap<>());
        rolePermissionMapModel.setIsAll(0);

        when(mockHsaMallOperSubjectPermissionTypeMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockHsaSubjectPermissionMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<PermissionModelDTO> result = hsaSubjectPermissionServiceImplUnderTest.getOperSubjectPermission(
                memberSystemPermissions, 0, rolePermissionMapModel);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSetPermissionModelDTO() {
        // Setup
        final HsaOperSubjectPermissionVO hsaOperSubjectPermissionVO = new HsaOperSubjectPermissionVO();
        hsaOperSubjectPermissionVO.setId("id");
        hsaOperSubjectPermissionVO.setOperSubjectGuid("id");
        hsaOperSubjectPermissionVO.setName("name");
        hsaOperSubjectPermissionVO.setIs_checked(0);
        hsaOperSubjectPermissionVO.setLogo("logo");
        final List<HsaOperSubjectPermissionVO> hsaSubjectPermissionVOS = Arrays.asList(hsaOperSubjectPermissionVO);
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsFormPermissions(0);
        permissionModelDTO.setIsChecked(0);
        final List<PermissionModelDTO> expectedResult = Arrays.asList(permissionModelDTO);

        // Run the test
        final List<PermissionModelDTO> result = hsaSubjectPermissionServiceImplUnderTest.setPermissionModelDTO(
                hsaSubjectPermissionVOS);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testJobAndRoleList() {
        // Setup
        final Map<String, List<MemberSystemPermissionDTO>> permissionMap = new HashMap<>();

        // Run the test
        final List<String> result = hsaSubjectPermissionServiceImplUnderTest.jobAndRoleList(permissionMap,
                "functionGroup", "function");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList("value"));
    }

    @Test
    public void testIsPermission() {
        // Setup
        final MemberSystemPermissionDTO memberSystemPermissionDTO = new MemberSystemPermissionDTO();
        memberSystemPermissionDTO.setName("name");
        final FunctionModelDTO functionModelDTO = new FunctionModelDTO();
        final PermissionModelDTO permissionModelDTO = new PermissionModelDTO();
        permissionModelDTO.setId("id");
        permissionModelDTO.setPermissionName("name");
        permissionModelDTO.setIsChecked(0);
        functionModelDTO.setPermissionModels(Arrays.asList(permissionModelDTO));
        memberSystemPermissionDTO.setFunctionModels(Arrays.asList(functionModelDTO));
        final List<MemberSystemPermissionDTO> systemPermissionDTOList = Arrays.asList(memberSystemPermissionDTO);

        // Run the test
        final Boolean result = hsaSubjectPermissionServiceImplUnderTest.isPermission(systemPermissionDTOList,
                "functionGroup", "function");

        // Verify the results
        assertThat(result).isFalse();
    }
}
