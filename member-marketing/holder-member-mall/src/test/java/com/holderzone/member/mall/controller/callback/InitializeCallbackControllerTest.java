package com.holderzone.member.mall.controller.callback;

import com.holderzone.member.mall.service.callback.InitializeCallbackService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(InitializeCallbackController.class)
public class InitializeCallbackControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private InitializeCallbackService mockInitializeCallbackService;

    @Test
    public void testInitializeSubjectData() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/initialize/init_subject_data")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockInitializeCallbackService).initializeSubjectData(Arrays.asList("value"));
    }
}
