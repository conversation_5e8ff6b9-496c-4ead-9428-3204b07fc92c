package com.holderzone.member.mall.manage;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.mall.shoppingcart.*;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.exception.MallBaseException;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.commodity.CommodityDetailConditionQO;
import com.holderzone.member.common.qo.equities.MemberPriceApplyCommodityQO;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.equities.MemberPriceApplyCommodityVO;
import com.holderzone.member.common.vo.grade.ComDataVO;
import com.holderzone.member.common.vo.grade.SelectDataVO;
import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartCommodityVO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartStoreVO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartVO;
import com.holderzone.member.common.vo.tool.CommodityAttrVO;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.common.vo.tool.MustSelectDataVO;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;
import com.holderzone.member.mall.service.shoppingcart.IHsaShoppingCartCommodityService;
import com.holderzone.member.mall.support.StoreSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingCartManageTest {

    @Mock
    private IHsaShoppingCartCommodityService mockShoppingCartCommodityService;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private StoreSupport mockStoreSupport;
    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private MemberBaseFeign mockBaseFeign;
    @Mock
    private MemberCommodityFeign mockCommodityFeign;

    private ShoppingCartManage shoppingCartManageUnderTest;

    @Before
    public void setUp() {
        shoppingCartManageUnderTest = new ShoppingCartManage(mockShoppingCartCommodityService, mockGuidGeneratorUtil,
                mockStoreSupport, mockExternalSupport, mockBaseFeign, mockCommodityFeign);
    }

    @Test
    public void testAdd() {
        // Setup
        final ShoppingCartAddDTO addDTO = new ShoppingCartAddDTO();
        addDTO.setMemberInfoGuid("memberInfoGuid");
        addDTO.setCommodityId(0L);
        addDTO.setNum(0);
        addDTO.setChangeType(0);
        addDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        addDTO.setSkuDTOList(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        addDTO.setAttrDTOList(Arrays.asList(shoppingCartAttrDTO));

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityVO commodityVO = new CommodityVO();
        commodityVO.setCommodityCode("commodityCode");
        commodityVO.setCommodityName("commodityName");
        commodityVO.setCommodityImg("commodityImg");
        commodityVO.setBasePrice("0.01");
        commodityVO.setStoreState(0);
        commodityVO.setGroupType(0);
        commodityVO.setIsDelete(0);
        final List<CommodityVO> commodityVOS = Arrays.asList(commodityVO);
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setSortType(0);
        request.setType(0);
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockCommodityFeign.listCommodityByIds(request)).thenReturn(commodityVOS);

        // Configure IHsaShoppingCartCommodityService.queryRepeatCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryRepeatCommodity("operSubjectGuid", "skuJson", "attrJson",
                "memberInfoGuid", 0L)).thenReturn(hsaShoppingCartCommodity);

        when(mockShoppingCartCommodityService.countLimit("memberInfoGuid")).thenReturn(0);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("43f2fe8e-6576-41ff-b049-cbaf3314b063");

        // Run the test
        final Integer result = shoppingCartManageUnderTest.add(addDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockShoppingCartCommodityService).changeNum(
                new CartAddNumDTO("43f2fe8e-6576-41ff-b049-cbaf3314b063", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));

        // Confirm IHsaShoppingCartCommodityService.save(...).
        final HsaShoppingCartCommodity t = new HsaShoppingCartCommodity();
        t.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setCommodityId(0L);
        t.setCommodityCode("commodityCode");
        t.setStoreId(0);
        t.setNum(0);
        t.setCommodityName("commodityName");
        t.setCommodityImg("commodityImg");
        t.setBasePrice("0.01");
        t.setGroupType(0);
        t.setSku("sku");
        t.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setAttr("attr");
        verify(mockShoppingCartCommodityService).save(t);
    }

    @Test
    public void testAdd_MemberCommodityFeignReturnsNoItems() {
        // Setup
        final ShoppingCartAddDTO addDTO = new ShoppingCartAddDTO();
        addDTO.setMemberInfoGuid("memberInfoGuid");
        addDTO.setCommodityId(0L);
        addDTO.setNum(0);
        addDTO.setChangeType(0);
        addDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        addDTO.setSkuDTOList(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        addDTO.setAttrDTOList(Arrays.asList(shoppingCartAttrDTO));

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setSortType(0);
        request.setType(0);
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockCommodityFeign.listCommodityByIds(request)).thenReturn(Collections.emptyList());

        // Configure IHsaShoppingCartCommodityService.queryRepeatCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryRepeatCommodity("operSubjectGuid", "skuJson", "attrJson",
                "memberInfoGuid", 0L)).thenReturn(hsaShoppingCartCommodity);

        when(mockShoppingCartCommodityService.countLimit("memberInfoGuid")).thenReturn(0);
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("43f2fe8e-6576-41ff-b049-cbaf3314b063");

        // Run the test
        final Integer result = shoppingCartManageUnderTest.add(addDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockShoppingCartCommodityService).changeNum(
                new CartAddNumDTO("43f2fe8e-6576-41ff-b049-cbaf3314b063", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));

        // Confirm IHsaShoppingCartCommodityService.save(...).
        final HsaShoppingCartCommodity t = new HsaShoppingCartCommodity();
        t.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setCommodityId(0L);
        t.setCommodityCode("commodityCode");
        t.setStoreId(0);
        t.setNum(0);
        t.setCommodityName("commodityName");
        t.setCommodityImg("commodityImg");
        t.setBasePrice("0.01");
        t.setGroupType(0);
        t.setSku("sku");
        t.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setAttr("attr");
        verify(mockShoppingCartCommodityService).save(t);
    }

    @Test
    public void testEdit() {
        // Setup
        final ShoppingCartEditDTO editDTO = new ShoppingCartEditDTO();
        editDTO.setMemberInfoGuid("memberInfoGuid");
        editDTO.setCommodityId(0L);
        editDTO.setNum(0);
        editDTO.setChangeType(0);
        editDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        editDTO.setSkuDTOList(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        editDTO.setAttrDTOList(Arrays.asList(shoppingCartAttrDTO));
        editDTO.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityVO commodityVO = new CommodityVO();
        commodityVO.setCommodityCode("commodityCode");
        commodityVO.setCommodityName("commodityName");
        commodityVO.setCommodityImg("commodityImg");
        commodityVO.setBasePrice("0.01");
        commodityVO.setStoreState(0);
        commodityVO.setGroupType(0);
        commodityVO.setIsDelete(0);
        final List<CommodityVO> commodityVOS = Arrays.asList(commodityVO);
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setSortType(0);
        request.setType(0);
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockCommodityFeign.listCommodityByIds(request)).thenReturn(commodityVOS);

        // Configure IHsaShoppingCartCommodityService.queryRepeatCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryRepeatCommodity("operSubjectGuid", "skuJson", "attrJson",
                "memberInfoGuid", 0L)).thenReturn(hsaShoppingCartCommodity);

        // Run the test
        shoppingCartManageUnderTest.edit(editDTO);

        // Verify the results
        verify(mockShoppingCartCommodityService).changeNum(
                new CartAddNumDTO("43f2fe8e-6576-41ff-b049-cbaf3314b063", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));

        // Confirm IHsaShoppingCartCommodityService.updateById(...).
        final HsaShoppingCartCommodity t = new HsaShoppingCartCommodity();
        t.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setCommodityId(0L);
        t.setCommodityCode("commodityCode");
        t.setStoreId(0);
        t.setNum(0);
        t.setCommodityName("commodityName");
        t.setCommodityImg("commodityImg");
        t.setBasePrice("0.01");
        t.setGroupType(0);
        t.setSku("sku");
        t.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setAttr("attr");
        verify(mockShoppingCartCommodityService).updateById(t);
    }

    @Test
    public void testEdit_MemberCommodityFeignReturnsNoItems() {
        // Setup
        final ShoppingCartEditDTO editDTO = new ShoppingCartEditDTO();
        editDTO.setMemberInfoGuid("memberInfoGuid");
        editDTO.setCommodityId(0L);
        editDTO.setNum(0);
        editDTO.setChangeType(0);
        editDTO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        editDTO.setSkuDTOList(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        editDTO.setAttrDTOList(Arrays.asList(shoppingCartAttrDTO));
        editDTO.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setSortType(0);
        request.setType(0);
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockCommodityFeign.listCommodityByIds(request)).thenReturn(Collections.emptyList());

        // Configure IHsaShoppingCartCommodityService.queryRepeatCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryRepeatCommodity("operSubjectGuid", "skuJson", "attrJson",
                "memberInfoGuid", 0L)).thenReturn(hsaShoppingCartCommodity);

        // Run the test
        shoppingCartManageUnderTest.edit(editDTO);

        // Verify the results
        verify(mockShoppingCartCommodityService).changeNum(
                new CartAddNumDTO("43f2fe8e-6576-41ff-b049-cbaf3314b063", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));

        // Confirm IHsaShoppingCartCommodityService.updateById(...).
        final HsaShoppingCartCommodity t = new HsaShoppingCartCommodity();
        t.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setCommodityId(0L);
        t.setCommodityCode("commodityCode");
        t.setStoreId(0);
        t.setNum(0);
        t.setCommodityName("commodityName");
        t.setCommodityImg("commodityImg");
        t.setBasePrice("0.01");
        t.setGroupType(0);
        t.setSku("sku");
        t.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setAttr("attr");
        verify(mockShoppingCartCommodityService).updateById(t);
    }

    @Test
    public void testQuery() {
        // Setup
        final ShoppingCartVO expectedResult = new ShoppingCartVO();
        final ShoppingCartStoreVO shoppingCartStoreVO = new ShoppingCartStoreVO();
        shoppingCartStoreVO.setStoreId(0);
        shoppingCartStoreVO.setStoreName("name");
        shoppingCartStoreVO.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO.setCommodityName("commodityName");
        shoppingCartCommodityVO.setCommodityPrice("0.01");
        shoppingCartCommodityVO.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO.setSkuName("skuName");
        shoppingCartCommodityVO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO.setCommodityCode("commodity_code");
        shoppingCartCommodityVO.setCommodityImg("commodityImg");
        shoppingCartCommodityVO.setNum(0);
        shoppingCartCommodityVO.setStartingNumber(0);
        shoppingCartStoreVO.setCommodityList(Arrays.asList(shoppingCartCommodityVO));
        expectedResult.setStoreList(Arrays.asList(shoppingCartStoreVO));
        final ShoppingCartStoreVO shoppingCartStoreVO1 = new ShoppingCartStoreVO();
        shoppingCartStoreVO1.setStoreId(0);
        shoppingCartStoreVO1.setStoreName("name");
        shoppingCartStoreVO1.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO1 = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO1.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO1.setCommodityName("commodityName");
        shoppingCartCommodityVO1.setCommodityPrice("0.01");
        shoppingCartCommodityVO1.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO1.setSkuName("skuName");
        shoppingCartCommodityVO1.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO1.setCommodityCode("commodity_code");
        shoppingCartCommodityVO1.setCommodityImg("commodityImg");
        shoppingCartCommodityVO1.setNum(0);
        shoppingCartCommodityVO1.setStartingNumber(0);
        shoppingCartStoreVO1.setCommodityList(Arrays.asList(shoppingCartCommodityVO1));
        expectedResult.setInvalidStoreList(Arrays.asList(shoppingCartStoreVO1));

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityVO memberPriceApplyCommodityVO = new MemberPriceApplyCommodityVO();
        memberPriceApplyCommodityVO.setDiscountDynamics(new BigDecimal("0.00"));
        memberPriceApplyCommodityVO.setApplyGoodsType(0);
        memberPriceApplyCommodityVO.setCommodityCode(Arrays.asList("value"));
        memberPriceApplyCommodityVO.setCommodityId(Arrays.asList("value"));
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.success(
                memberPriceApplyCommodityVO);
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        final ShoppingCartVO result = shoppingCartManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_IHsaShoppingCartCommodityServiceReturnsNoItems() {
        // Setup
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(Collections.emptyList());

        // Run the test
        final ShoppingCartVO result = shoppingCartManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testQuery_StoreSupportReturnsNoItems() {
        // Setup
        final ShoppingCartVO expectedResult = new ShoppingCartVO();
        final ShoppingCartStoreVO shoppingCartStoreVO = new ShoppingCartStoreVO();
        shoppingCartStoreVO.setStoreId(0);
        shoppingCartStoreVO.setStoreName("name");
        shoppingCartStoreVO.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO.setCommodityName("commodityName");
        shoppingCartCommodityVO.setCommodityPrice("0.01");
        shoppingCartCommodityVO.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO.setSkuName("skuName");
        shoppingCartCommodityVO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO.setCommodityCode("commodity_code");
        shoppingCartCommodityVO.setCommodityImg("commodityImg");
        shoppingCartCommodityVO.setNum(0);
        shoppingCartCommodityVO.setStartingNumber(0);
        shoppingCartStoreVO.setCommodityList(Arrays.asList(shoppingCartCommodityVO));
        expectedResult.setStoreList(Arrays.asList(shoppingCartStoreVO));
        final ShoppingCartStoreVO shoppingCartStoreVO1 = new ShoppingCartStoreVO();
        shoppingCartStoreVO1.setStoreId(0);
        shoppingCartStoreVO1.setStoreName("name");
        shoppingCartStoreVO1.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO1 = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO1.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO1.setCommodityName("commodityName");
        shoppingCartCommodityVO1.setCommodityPrice("0.01");
        shoppingCartCommodityVO1.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO1.setSkuName("skuName");
        shoppingCartCommodityVO1.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO1.setCommodityCode("commodity_code");
        shoppingCartCommodityVO1.setCommodityImg("commodityImg");
        shoppingCartCommodityVO1.setNum(0);
        shoppingCartCommodityVO1.setStartingNumber(0);
        shoppingCartStoreVO1.setCommodityList(Arrays.asList(shoppingCartCommodityVO1));
        expectedResult.setInvalidStoreList(Arrays.asList(shoppingCartStoreVO1));

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityVO memberPriceApplyCommodityVO = new MemberPriceApplyCommodityVO();
        memberPriceApplyCommodityVO.setDiscountDynamics(new BigDecimal("0.00"));
        memberPriceApplyCommodityVO.setApplyGoodsType(0);
        memberPriceApplyCommodityVO.setCommodityCode(Arrays.asList("value"));
        memberPriceApplyCommodityVO.setCommodityId(Arrays.asList("value"));
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.success(
                memberPriceApplyCommodityVO);
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        final ShoppingCartVO result = shoppingCartManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_MemberBaseFeignReturnsNoItem() {
        // Setup
        final ShoppingCartVO expectedResult = new ShoppingCartVO();
        final ShoppingCartStoreVO shoppingCartStoreVO = new ShoppingCartStoreVO();
        shoppingCartStoreVO.setStoreId(0);
        shoppingCartStoreVO.setStoreName("name");
        shoppingCartStoreVO.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO.setCommodityName("commodityName");
        shoppingCartCommodityVO.setCommodityPrice("0.01");
        shoppingCartCommodityVO.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO.setSkuName("skuName");
        shoppingCartCommodityVO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO.setCommodityCode("commodity_code");
        shoppingCartCommodityVO.setCommodityImg("commodityImg");
        shoppingCartCommodityVO.setNum(0);
        shoppingCartCommodityVO.setStartingNumber(0);
        shoppingCartStoreVO.setCommodityList(Arrays.asList(shoppingCartCommodityVO));
        expectedResult.setStoreList(Arrays.asList(shoppingCartStoreVO));
        final ShoppingCartStoreVO shoppingCartStoreVO1 = new ShoppingCartStoreVO();
        shoppingCartStoreVO1.setStoreId(0);
        shoppingCartStoreVO1.setStoreName("name");
        shoppingCartStoreVO1.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO1 = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO1.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO1.setCommodityName("commodityName");
        shoppingCartCommodityVO1.setCommodityPrice("0.01");
        shoppingCartCommodityVO1.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO1.setSkuName("skuName");
        shoppingCartCommodityVO1.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO1.setCommodityCode("commodity_code");
        shoppingCartCommodityVO1.setCommodityImg("commodityImg");
        shoppingCartCommodityVO1.setNum(0);
        shoppingCartCommodityVO1.setStartingNumber(0);
        shoppingCartStoreVO1.setCommodityList(Arrays.asList(shoppingCartCommodityVO1));
        expectedResult.setInvalidStoreList(Arrays.asList(shoppingCartStoreVO1));

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(Result.success());

        // Run the test
        final ShoppingCartVO result = shoppingCartManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQuery_MemberBaseFeignReturnsError() {
        // Setup
        final ShoppingCartVO expectedResult = new ShoppingCartVO();
        final ShoppingCartStoreVO shoppingCartStoreVO = new ShoppingCartStoreVO();
        shoppingCartStoreVO.setStoreId(0);
        shoppingCartStoreVO.setStoreName("name");
        shoppingCartStoreVO.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO.setCommodityName("commodityName");
        shoppingCartCommodityVO.setCommodityPrice("0.01");
        shoppingCartCommodityVO.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO.setSkuName("skuName");
        shoppingCartCommodityVO.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO.setCommodityCode("commodity_code");
        shoppingCartCommodityVO.setCommodityImg("commodityImg");
        shoppingCartCommodityVO.setNum(0);
        shoppingCartCommodityVO.setStartingNumber(0);
        shoppingCartStoreVO.setCommodityList(Arrays.asList(shoppingCartCommodityVO));
        expectedResult.setStoreList(Arrays.asList(shoppingCartStoreVO));
        final ShoppingCartStoreVO shoppingCartStoreVO1 = new ShoppingCartStoreVO();
        shoppingCartStoreVO1.setStoreId(0);
        shoppingCartStoreVO1.setStoreName("name");
        shoppingCartStoreVO1.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO1 = new ShoppingCartCommodityVO();
        shoppingCartCommodityVO1.setShoppingCartGuid("quantityNotEnoughGuid");
        shoppingCartCommodityVO1.setCommodityName("commodityName");
        shoppingCartCommodityVO1.setCommodityPrice("0.01");
        shoppingCartCommodityVO1.setMemberPrice(new BigDecimal("0.00"));
        shoppingCartCommodityVO1.setSkuName("skuName");
        shoppingCartCommodityVO1.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        shoppingCartCommodityVO1.setCommodityCode("commodity_code");
        shoppingCartCommodityVO1.setCommodityImg("commodityImg");
        shoppingCartCommodityVO1.setNum(0);
        shoppingCartCommodityVO1.setStartingNumber(0);
        shoppingCartStoreVO1.setCommodityList(Arrays.asList(shoppingCartCommodityVO1));
        expectedResult.setInvalidStoreList(Arrays.asList(shoppingCartStoreVO1));

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.error("");
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        final ShoppingCartVO result = shoppingCartManageUnderTest.query("memberInfoGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBatchDelete() {
        // Setup
        final SingleDataDTO request = new SingleDataDTO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setData("data");
        request.setDatas(Arrays.asList("value"));
        request.setType(0);

        // Run the test
        shoppingCartManageUnderTest.batchDelete(request);

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByIds(Arrays.asList("value"));
    }

    @Test
    public void testChangeNum() {
        // Setup
        final ShoppingCartNumChangeDTO request = new ShoppingCartNumChangeDTO();
        request.setShoppingCartGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        request.setMemberInfoGuid("memberInfoGuid");
        request.setChangeType(0);
        request.setChangeNum(0);

        // Run the test
        shoppingCartManageUnderTest.changeNum(request);

        // Verify the results
        verify(mockShoppingCartCommodityService).changeNum(
                new CartAddNumDTO("43f2fe8e-6576-41ff-b049-cbaf3314b063", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
    }

    @Test
    public void testClean() {
        // Setup
        // Run the test
        shoppingCartManageUnderTest.clean();

        // Verify the results
        verify(mockShoppingCartCommodityService).removeAll("operSubjectGuid");
    }

    @Test
    public void testCleanInvalid() {
        // Setup
        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityVO memberPriceApplyCommodityVO = new MemberPriceApplyCommodityVO();
        memberPriceApplyCommodityVO.setDiscountDynamics(new BigDecimal("0.00"));
        memberPriceApplyCommodityVO.setApplyGoodsType(0);
        memberPriceApplyCommodityVO.setCommodityCode(Arrays.asList("value"));
        memberPriceApplyCommodityVO.setCommodityId(Arrays.asList("value"));
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.success(
                memberPriceApplyCommodityVO);
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        shoppingCartManageUnderTest.cleanInvalid("memberInfoGuid");

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));
    }

    @Test
    public void testCleanInvalid_IHsaShoppingCartCommodityServiceListShoppingCartCommodityReturnsNoItems() {
        // Setup
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(Collections.emptyList());

        // Run the test
        shoppingCartManageUnderTest.cleanInvalid("memberInfoGuid");

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));
    }

    @Test
    public void testCleanInvalid_StoreSupportReturnsNoItems() {
        // Setup
        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityVO memberPriceApplyCommodityVO = new MemberPriceApplyCommodityVO();
        memberPriceApplyCommodityVO.setDiscountDynamics(new BigDecimal("0.00"));
        memberPriceApplyCommodityVO.setApplyGoodsType(0);
        memberPriceApplyCommodityVO.setCommodityCode(Arrays.asList("value"));
        memberPriceApplyCommodityVO.setCommodityId(Arrays.asList("value"));
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.success(
                memberPriceApplyCommodityVO);
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        shoppingCartManageUnderTest.cleanInvalid("memberInfoGuid");

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));
    }

    @Test
    public void testCleanInvalid_MemberBaseFeignReturnsNoItem() {
        // Setup
        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(Result.success());

        // Run the test
        shoppingCartManageUnderTest.cleanInvalid("memberInfoGuid");

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));
    }

    @Test
    public void testCleanInvalid_MemberBaseFeignReturnsError() {
        // Setup
        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity("memberInfoGuid",
                "operSubjectGuid")).thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure MemberBaseFeign.getMemberPriceApplyCommodity(...).
        final Result<MemberPriceApplyCommodityVO> memberPriceApplyCommodityVOResult = Result.error("");
        final MemberPriceApplyCommodityQO qo = new MemberPriceApplyCommodityQO();
        qo.setMemberInfoGuid("memberInfoGuid");
        qo.setTerminal("mini_program");
        qo.setBusiness("13");
        qo.setChannel("mall");
        qo.setBusinessType(0);
        when(mockBaseFeign.getMemberPriceApplyCommodity(qo)).thenReturn(memberPriceApplyCommodityVOResult);

        // Run the test
        shoppingCartManageUnderTest.cleanInvalid("memberInfoGuid");

        // Verify the results
        verify(mockShoppingCartCommodityService).removeByShoppingCartGuidList(Arrays.asList("value"));
    }

    @Test
    public void testListRecommendCommodity() {
        // Setup
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setChannel("会员商城");
        request.setIsDistinct(0);
        request.setSortType(0);
        request.setType(0);
        request.setCommodityIdList(Arrays.asList(0L));

        // Configure MemberCommodityFeign.pageCommodity(...).
        final CommodityConditionQO request1 = new CommodityConditionQO();
        request1.setCurrentPage(0);
        request1.setPageSize(0);
        request1.setStrategyStatus(0);
        request1.setCommodityState(0);
        request1.setChannel("会员商城");
        request1.setIsDistinct(0);
        request1.setSortType(0);
        request1.setType(0);
        request1.setCommodityIdList(Arrays.asList(0L));
        when(mockCommodityFeign.pageCommodity(request1)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test
        final PageResult result = shoppingCartManageUnderTest.listRecommendCommodity(request);

        // Verify the results
    }

    @Test
    public void testCommodityDetail1() {
        // Setup
        final CommodityDetailConditionQO shoppingCartGuid = new CommodityDetailConditionQO();
        shoppingCartGuid.setStoreId(0);
        shoppingCartGuid.setShoppingCartGuid("shoppingCartGuid");
        shoppingCartGuid.setCommodityCode("commodityCode");

        final CartCommodityDetailDTO expectedResult = new CartCommodityDetailDTO();
        expectedResult.setName("commodityName");
        expectedResult.setCommodityCode("commodity_code");
        expectedResult.setStartingNumber(0);
        expectedResult.setSelectedNumber(0);
        expectedResult.setCommodityImg(Arrays.asList("value"));
        expectedResult.setBasePrice("base_price");
        expectedResult.setCommodityGroupTypeName("commodity_group_type_name");
        final SelectDataVO selectDataVO = new SelectDataVO();
        final ComDataVO comDataVO = new ComDataVO();
        comDataVO.setCommodity_info_id(0L);
        comDataVO.setName("name");
        comDataVO.setConstitute_price(new BigDecimal("0.00"));
        selectDataVO.setCom_data(Arrays.asList(comDataVO));
        expectedResult.setSelectData(Arrays.asList(selectDataVO));
        final MustSelectDataVO mustSelectDataVO = new MustSelectDataVO();
        mustSelectDataVO.setCommodity_info_id(0L);
        mustSelectDataVO.setName("name");
        expectedResult.setMustSelectData(Arrays.asList(mustSelectDataVO));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        expectedResult.setSelectedSku(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        expectedResult.setSelectedAttr(Arrays.asList(shoppingCartAttrDTO));
        final CommodityAttrVO commodityAttrVO = new CommodityAttrVO();
        commodityAttrVO.setName("name");
        expectedResult.setAttr(Arrays.asList(commodityAttrVO));
        expectedResult.setFreight(new BigDecimal("0.00"));
        expectedResult.setUnit("unit");
        expectedResult.setCommodityId(0L);
        expectedResult.setStoreId(0);

        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsVO commodityDetailsVO = new CommodityDetailsVO();
        commodityDetailsVO.setS_id(0);
        commodityDetailsVO.setS_name("commodityName");
        commodityDetailsVO.setCommodity_code("commodity_code");
        commodityDetailsVO.setBase_price("base_price");
        commodityDetailsVO.setCommodity_group_type_name("commodity_group_type_name");
        commodityDetailsVO.setStore_state("store_state");
        commodityDetailsVO.setStarting_number(0);
        commodityDetailsVO.setCommodity_img(Arrays.asList("value"));
        final SelectDataVO selectDataVO1 = new SelectDataVO();
        final ComDataVO comDataVO1 = new ComDataVO();
        comDataVO1.setCommodity_info_id(0L);
        comDataVO1.setName("name");
        comDataVO1.setConstitute_price(new BigDecimal("0.00"));
        selectDataVO1.setCom_data(Arrays.asList(comDataVO1));
        commodityDetailsVO.setSelect_data(Arrays.asList(selectDataVO1));
        final MustSelectDataVO mustSelectDataVO1 = new MustSelectDataVO();
        mustSelectDataVO1.setCommodity_info_id(0L);
        mustSelectDataVO1.setName("name");
        commodityDetailsVO.setMust_select_data(Arrays.asList(mustSelectDataVO1));
        commodityDetailsVO.setUnit("unit");
        final CommodityAttrVO commodityAttrVO1 = new CommodityAttrVO();
        commodityAttrVO1.setName("name");
        commodityDetailsVO.setAttr(Arrays.asList(commodityAttrVO1));
        commodityDetailsVO.setFreight(new BigDecimal("0.00"));
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setCommodity_list(Arrays.asList(0L));
        when(mockCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(commodityDetailsVO);

        // Run the test
        final CartCommodityDetailDTO result = shoppingCartManageUnderTest.commodityDetail(shoppingCartGuid);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCommodityDetail1_MemberCommodityFeignReturnsNull() {
        // Setup
        final CommodityDetailConditionQO shoppingCartGuid = new CommodityDetailConditionQO();
        shoppingCartGuid.setStoreId(0);
        shoppingCartGuid.setShoppingCartGuid("shoppingCartGuid");
        shoppingCartGuid.setCommodityCode("commodityCode");

        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setCommodity_list(Arrays.asList(0L));
        when(mockCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> shoppingCartManageUnderTest.commodityDetail(shoppingCartGuid))
                .isInstanceOf(MallBaseException.class);
    }

    @Test
    public void testCommodityDetail2() {
        // Setup
        final CartCommodityDetailDTO expectedResult = new CartCommodityDetailDTO();
        expectedResult.setName("commodityName");
        expectedResult.setCommodityCode("commodity_code");
        expectedResult.setStartingNumber(0);
        expectedResult.setSelectedNumber(0);
        expectedResult.setCommodityImg(Arrays.asList("value"));
        expectedResult.setBasePrice("base_price");
        expectedResult.setCommodityGroupTypeName("commodity_group_type_name");
        final SelectDataVO selectDataVO = new SelectDataVO();
        final ComDataVO comDataVO = new ComDataVO();
        comDataVO.setCommodity_info_id(0L);
        comDataVO.setName("name");
        comDataVO.setConstitute_price(new BigDecimal("0.00"));
        selectDataVO.setCom_data(Arrays.asList(comDataVO));
        expectedResult.setSelectData(Arrays.asList(selectDataVO));
        final MustSelectDataVO mustSelectDataVO = new MustSelectDataVO();
        mustSelectDataVO.setCommodity_info_id(0L);
        mustSelectDataVO.setName("name");
        expectedResult.setMustSelectData(Arrays.asList(mustSelectDataVO));
        final ShoppingCartSkuDTO shoppingCartSkuDTO = new ShoppingCartSkuDTO();
        shoppingCartSkuDTO.setSkuIdList(Arrays.asList(0L));
        shoppingCartSkuDTO.setSkuNameList(Arrays.asList("value"));
        expectedResult.setSelectedSku(Arrays.asList(shoppingCartSkuDTO));
        final ShoppingCartAttrDTO shoppingCartAttrDTO = new ShoppingCartAttrDTO();
        expectedResult.setSelectedAttr(Arrays.asList(shoppingCartAttrDTO));
        final CommodityAttrVO commodityAttrVO = new CommodityAttrVO();
        commodityAttrVO.setName("name");
        expectedResult.setAttr(Arrays.asList(commodityAttrVO));
        expectedResult.setFreight(new BigDecimal("0.00"));
        expectedResult.setUnit("unit");
        expectedResult.setCommodityId(0L);
        expectedResult.setStoreId(0);

        // Configure IHsaShoppingCartCommodityService.queryByGuid(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryByGuid("shoppingCartGuid")).thenReturn(hsaShoppingCartCommodity);

        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsVO commodityDetailsVO = new CommodityDetailsVO();
        commodityDetailsVO.setS_id(0);
        commodityDetailsVO.setS_name("commodityName");
        commodityDetailsVO.setCommodity_code("commodity_code");
        commodityDetailsVO.setBase_price("base_price");
        commodityDetailsVO.setCommodity_group_type_name("commodity_group_type_name");
        commodityDetailsVO.setStore_state("store_state");
        commodityDetailsVO.setStarting_number(0);
        commodityDetailsVO.setCommodity_img(Arrays.asList("value"));
        final SelectDataVO selectDataVO1 = new SelectDataVO();
        final ComDataVO comDataVO1 = new ComDataVO();
        comDataVO1.setCommodity_info_id(0L);
        comDataVO1.setName("name");
        comDataVO1.setConstitute_price(new BigDecimal("0.00"));
        selectDataVO1.setCom_data(Arrays.asList(comDataVO1));
        commodityDetailsVO.setSelect_data(Arrays.asList(selectDataVO1));
        final MustSelectDataVO mustSelectDataVO1 = new MustSelectDataVO();
        mustSelectDataVO1.setCommodity_info_id(0L);
        mustSelectDataVO1.setName("name");
        commodityDetailsVO.setMust_select_data(Arrays.asList(mustSelectDataVO1));
        commodityDetailsVO.setUnit("unit");
        final CommodityAttrVO commodityAttrVO1 = new CommodityAttrVO();
        commodityAttrVO1.setName("name");
        commodityDetailsVO.setAttr(Arrays.asList(commodityAttrVO1));
        commodityDetailsVO.setFreight(new BigDecimal("0.00"));
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setCommodity_list(Arrays.asList(0L));
        when(mockCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(commodityDetailsVO);

        // Run the test
        final CartCommodityDetailDTO result = shoppingCartManageUnderTest.commodityDetail("shoppingCartGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCommodityDetail2_IHsaShoppingCartCommodityServiceReturnsNull() {
        // Setup
        when(mockShoppingCartCommodityService.queryByGuid("shoppingCartGuid")).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> shoppingCartManageUnderTest.commodityDetail("shoppingCartGuid"))
                .isInstanceOf(MallBaseException.class);
    }

    @Test
    public void testCommodityDetail2_MemberCommodityFeignReturnsNull() {
        // Setup
        // Configure IHsaShoppingCartCommodityService.queryByGuid(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        when(mockShoppingCartCommodityService.queryByGuid("shoppingCartGuid")).thenReturn(hsaShoppingCartCommodity);

        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setCommodity_list(Arrays.asList(0L));
        when(mockCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> shoppingCartManageUnderTest.commodityDetail("shoppingCartGuid"))
                .isInstanceOf(MallBaseException.class);
    }

    @Test
    public void testToSettleCheck() {
        // Setup
        final SettleCheckDTO expectedResult = new SettleCheckDTO();
        expectedResult.setQuantityNotEnoughGuid("quantityNotEnoughGuid");
        expectedResult.setValidGuidList(Arrays.asList("value"));
        expectedResult.setInvalidToast("invalidToast");
        expectedResult.setNewStartingNumber(0);

        // Configure IHsaShoppingCartCommodityService.listByGuid(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listByGuid(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final SettleCheckDTO result = shoppingCartManageUnderTest.toSettleCheck(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testToSettleCheck_IHsaShoppingCartCommodityServiceReturnsNoItems() {
        // Setup
        final SettleCheckDTO expectedResult = new SettleCheckDTO();
        expectedResult.setQuantityNotEnoughGuid("quantityNotEnoughGuid");
        expectedResult.setValidGuidList(Arrays.asList("value"));
        expectedResult.setInvalidToast("invalidToast");
        expectedResult.setNewStartingNumber(0);

        when(mockShoppingCartCommodityService.listByGuid(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Configure StoreSupport.getStoreByIds(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> storeDataInfoVOS = Arrays.asList(storeDataInfoVO);
        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(storeDataInfoVOS);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final SettleCheckDTO result = shoppingCartManageUnderTest.toSettleCheck(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testToSettleCheck_StoreSupportReturnsNoItems() {
        // Setup
        final SettleCheckDTO expectedResult = new SettleCheckDTO();
        expectedResult.setQuantityNotEnoughGuid("quantityNotEnoughGuid");
        expectedResult.setValidGuidList(Arrays.asList("value"));
        expectedResult.setInvalidToast("invalidToast");
        expectedResult.setNewStartingNumber(0);

        // Configure IHsaShoppingCartCommodityService.listByGuid(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("43f2fe8e-6576-41ff-b049-cbaf3314b063");
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setCommodityCode("commodityCode");
        hsaShoppingCartCommodity.setStoreId(0);
        hsaShoppingCartCommodity.setNum(0);
        hsaShoppingCartCommodity.setCommodityName("commodityName");
        hsaShoppingCartCommodity.setCommodityImg("commodityImg");
        hsaShoppingCartCommodity.setBasePrice("0.01");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listByGuid(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        when(mockStoreSupport.getStoreByIds(Arrays.asList(0))).thenReturn(Collections.emptyList());
        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Run the test
        final SettleCheckDTO result = shoppingCartManageUnderTest.toSettleCheck(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
