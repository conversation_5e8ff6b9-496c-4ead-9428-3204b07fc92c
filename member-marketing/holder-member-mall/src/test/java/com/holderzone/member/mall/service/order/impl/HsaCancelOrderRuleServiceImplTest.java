package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.qo.mall.order.CancelOrderRuleQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.order.OrderReasonVO;
import com.holderzone.member.mall.entity.order.HsaCancelOrderRule;
import com.holderzone.member.mall.mapper.order.HsaCancelOrderRuleMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaCancelOrderRuleServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaCancelOrderRuleMapper mockHsaCancelOrderRuleMapper;

    @InjectMocks
    private HsaCancelOrderRuleServiceImpl hsaCancelOrderRuleServiceImplUnderTest;

    @Test
    public void testAddOrUpdate() {
        // Setup
        final CancelOrderRuleQO cancelOrderRuleQO = new CancelOrderRuleQO();
        cancelOrderRuleQO.setGuid("guid");
        cancelOrderRuleQO.setCancel("cancel");
        cancelOrderRuleQO.setCancelType("cancelType");
        cancelOrderRuleQO.setCustomInput(0);
        cancelOrderRuleQO.setIsDelete(0);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("7c5419ef-f63f-4f6d-aa47-cd6051792f53");

        // Configure HsaCancelOrderRuleMapper.queryByGuid(...).
        final HsaCancelOrderRule hsaCancelOrderRule = new HsaCancelOrderRule();
        hsaCancelOrderRule.setId(0L);
        hsaCancelOrderRule.setGuid("7c5419ef-f63f-4f6d-aa47-cd6051792f53");
        hsaCancelOrderRule.setOperSubjectGuid("operSubjectGuid");
        hsaCancelOrderRule.setCancelType("cancelType");
        hsaCancelOrderRule.setIsDelete(0);
        when(mockHsaCancelOrderRuleMapper.queryByGuid("guid")).thenReturn(hsaCancelOrderRule);

        // Run the test
        final boolean result = hsaCancelOrderRuleServiceImplUnderTest.addOrUpdate(cancelOrderRuleQO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm HsaCancelOrderRuleMapper.insert(...).
        final HsaCancelOrderRule t = new HsaCancelOrderRule();
        t.setId(0L);
        t.setGuid("7c5419ef-f63f-4f6d-aa47-cd6051792f53");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setCancelType("cancelType");
        t.setIsDelete(0);
        verify(mockHsaCancelOrderRuleMapper).insert(t);

        // Confirm HsaCancelOrderRuleMapper.updateByGuid(...).
        final HsaCancelOrderRule t1 = new HsaCancelOrderRule();
        t1.setId(0L);
        t1.setGuid("7c5419ef-f63f-4f6d-aa47-cd6051792f53");
        t1.setOperSubjectGuid("operSubjectGuid");
        t1.setCancelType("cancelType");
        t1.setIsDelete(0);
        verify(mockHsaCancelOrderRuleMapper).updateByGuid(t1);
    }

    @Test
    public void testGetCancelOrderRule() {
        // Setup
        final CancelOrderRuleQO cancelOrderRuleQO = new CancelOrderRuleQO();
        cancelOrderRuleQO.setGuid("guid");
        cancelOrderRuleQO.setCancel("cancel");
        cancelOrderRuleQO.setCancelType("cancelType");
        cancelOrderRuleQO.setCustomInput(0);
        cancelOrderRuleQO.setIsDelete(0);
        final List<CancelOrderRuleQO> expectedResult = Arrays.asList(cancelOrderRuleQO);

        // Configure HsaCancelOrderRuleMapper.selectList(...).
        final HsaCancelOrderRule hsaCancelOrderRule = new HsaCancelOrderRule();
        hsaCancelOrderRule.setId(0L);
        hsaCancelOrderRule.setGuid("7c5419ef-f63f-4f6d-aa47-cd6051792f53");
        hsaCancelOrderRule.setOperSubjectGuid("operSubjectGuid");
        hsaCancelOrderRule.setCancelType("cancelType");
        hsaCancelOrderRule.setIsDelete(0);
        final List<HsaCancelOrderRule> hsaCancelOrderRules = Arrays.asList(hsaCancelOrderRule);
        when(mockHsaCancelOrderRuleMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaCancelOrderRules);

        // Run the test
        final List<CancelOrderRuleQO> result = hsaCancelOrderRuleServiceImplUnderTest.getCancelOrderRule();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetCancelOrderRule_HsaCancelOrderRuleMapperReturnsNoItems() {
        // Setup
        when(mockHsaCancelOrderRuleMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<CancelOrderRuleQO> result = hsaCancelOrderRuleServiceImplUnderTest.getCancelOrderRule();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteCancelOrderRule() {
        // Setup
        // Run the test
        final boolean result = hsaCancelOrderRuleServiceImplUnderTest.deleteCancelOrderRule(
                "0ae92a13-3628-48b5-a503-e7ab1b74577a");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockHsaCancelOrderRuleMapper).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testListReason() {
        // Setup
        final OrderReasonVO orderReasonVO = new OrderReasonVO();
        orderReasonVO.setCancel("cancel");
        orderReasonVO.setCustomInput((byte) 0b0);
        final List<OrderReasonVO> expectedResult = Arrays.asList(orderReasonVO);

        // Run the test
        final List<OrderReasonVO> result = hsaCancelOrderRuleServiceImplUnderTest.listReason("type");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
