package com.holderzone.member.mall.controller.logistics;

import com.holderzone.member.common.dto.logistics.KdhTrackDetail;
import com.holderzone.member.common.qo.logistics.ExpressTrackQO;
import com.holderzone.member.mall.service.logistics.ExpressService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(ExpressController.class)
public class ExpressControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ExpressService mockExpressService;

    @Test
    public void testQueryTrack() throws Exception {
        // Setup
        // Configure ExpressService.queryExpress(...).
        final KdhTrackDetail kdhTrackDetail = new KdhTrackDetail();
        kdhTrackDetail.setTime("time");
        kdhTrackDetail.setContext("context");
        kdhTrackDetail.setStatus("status");
        final List<KdhTrackDetail> kdhTrackDetails = Arrays.asList(kdhTrackDetail);
        final ExpressTrackQO expressTrackQO = new ExpressTrackQO();
        expressTrackQO.setLogisticsNumber("logisticsNumber");
        expressTrackQO.setLogisticsName("logisticsName");
        expressTrackQO.setLogisticsCode("logisticsCode");
        when(mockExpressService.queryExpress(expressTrackQO)).thenReturn(kdhTrackDetails);

        // Run the test and verify the results
        mockMvc.perform(post("/express/query_track")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testQueryTrack_ExpressServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure ExpressService.queryExpress(...).
        final ExpressTrackQO expressTrackQO = new ExpressTrackQO();
        expressTrackQO.setLogisticsNumber("logisticsNumber");
        expressTrackQO.setLogisticsName("logisticsName");
        expressTrackQO.setLogisticsCode("logisticsCode");
        when(mockExpressService.queryExpress(expressTrackQO)).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/express/query_track")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }
}
