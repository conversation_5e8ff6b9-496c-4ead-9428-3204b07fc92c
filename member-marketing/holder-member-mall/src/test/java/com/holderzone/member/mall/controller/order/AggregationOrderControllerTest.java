package com.holderzone.member.mall.controller.order;

import com.holderzone.member.common.dto.order.aggregation.StoreOrderDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.order.AggregationOrderDetailResponseVO;
import com.holderzone.member.common.qo.order.AggregationOrderQueryVO;
import com.holderzone.member.mall.adpter.AggregationOrderAdapter;
import com.holderzone.member.mall.service.order.HsaAggregationOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AggregationOrderController.class)
public class AggregationOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaAggregationOrderService mockAggregationOrderService;
    @MockBean
    private AggregationOrderAdapter mockAggregationOrderAdapter;

    @Test
    public void testPush() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/aggregation/order/store/push")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaAggregationOrderService.storeOrderPush(...).
        final StoreOrderDTO storeOrderDTO = new StoreOrderDTO();
        storeOrderDTO.setEnterpriseGuid("enterpriseGuid");
        storeOrderDTO.setOperSubjectGuid("operSubjectGuid");
        storeOrderDTO.setOrderRecordGuid("orderRecordGuid");
        storeOrderDTO.setOpenId("openId");
        final StoreOrderDTO.InnerOrder orderDTO = new StoreOrderDTO.InnerOrder();
        storeOrderDTO.setOrderDTO(orderDTO);
        verify(mockAggregationOrderService).storeOrderPush(storeOrderDTO);
    }

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure AggregationOrderAdapter.pageInfo(...).
        final AggregationOrderQueryVO queryVO = new AggregationOrderQueryVO();
        queryVO.setOrderSource(0);
        queryVO.setMemberInfoGuid("memberInfoGuid");
        queryVO.setOperSubjectGuid("operSubjectGuid");
        when(mockAggregationOrderAdapter.pageInfo(queryVO)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/aggregation/order/list")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGet() throws Exception {
        // Setup
        // Configure AggregationOrderAdapter.getDetail(...).
        final AggregationOrderDetailResponseVO aggregationOrderDetailResponseVO = new AggregationOrderDetailResponseVO();
        aggregationOrderDetailResponseVO.setGuid("a83381d7-0da5-4c73-9e88-22ff2589bdcc");
        aggregationOrderDetailResponseVO.setStoreName("storeName");
        aggregationOrderDetailResponseVO.setDiningTableName("diningTableName");
        aggregationOrderDetailResponseVO.setGuestCount(0);
        aggregationOrderDetailResponseVO.setState(0);
        when(mockAggregationOrderAdapter.getDetail("7945b799-bbdd-4b91-81f7-bb3f8f4444ad"))
                .thenReturn(aggregationOrderDetailResponseVO);

        // Run the test and verify the results
        mockMvc.perform(get("/aggregation/order/get/{guid}", "7945b799-bbdd-4b91-81f7-bb3f8f4444ad")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
