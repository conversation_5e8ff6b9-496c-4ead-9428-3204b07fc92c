package com.holderzone.member.mall.service.set.impl;

import com.holderzone.member.common.dto.mall.set.CommonSetDTO;
import com.holderzone.member.mall.service.cache.CacheService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaCommonSetServiceImplTest {

    @Mock
    private CacheService mockCacheService;

    private HsaCommonSetServiceImpl hsaCommonSetServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaCommonSetServiceImplUnderTest = new HsaCommonSetServiceImpl(mockCacheService);
    }

    @Test
    public void testInit() {
        // Setup
        // Run the test
        hsaCommonSetServiceImplUnderTest.init(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testUpdateCommonSet() {
        // Setup
        final CommonSetDTO commonSetDTO = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);

        // Run the test
        hsaCommonSetServiceImplUnderTest.updateCommonSet(commonSetDTO);

        // Verify the results
        verify(mockCacheService).cacheValueSet("key", "value");
    }

    @Test
    public void testQueryByOperSubject() {
        // Setup
        final CommonSetDTO expectedResult = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);
        when(mockCacheService.cacheValueQuery("key")).thenReturn("result");

        // Run the test
        final CommonSetDTO result = hsaCommonSetServiceImplUnderTest.queryByOperSubject();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByOperSubject_CacheServiceCacheValueQueryReturnsNull() {
        // Setup
        final CommonSetDTO expectedResult = new CommonSetDTO(0L, "operSubjectGuid", 0, "floatImg", 0);
        when(mockCacheService.cacheValueQuery("key")).thenReturn(null);

        // Run the test
        final CommonSetDTO result = hsaCommonSetServiceImplUnderTest.queryByOperSubject();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockCacheService).cacheValueSet("key", "value");
    }
}
