package com.holderzone.member.mall.controller.order;

import com.holderzone.member.common.dto.order.AgreeRefundDTO;
import com.holderzone.member.common.dto.order.RefuseRefundDTO;
import com.holderzone.member.common.qo.mall.order.ApplyRefundQO;
import com.holderzone.member.mall.manage.AfterSaleOrderManage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(AfterSaleOrderController.class)
public class AfterSaleOrderControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AfterSaleOrderManage mockAfterSaleOrderManage;

    @Test
    public void testMemberApplyRefund() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/after_sale_order/member_apply_refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm AfterSaleOrderManage.memberApplyRefund(...).
        final ApplyRefundQO refundQO = new ApplyRefundQO();
        verify(mockAfterSaleOrderManage).memberApplyRefund(refundQO);
    }

    @Test
    public void testBatchAgreeRefund() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/after_sale_order/batch_agree_refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAfterSaleOrderManage).batchAgreeRefund(
                new AgreeRefundDTO("afterOrderGuid", new BigDecimal("0.00"), Arrays.asList("value")));
    }

    @Test
    public void testBatchRefuseRefund() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/after_sale_order/batch_refuse_refund")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAfterSaleOrderManage).batchRefuseRefund(
                new RefuseRefundDTO("refuseReason", Arrays.asList("value"), 0));
    }

    @Test
    public void testUserCancelRefund() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/after_sale_order/user_cancel_refund/{guid}", "06711879-af9e-4fcc-af6c-379b2e02b246")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockAfterSaleOrderManage).userCancelRefund("06711879-af9e-4fcc-af6c-379b2e02b246");
    }
}
