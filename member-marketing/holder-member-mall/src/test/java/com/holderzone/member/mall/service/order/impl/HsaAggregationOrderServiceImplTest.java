package com.holderzone.member.mall.service.order.impl;

import com.holderzone.member.common.dto.order.aggregation.StoreOrderDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.qo.order.AggregationOrderQueryVO;
import com.holderzone.member.common.qo.order.AggregationOrderResponseVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.cloud.OperSubjectCloudVO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import com.holderzone.member.mall.entity.order.HsaAggregationOrder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaAggregationOrderServiceImplTest {

    @Mock
    private MemberMallToolFeign mockMemberMallToolFeign;
    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;

    private HsaAggregationOrderServiceImpl hsaAggregationOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaAggregationOrderServiceImplUnderTest = new HsaAggregationOrderServiceImpl(mockMemberMallToolFeign,
                mockMemberBaseFeign, mockGuidGeneratorUtil);
    }

    @Test
    public void testGetByThirdOrderGuidAndBusinessType() {
        // Setup
        final HsaAggregationOrder expectedResult = new HsaAggregationOrder();
        expectedResult.setGuid("07246ba8-116a-4892-b171-ac2028ca919e");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setThirdEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setThirdOperSubjectGuid("operSubjectGuid");
        expectedResult.setThirdOrderGuid("guid");
        expectedResult.setOrderNo("orderNo");
        expectedResult.setStoreGuid("storeGuid");
        expectedResult.setStoreName("storeName");
        expectedResult.setOrderFee(new BigDecimal("0.00"));
        expectedResult.setActuallyPayFee(new BigDecimal("0.00"));
        expectedResult.setState(0);
        expectedResult.setMemberInfoGuid("memberGuid");
        expectedResult.setMemberPhone("memberPhone");
        expectedResult.setOpenId("openId");
        expectedResult.setBusinessType("businessType");
        expectedResult.setDeviceType(0);
        expectedResult.setCheckinTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final HsaAggregationOrder result = hsaAggregationOrderServiceImplUnderTest.getByThirdOrderGuidAndBusinessType(
                "orderGuid", "businessType");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testStoreOrderPush() {
        // Setup
        final StoreOrderDTO storeOrderDTO = new StoreOrderDTO();
        storeOrderDTO.setEnterpriseGuid("enterpriseGuid");
        storeOrderDTO.setOperSubjectGuid("operSubjectGuid");
        storeOrderDTO.setOrderRecordGuid("orderRecordGuid");
        storeOrderDTO.setOpenId("openId");
        final StoreOrderDTO.InnerOrder orderDTO = new StoreOrderDTO.InnerOrder();
        orderDTO.setGuid("guid");
        orderDTO.setOrderNo("orderNo");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        orderDTO.setDeviceType(0);
        orderDTO.setOrderFee(new BigDecimal("0.00"));
        orderDTO.setActuallyPayFee(new BigDecimal("0.00"));
        orderDTO.setMemberPhone("memberPhone");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeOrderDTO.setOrderDTO(orderDTO);
        storeOrderDTO.setPhone("memberPhone");

        // Configure MemberMallToolFeign.queryByMultiOperSubjectGuid(...).
        final OperSubjectCloudVO operSubjectCloudVO = new OperSubjectCloudVO();
        operSubjectCloudVO.setGuid("6a7e945d-64f5-4283-a01b-90b7c52e64a0");
        operSubjectCloudVO.setMultiEnterpriseGuid("multiEnterpriseGuid");
        operSubjectCloudVO.setOperSubjectGuid("operSubjectGuid");
        operSubjectCloudVO.setMultiOperSubiectGuid("multiOperSubiectGuid");
        operSubjectCloudVO.setEnterpriseGuid("enterpriseGuid");
        when(mockMemberMallToolFeign.queryByMultiOperSubjectGuid("operSubjectGuid")).thenReturn(operSubjectCloudVO);

        // Configure MemberBaseFeign.getOperationMemberInfoList(...).
        final MemberInfoVO memberInfoVO = new MemberInfoVO();
        memberInfoVO.setOperSubjectGuid("operSubjectGuid");
        memberInfoVO.setEnterpriseGuid("enterpriseGuid");
        memberInfoVO.setAccountState(0);
        memberInfoVO.setMemberGuid("memberGuid");
        memberInfoVO.setMemberAccount("memberAccount");
        final List<MemberInfoVO> memberInfoVOS = Arrays.asList(memberInfoVO);
        final MemberListQO query = new MemberListQO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setOperSubjectGuid("operSubjectGuid");
        query.setIsDisable(0);
        query.setBusinessPhoneNum(Arrays.asList("value"));
        when(mockMemberBaseFeign.getOperationMemberInfoList(query)).thenReturn(memberInfoVOS);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("07246ba8-116a-4892-b171-ac2028ca919e");

        // Run the test
        hsaAggregationOrderServiceImplUnderTest.storeOrderPush(storeOrderDTO);

        // Verify the results
    }

    @Test
    public void testStoreOrderPush_MemberBaseFeignReturnsNoItems() {
        // Setup
        final StoreOrderDTO storeOrderDTO = new StoreOrderDTO();
        storeOrderDTO.setEnterpriseGuid("enterpriseGuid");
        storeOrderDTO.setOperSubjectGuid("operSubjectGuid");
        storeOrderDTO.setOrderRecordGuid("orderRecordGuid");
        storeOrderDTO.setOpenId("openId");
        final StoreOrderDTO.InnerOrder orderDTO = new StoreOrderDTO.InnerOrder();
        orderDTO.setGuid("guid");
        orderDTO.setOrderNo("orderNo");
        orderDTO.setStoreGuid("storeGuid");
        orderDTO.setStoreName("storeName");
        orderDTO.setDeviceType(0);
        orderDTO.setOrderFee(new BigDecimal("0.00"));
        orderDTO.setActuallyPayFee(new BigDecimal("0.00"));
        orderDTO.setMemberPhone("memberPhone");
        orderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderDTO.setCheckoutTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        storeOrderDTO.setOrderDTO(orderDTO);
        storeOrderDTO.setPhone("memberPhone");

        // Configure MemberMallToolFeign.queryByMultiOperSubjectGuid(...).
        final OperSubjectCloudVO operSubjectCloudVO = new OperSubjectCloudVO();
        operSubjectCloudVO.setGuid("6a7e945d-64f5-4283-a01b-90b7c52e64a0");
        operSubjectCloudVO.setMultiEnterpriseGuid("multiEnterpriseGuid");
        operSubjectCloudVO.setOperSubjectGuid("operSubjectGuid");
        operSubjectCloudVO.setMultiOperSubiectGuid("multiOperSubiectGuid");
        operSubjectCloudVO.setEnterpriseGuid("enterpriseGuid");
        when(mockMemberMallToolFeign.queryByMultiOperSubjectGuid("operSubjectGuid")).thenReturn(operSubjectCloudVO);

        // Configure MemberBaseFeign.getOperationMemberInfoList(...).
        final MemberListQO query = new MemberListQO();
        query.setCurrentPage(0);
        query.setPageSize(0);
        query.setOperSubjectGuid("operSubjectGuid");
        query.setIsDisable(0);
        query.setBusinessPhoneNum(Arrays.asList("value"));
        when(mockMemberBaseFeign.getOperationMemberInfoList(query)).thenReturn(Collections.emptyList());

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("07246ba8-116a-4892-b171-ac2028ca919e");

        // Run the test
        hsaAggregationOrderServiceImplUnderTest.storeOrderPush(storeOrderDTO);

        // Verify the results
    }

    @Test
    public void testPageInfo() {
        // Setup
        final AggregationOrderQueryVO queryVO = new AggregationOrderQueryVO();
        queryVO.setCurrentPage(0);
        queryVO.setPageSize(0);
        queryVO.setOrderSource(0);
        queryVO.setMemberInfoGuid("memberInfoGuid");
        queryVO.setOperSubjectGuid("operSubjectGuid");

        final PageResult<AggregationOrderResponseVO> expectedResult = new PageResult<>(0, 0, 0);

        // Run the test
        final PageResult<AggregationOrderResponseVO> result = hsaAggregationOrderServiceImplUnderTest.pageInfo(queryVO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
