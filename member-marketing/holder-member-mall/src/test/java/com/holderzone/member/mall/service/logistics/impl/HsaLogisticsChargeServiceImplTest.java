package com.holderzone.member.mall.service.logistics.impl;

import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaLogisticsChargeServiceImplTest {

    private HsaLogisticsChargeServiceImpl hsaLogisticsChargeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaLogisticsChargeServiceImplUnderTest = new HsaLogisticsChargeServiceImpl();
    }

    @Test
    public void testFindByTemplateGuid() {
        // Setup
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("a61ffcbd-7dd3-4286-884b-b7ef923813f4");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> expectedResult = Arrays.asList(hsaLogisticsCharge);

        // Run the test
        final List<HsaLogisticsCharge> result = hsaLogisticsChargeServiceImplUnderTest.findByTemplateGuid(
                "templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRemoveByTemplateGuid() {
        // Setup
        // Run the test
        hsaLogisticsChargeServiceImplUnderTest.removeByTemplateGuid("templateGuid");

        // Verify the results
    }
}
