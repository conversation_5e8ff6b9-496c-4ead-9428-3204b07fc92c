package com.holderzone.member.mall.service.trade.impl;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.mall.trade.TradePayDTO;
import com.holderzone.member.common.dto.mall.trade.TradeRefundDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallOrderRefundDTO;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.util.replace.SystemRoleHelper;
import com.holderzone.member.common.vo.card.ConsumptionRespVO;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.PushOrderEvent;
import com.holderzone.member.mall.event.domain.EventEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaBalanceTradeServiceImplTest {

    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private PushOrderEvent mockPushOrderEvent;
    @Mock
    private MemberMallPublisher mockPublisher;
    @Mock
    private SystemRoleHelper mockSystemRoleHelper;

    private HsaBalanceTradeServiceImpl hsaBalanceTradeServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaBalanceTradeServiceImplUnderTest = new HsaBalanceTradeServiceImpl(mockMemberBaseFeign, mockPushOrderEvent,
                mockPublisher, mockSystemRoleHelper);
    }

    @Test
    public void testPayWay() {
        assertThat(hsaBalanceTradeServiceImplUnderTest.payWay()).isEqualTo(PayWayEnum.CARD_BALANCE_PAY);
    }

    @Test
    public void testPay() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        requestConfirmPayVO.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        requestConfirmPayVO.setMemberConsumptionGuid("memberConsumptionGuid");
        requestConfirmPayVO.setRefundAmount(new BigDecimal("0.00"));
        requestConfirmPayVO.setNoVerifyPassword("noVerifyPassword");
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);

        // Configure MemberBaseFeign.payOrder(...).
        final ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        consumptionRespVO.setMemberConsumptionGuid("memberConsumptionGuid");
        consumptionRespVO.setMemberInfoCardGuid("memberInfoCardGuid");
        consumptionRespVO.setStoreName("storeName");
        consumptionRespVO.setMemberPhone("memberPhone");
        consumptionRespVO.setCardPayMoney(new BigDecimal("0.00"));
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.success(consumptionRespVO);
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        final String result = hsaBalanceTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("memberConsumptionGuid");
    }

    @Test
    public void testPay_MemberBaseFeignReturnsNoItem() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        requestConfirmPayVO.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        requestConfirmPayVO.setMemberConsumptionGuid("memberConsumptionGuid");
        requestConfirmPayVO.setRefundAmount(new BigDecimal("0.00"));
        requestConfirmPayVO.setNoVerifyPassword("noVerifyPassword");
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);

        // Configure MemberBaseFeign.payOrder(...).
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(Result.success());

        // Run the test
        final String result = hsaBalanceTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("memberConsumptionGuid");
    }

    @Test
    public void testPay_MemberBaseFeignReturnsError() {
        // Setup
        final TradePayDTO tradePayDTO = new TradePayDTO();
        final RequestConfirmPayVO requestConfirmPayVO = new RequestConfirmPayVO();
        requestConfirmPayVO.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        requestConfirmPayVO.setMemberConsumptionGuid("memberConsumptionGuid");
        requestConfirmPayVO.setRefundAmount(new BigDecimal("0.00"));
        requestConfirmPayVO.setNoVerifyPassword("noVerifyPassword");
        tradePayDTO.setRequestConfirmPayVO(requestConfirmPayVO);

        // Configure MemberBaseFeign.payOrder(...).
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.error("");
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        final String result = hsaBalanceTradeServiceImplUnderTest.pay(tradePayDTO);

        // Verify the results
        assertThat(result).isEqualTo("memberConsumptionGuid");
    }

    @Test
    public void testRefund() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("5db2c5b6-3e9c-472d-ac98-791c0cdb6bc1");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setOrderNumber("memberConsumptionGuid");
        mallBaseOrderDTO.setPayMethod(0);
        tradeRefundDTO.setMallBaseOrderDTO(mallBaseOrderDTO);

        // Configure MemberBaseFeign.payOrder(...).
        final ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        consumptionRespVO.setMemberConsumptionGuid("memberConsumptionGuid");
        consumptionRespVO.setMemberInfoCardGuid("memberInfoCardGuid");
        consumptionRespVO.setStoreName("storeName");
        consumptionRespVO.setMemberPhone("memberPhone");
        consumptionRespVO.setCardPayMoney(new BigDecimal("0.00"));
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.success(consumptionRespVO);
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        final Integer result = hsaBalanceTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPublisher).publish(EventEnum.ORDER_PAY_CONDITION, "content");
    }

    @Test
    public void testRefund_MemberBaseFeignReturnsNoItem() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("5db2c5b6-3e9c-472d-ac98-791c0cdb6bc1");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setOrderNumber("memberConsumptionGuid");
        mallBaseOrderDTO.setPayMethod(0);
        tradeRefundDTO.setMallBaseOrderDTO(mallBaseOrderDTO);

        // Configure MemberBaseFeign.payOrder(...).
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(Result.success());

        // Run the test
        final Integer result = hsaBalanceTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPublisher).publish(EventEnum.ORDER_PAY_CONDITION, "content");
    }

    @Test
    public void testRefund_MemberBaseFeignReturnsError() {
        // Setup
        final TradeRefundDTO tradeRefundDTO = new TradeRefundDTO();
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        tradeRefundDTO.setOrderRefundDTO(orderRefundDTO);
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("5db2c5b6-3e9c-472d-ac98-791c0cdb6bc1");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setOrderNumber("memberConsumptionGuid");
        mallBaseOrderDTO.setPayMethod(0);
        tradeRefundDTO.setMallBaseOrderDTO(mallBaseOrderDTO);

        // Configure MemberBaseFeign.payOrder(...).
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.error("");
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("memberConsumptionGuid");
        request.setRefundAmount(new BigDecimal("0.00"));
        request.setNoVerifyPassword("noVerifyPassword");
        request.setIsCredit(0);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        final Integer result = hsaBalanceTradeServiceImplUnderTest.refund(tradeRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockPublisher).publish(EventEnum.ORDER_PAY_CONDITION, "content");
    }
}
