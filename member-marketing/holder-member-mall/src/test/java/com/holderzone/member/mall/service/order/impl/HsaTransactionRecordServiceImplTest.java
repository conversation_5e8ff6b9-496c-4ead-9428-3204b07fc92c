package com.holderzone.member.mall.service.order.impl;

import com.holderzone.member.mall.entity.order.HsaTransactionRecord;
import org.junit.Before;
import org.junit.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class HsaTransactionRecordServiceImplTest {

    private HsaTransactionRecordServiceImpl hsaTransactionRecordServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        hsaTransactionRecordServiceImplUnderTest = new HsaTransactionRecordServiceImpl();
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaTransactionRecord expectedResult = new HsaTransactionRecord();
        expectedResult.setGuid("0d5887fb-8eee-4830-9f1b-70d21b34ade5");
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setOperSubjectGuid("operSubjectGuid");

        // Run the test
        final HsaTransactionRecord result = hsaTransactionRecordServiceImplUnderTest.queryByGuid(
                "2a1c87e5-90d9-40e6-8868-f54fc0a93219");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuids() {
        // Setup
        final HsaTransactionRecord hsaTransactionRecord = new HsaTransactionRecord();
        hsaTransactionRecord.setGuid("0d5887fb-8eee-4830-9f1b-70d21b34ade5");
        hsaTransactionRecord.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaTransactionRecord.setOperSubjectGuid("operSubjectGuid");
        final List<HsaTransactionRecord> expectedResult = Arrays.asList(hsaTransactionRecord);

        // Run the test
        final List<HsaTransactionRecord> result = hsaTransactionRecordServiceImplUnderTest.queryByGuids(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testUpdateByGuid() {
        // Setup
        final HsaTransactionRecord t = new HsaTransactionRecord();
        t.setGuid("0d5887fb-8eee-4830-9f1b-70d21b34ade5");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setRefundTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setOperSubjectGuid("operSubjectGuid");

        // Run the test
        final boolean result = hsaTransactionRecordServiceImplUnderTest.updateByGuid(t);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuid() {
        // Setup
        // Run the test
        final boolean result = hsaTransactionRecordServiceImplUnderTest.removeByGuid(
                "d5387015-9e4e-44e6-8e3f-dc84efae35a3");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testRemoveByGuids() {
        // Setup
        // Run the test
        final boolean result = hsaTransactionRecordServiceImplUnderTest.removeByGuids(Arrays.asList("value"));

        // Verify the results
        assertThat(result).isFalse();
    }
}
