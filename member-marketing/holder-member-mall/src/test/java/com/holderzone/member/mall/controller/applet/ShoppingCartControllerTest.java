package com.holderzone.member.mall.controller.applet;

import com.holderzone.member.common.dto.mall.shoppingcart.*;
import com.holderzone.member.common.dto.order.SingleDataDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.commodity.CommodityDetailConditionQO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartCommodityVO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartStoreVO;
import com.holderzone.member.common.vo.shoppingcart.ShoppingCartVO;
import com.holderzone.member.mall.manage.ShoppingCartManage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(ShoppingCartController.class)
public class ShoppingCartControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ShoppingCartManage mockShoppingCartManage;

    @Test
    public void testAdd() throws Exception {
        // Setup
        // Configure ShoppingCartManage.add(...).
        final ShoppingCartAddDTO addDTO = new ShoppingCartAddDTO();
        addDTO.setMemberInfoGuid("memberInfoGuid");
        addDTO.setCommodityId(0L);
        addDTO.setStoreId(0);
        addDTO.setNum(0);
        addDTO.setChangeType(0);
        when(mockShoppingCartManage.add(addDTO)).thenReturn(0);

        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/add")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testEdit() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/edit")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm ShoppingCartManage.edit(...).
        final ShoppingCartEditDTO editDTO = new ShoppingCartEditDTO();
        editDTO.setGuid("a0bd9bb5-f391-4d22-b123-02827380d3b7");
        verify(mockShoppingCartManage).edit(editDTO);
    }

    @Test
    public void testQuery() throws Exception {
        // Setup
        // Configure ShoppingCartManage.query(...).
        final ShoppingCartVO shoppingCartVO = new ShoppingCartVO();
        final ShoppingCartStoreVO shoppingCartStoreVO = new ShoppingCartStoreVO();
        shoppingCartStoreVO.setStoreId(0);
        shoppingCartStoreVO.setStoreName("storeName");
        shoppingCartStoreVO.setLogo("logo");
        final ShoppingCartCommodityVO shoppingCartCommodityVO = new ShoppingCartCommodityVO();
        shoppingCartStoreVO.setCommodityList(Arrays.asList(shoppingCartCommodityVO));
        shoppingCartVO.setStoreList(Arrays.asList(shoppingCartStoreVO));
        when(mockShoppingCartManage.query("memberInfoGuid")).thenReturn(shoppingCartVO);

        // Run the test and verify the results
        mockMvc.perform(get("/shopping_cart/query")
                        .param("memberInfoGuid", "memberInfoGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testBatchDelete() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/batch_delete")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm ShoppingCartManage.batchDelete(...).
        final SingleDataDTO request = new SingleDataDTO();
        request.setData("data");
        request.setDatas(Arrays.asList("value"));
        request.setType(0);
        verify(mockShoppingCartManage).batchDelete(request);
    }

    @Test
    public void testChangeNum() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/change_num")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm ShoppingCartManage.changeNum(...).
        final ShoppingCartNumChangeDTO request = new ShoppingCartNumChangeDTO();
        request.setShoppingCartGuid("shoppingCartGuid");
        request.setMemberInfoGuid("memberInfoGuid");
        request.setChangeType(0);
        request.setChangeNum(0);
        verify(mockShoppingCartManage).changeNum(request);
    }

    @Test
    public void testClean() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/shopping_cart/clean")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockShoppingCartManage).clean();
    }

    @Test
    public void testCleanInvalid() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/shopping_cart/clean_invalid")
                        .param("memberInfoGuid", "memberInfoGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockShoppingCartManage).cleanInvalid("memberInfoGuid");
    }

    @Test
    public void testListRecommendCommodity() throws Exception {
        // Setup
        // Configure ShoppingCartManage.listRecommendCommodity(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setStoreId("storeId");
        request.setStoreIdList(Arrays.asList(0L));
        request.setStrategyStatus(0);
        request.setCommodityState(0);
        request.setStoreState(0);
        when(mockShoppingCartManage.listRecommendCommodity(request)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/list_recommend_commodity")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCommodityDetail1() throws Exception {
        // Setup
        // Configure ShoppingCartManage.commodityDetail(...).
        final CartCommodityDetailDTO cartCommodityDetailDTO = new CartCommodityDetailDTO();
        cartCommodityDetailDTO.setName("name");
        cartCommodityDetailDTO.setCommodityCode("commodityCode");
        cartCommodityDetailDTO.setStartingNumber(0);
        cartCommodityDetailDTO.setSelectedNumber(0);
        cartCommodityDetailDTO.setCommodityImg(Arrays.asList("value"));
        when(mockShoppingCartManage.commodityDetail("shoppingCartGuid")).thenReturn(cartCommodityDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(get("/shopping_cart/commodity_detail/{shoppingCartGuid}", "shoppingCartGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testToSettleCheck() throws Exception {
        // Setup
        // Configure ShoppingCartManage.toSettleCheck(...).
        final SettleCheckDTO settleCheckDTO = new SettleCheckDTO();
        settleCheckDTO.setQuantityNotEnoughGuid("quantityNotEnoughGuid");
        settleCheckDTO.setValidGuidList(Arrays.asList("value"));
        settleCheckDTO.setInvalidToast("invalidToast");
        settleCheckDTO.setNewStartingNumber(0);
        when(mockShoppingCartManage.toSettleCheck(Arrays.asList("value"))).thenReturn(settleCheckDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/settle_check")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testCommodityDetail2() throws Exception {
        // Setup
        // Configure ShoppingCartManage.commodityDetail(...).
        final CartCommodityDetailDTO cartCommodityDetailDTO = new CartCommodityDetailDTO();
        cartCommodityDetailDTO.setName("name");
        cartCommodityDetailDTO.setCommodityCode("commodityCode");
        cartCommodityDetailDTO.setStartingNumber(0);
        cartCommodityDetailDTO.setSelectedNumber(0);
        cartCommodityDetailDTO.setCommodityImg(Arrays.asList("value"));
        final CommodityDetailConditionQO shoppingCartGuid = new CommodityDetailConditionQO();
        shoppingCartGuid.setStoreId(0);
        shoppingCartGuid.setShoppingCartGuid("shoppingCartGuid");
        shoppingCartGuid.setCommodityCode("commodityCode");
        when(mockShoppingCartManage.commodityDetail(shoppingCartGuid)).thenReturn(cartCommodityDetailDTO);

        // Run the test and verify the results
        mockMvc.perform(post("/shopping_cart/commodity/detail")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
