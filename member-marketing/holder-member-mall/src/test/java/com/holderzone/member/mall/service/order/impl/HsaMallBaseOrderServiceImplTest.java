package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.MoreExecutors;
import com.holderzone.member.common.client.FileOssService;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.base.ResOrderCommodity;
import com.holderzone.member.common.dto.card.RequestOrderInfoDTO;
import com.holderzone.member.common.dto.card.RequestPayInfoDTO;
import com.holderzone.member.common.dto.excel.FileDto;
import com.holderzone.member.common.dto.mall.MallOrderProcessDTO;
import com.holderzone.member.common.dto.mall.OrderStatusDTO;
import com.holderzone.member.common.dto.order.AfterSaleOrderDTO;
import com.holderzone.member.common.dto.order.MallBaseOrderDTO;
import com.holderzone.member.common.dto.order.MallOrderRefundDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.mall.logistics.ExpressTypeEnum;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.TerOrderCallbackQO;
import com.holderzone.member.common.qo.commodity.CommodityConditionQO;
import com.holderzone.member.common.qo.equities.AccumulationReleaseKeyQO;
import com.holderzone.member.common.qo.equities.BaseOrderCancelQO;
import com.holderzone.member.common.qo.equities.OrderGenerateCallbackQO;
import com.holderzone.member.common.qo.integral.OrderIntegralDeductDetailQO;
import com.holderzone.member.common.qo.mall.order.*;
import com.holderzone.member.common.qo.tool.CommodityDetailsQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.card.ConsumptionRespVO;
import com.holderzone.member.common.vo.commodity.CommodityVO;
import com.holderzone.member.common.vo.grade.ComDataVO;
import com.holderzone.member.common.vo.grade.SelectDataVO;
import com.holderzone.member.common.vo.mall.OrderReceiverAddressVO;
import com.holderzone.member.common.vo.mall.PayOrderVO;
import com.holderzone.member.common.vo.order.*;
import com.holderzone.member.common.vo.tool.CommodityDetailsVO;
import com.holderzone.member.mall.entity.commodity.HsaDistributionSet;
import com.holderzone.member.mall.entity.order.*;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;
import com.holderzone.member.mall.event.MemberMallPublisher;
import com.holderzone.member.mall.event.PushOrderEvent;
import com.holderzone.member.mall.event.domain.EventEnum;
import com.holderzone.member.mall.mapper.commodity.HsaDistributionSetMapper;
import com.holderzone.member.mall.mapper.order.*;
import com.holderzone.member.mall.provider.factory.LogisticsServiceFactory;
import com.holderzone.member.mall.service.cache.CacheService;
import com.holderzone.member.mall.service.order.*;
import com.holderzone.member.mall.service.shoppingcart.IHsaShoppingCartCommodityService;
import com.holderzone.member.mall.service.trade.factory.HsaTradeServiceFactory;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import com.holderzone.member.mall.support.StoreSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMallBaseOrderServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private FileOssService mockFileOssService;
    @Mock
    private HsaMallBaseOrderMapper mockHsaMallBaseOrderMapper;
    @Mock
    private MemberCommodityFeign mockMemberCommodityFeign;
    @Mock
    private MemberBaseFeign mockMemberBaseFeign;
    @Mock
    private HsaAfterSaleOrderMapper mockHsaAfterSaleOrderMapper;
    @Mock
    private HsaMallOrderTimeRuleService mockHsaMallOrderTimeRuleService;
    @Mock
    private StoreSupport mockStoreSupport;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private HsaDistributionSetMapper mockHsaDistributionSetMapper;
    @Mock
    private HsaProductOrderDetailMapper mockHsaProductOrderDetailMapper;
    @Mock
    private HsaProductOrderDetailService mockProductOrderDetailService;
    @Mock
    private HsaMallOrderTimeRuleMapper mockHsaMallOrderTimeRuleMapper;
    @Mock
    private HsaOrderAutoConfigMapper mockOrderAutoConfigMapper;
    @Mock
    private CacheService mockCacheService;
    @Mock
    private HsaOrderReceiverAddressMapper mockHsaOrderReceiverAddressMapper;
    @Mock
    private LogisticsCacheSupport mockLogisticsCacheSupport;
    @Mock
    private HsaOrderReceiverAddressService mockHsaOrderReceiverAddressService;
    @Mock
    private LogisticsServiceFactory mockLogisticsServiceFactory;
    @Mock
    private HsaTradeServiceFactory mockTradeServiceFactory;
    @Mock
    private HsaOrderAutoConfigService mockOrderAutoConfigService;
    @Mock
    private PushOrderEvent mockPushOrderEvent;
    @Mock
    private HsaAfterSaleOrderService mockAfterSaleOrderService;
    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private MemberMallPublisher mockPublisher;
    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private MemberBaseFeign mockBaseFeign;
    @Mock
    private IHsaShoppingCartCommodityService mockShoppingCartCommodityService;

    @InjectMocks
    private HsaMallBaseOrderServiceImpl hsaMallBaseOrderServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(hsaMallBaseOrderServiceImplUnderTest, "memberMallThreadExecutor",
                MoreExecutors.directExecutor());
    }

    @Test
    public void testFindMallBaseOrderPage() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final PageResult result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderPage(queryMallOrderQO);

        // Verify the results
    }

    @Test
    public void testFindMallBaseOrderPage_HsaMallBaseOrderMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final PageResult result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderPage(queryMallOrderQO);

        // Verify the results
    }

    @Test
    public void testFindMallBaseOrderPage_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final PageResult result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderPage(queryMallOrderQO);

        // Verify the results
    }

    @Test
    public void testFindMallBaseOrderPage_HsaOrderReceiverAddressMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderPage(queryMallOrderQO);

        // Verify the results
    }

    @Test
    public void testFindMallBaseOrderList() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> expectedResult = Arrays.asList(mallBaseOrderVO);

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO1 = new MallBaseOrderVO();
        mallBaseOrderVO1.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO1.setOrderNumber("orderNumber");
        mallBaseOrderVO1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO1.setOrderCondition(0);
        mallBaseOrderVO1.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO1 = new ProductOrderVO();
        productOrderVO1.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO1.setProductId(0L);
        productOrderVO1.setProductName("product_name");
        productOrderVO1.setProductUnitPrice("price");
        productOrderVO1.setProductNum(0);
        productOrderVO1.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO1.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO1.setProductImg("productImg");
        productOrderVO1.setProductDetail("productDetail");
        mallBaseOrderVO1.setProductOrderVOS(Arrays.asList(productOrderVO1));
        mallBaseOrderVO1.setOrderAfterCondition(0);
        mallBaseOrderVO1.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO1 = new MallOrderProcessDTO();
        mallOrderProcessDTO1.setProcessState(0);
        mallOrderProcessDTO1.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO1.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO1));
        mallBaseOrderVO1.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO1.setMemberName("memberName");
        mallBaseOrderVO1.setReceiverGuid("receiverGuid");
        mallBaseOrderVO1.setReceiverPhone("receiverPhone");
        mallBaseOrderVO1.setReceiverName("receiverName");
        mallBaseOrderVO1.setReceiverAddress("receiverAddress");
        mallBaseOrderVO1.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO1);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final List<MallBaseOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderList(
                queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMallBaseOrderList_HsaMallBaseOrderMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final List<MallBaseOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderList(
                queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindMallBaseOrderList_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        // Run the test
        final List<MallBaseOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderList(
                queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindMallBaseOrderList_HsaOrderReceiverAddressMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO1)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<MallBaseOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderList(
                queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindMallBaseOrderProduct() {
        // Setup
        final ResOrderCommodity resOrderCommodity = new ResOrderCommodity();
        resOrderCommodity.setProductId(0);
        resOrderCommodity.setProductCode("productCode");
        resOrderCommodity.setProduct_name("product_name");
        resOrderCommodity.setQuantity(0);
        resOrderCommodity.setStrategyId(0);
        resOrderCommodity.setType_id(0);
        final List<ResOrderCommodity> expectedResult = Arrays.asList(resOrderCommodity);

        // Configure HsaMallBaseOrderMapper.selectOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityVO commodityVO = new CommodityVO();
        commodityVO.setCommodityCode("commodityCode");
        commodityVO.setCommodityName("commodityName");
        commodityVO.setCommodityId(0L);
        commodityVO.setCategoryId(0);
        commodityVO.setStrategyId(0);
        final List<CommodityVO> commodityVOS = Arrays.asList(commodityVO);
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStoreId("storeId");
        request.setStoreIdList(Arrays.asList(0L));
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockMemberCommodityFeign.listCommodityByIds(request)).thenReturn(commodityVOS);

        // Run the test
        final List<ResOrderCommodity> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderProduct(
                "orderNum");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMallBaseOrderProduct_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final ResOrderCommodity resOrderCommodity = new ResOrderCommodity();
        resOrderCommodity.setProductId(0);
        resOrderCommodity.setProductCode("productCode");
        resOrderCommodity.setProduct_name("product_name");
        resOrderCommodity.setQuantity(0);
        resOrderCommodity.setStrategyId(0);
        resOrderCommodity.setType_id(0);
        final List<ResOrderCommodity> expectedResult = Arrays.asList(resOrderCommodity);

        // Configure HsaMallBaseOrderMapper.selectOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityVO commodityVO = new CommodityVO();
        commodityVO.setCommodityCode("commodityCode");
        commodityVO.setCommodityName("commodityName");
        commodityVO.setCommodityId(0L);
        commodityVO.setCategoryId(0);
        commodityVO.setStrategyId(0);
        final List<CommodityVO> commodityVOS = Arrays.asList(commodityVO);
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStoreId("storeId");
        request.setStoreIdList(Arrays.asList(0L));
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockMemberCommodityFeign.listCommodityByIds(request)).thenReturn(commodityVOS);

        // Run the test
        final List<ResOrderCommodity> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderProduct(
                "orderNum");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMallBaseOrderProduct_MemberCommodityFeignReturnsNoItems() {
        // Setup
        // Configure HsaMallBaseOrderMapper.selectOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure MemberCommodityFeign.listCommodityByIds(...).
        final CommodityConditionQO request = new CommodityConditionQO();
        request.setCurrentPage(0);
        request.setPageSize(0);
        request.setStoreId("storeId");
        request.setStoreIdList(Arrays.asList(0L));
        request.setCommodityIdList(Arrays.asList(0L));
        when(mockMemberCommodityFeign.listCommodityByIds(request)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ResOrderCommodity> result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderProduct(
                "orderNum");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindMallBaseOrderDetails() {
        // Setup
        final MallBaseOrderDetailsVO expectedResult = new MallBaseOrderDetailsVO();
        expectedResult.setOrderCondition(0);
        expectedResult.setSurplusAfterTime(0L);
        expectedResult.setActualRefundAmount(new BigDecimal("0.00"));
        expectedResult.setOrderAfterCondition(0);
        expectedResult.setRefundCondition(0);
        expectedResult.setAfterGuid("afterGuid");
        expectedResult.setAfterGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancel("cancel");
        expectedResult.setRefundType(0);
        expectedResult.setAfterOrderNum("afterOrderNum");
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        expectedResult.setProductOrderVOS(Arrays.asList(productOrderVO));
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        expectedResult.setReceiverName("receiverName");
        expectedResult.setReceiverPhone("receiverPhone");
        expectedResult.setReceiverAddress("receiverAddress");
        expectedResult.setSenderName("linkman");
        expectedResult.setSenderAddress("detailAddress");
        expectedResult.setSenderConnect("linkmanPhone");
        expectedResult.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setPaidWaitTime(0L);
        expectedResult.setReceiveGoodsWaitTime(0L);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("071f87ff-4fe9-4f5d-8e75-5113f552f8bb"))
                .thenReturn(hsaMallBaseOrder);

        // Configure HsaAfterSaleOrderMapper.selectList(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setGuid("afterGuid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setActualRefundAmount(new BigDecimal("0.00"));
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefundType(0);
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaAfterSaleOrder> hsaAfterSaleOrders = Arrays.asList(hsaAfterSaleOrder);
        when(mockHsaAfterSaleOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrders);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("orderReceiverGuid")).thenReturn(hsaOrderReceiverAddress);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallBaseOrderDetailsVO result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderDetails(
                "071f87ff-4fe9-4f5d-8e75-5113f552f8bb");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMallBaseOrderDetails_HsaAfterSaleOrderMapperReturnsNoItems() {
        // Setup
        final MallBaseOrderDetailsVO expectedResult = new MallBaseOrderDetailsVO();
        expectedResult.setOrderCondition(0);
        expectedResult.setSurplusAfterTime(0L);
        expectedResult.setActualRefundAmount(new BigDecimal("0.00"));
        expectedResult.setOrderAfterCondition(0);
        expectedResult.setRefundCondition(0);
        expectedResult.setAfterGuid("afterGuid");
        expectedResult.setAfterGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancel("cancel");
        expectedResult.setRefundType(0);
        expectedResult.setAfterOrderNum("afterOrderNum");
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        expectedResult.setProductOrderVOS(Arrays.asList(productOrderVO));
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        expectedResult.setReceiverName("receiverName");
        expectedResult.setReceiverPhone("receiverPhone");
        expectedResult.setReceiverAddress("receiverAddress");
        expectedResult.setSenderName("linkman");
        expectedResult.setSenderAddress("detailAddress");
        expectedResult.setSenderConnect("linkmanPhone");
        expectedResult.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setPaidWaitTime(0L);
        expectedResult.setReceiveGoodsWaitTime(0L);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("071f87ff-4fe9-4f5d-8e75-5113f552f8bb"))
                .thenReturn(hsaMallBaseOrder);

        when(mockHsaAfterSaleOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("orderReceiverGuid")).thenReturn(hsaOrderReceiverAddress);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallBaseOrderDetailsVO result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderDetails(
                "071f87ff-4fe9-4f5d-8e75-5113f552f8bb");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindMallBaseOrderDetails_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final MallBaseOrderDetailsVO expectedResult = new MallBaseOrderDetailsVO();
        expectedResult.setOrderCondition(0);
        expectedResult.setSurplusAfterTime(0L);
        expectedResult.setActualRefundAmount(new BigDecimal("0.00"));
        expectedResult.setOrderAfterCondition(0);
        expectedResult.setRefundCondition(0);
        expectedResult.setAfterGuid("afterGuid");
        expectedResult.setAfterGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setCancel("cancel");
        expectedResult.setRefundType(0);
        expectedResult.setAfterOrderNum("afterOrderNum");
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        expectedResult.setProductOrderVOS(Arrays.asList(productOrderVO));
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        expectedResult.setReceiverName("receiverName");
        expectedResult.setReceiverPhone("receiverPhone");
        expectedResult.setReceiverAddress("receiverAddress");
        expectedResult.setSenderName("linkman");
        expectedResult.setSenderAddress("detailAddress");
        expectedResult.setSenderConnect("linkmanPhone");
        expectedResult.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setPaidWaitTime(0L);
        expectedResult.setReceiveGoodsWaitTime(0L);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("071f87ff-4fe9-4f5d-8e75-5113f552f8bb"))
                .thenReturn(hsaMallBaseOrder);

        // Configure HsaAfterSaleOrderMapper.selectList(...).
        final HsaAfterSaleOrder hsaAfterSaleOrder = new HsaAfterSaleOrder();
        hsaAfterSaleOrder.setGuid("afterGuid");
        hsaAfterSaleOrder.setOrderGuid("orderGuid");
        hsaAfterSaleOrder.setAfterOrderNum("afterOrderNum");
        hsaAfterSaleOrder.setRefundCondition(0);
        hsaAfterSaleOrder.setActualRefundAmount(new BigDecimal("0.00"));
        hsaAfterSaleOrder.setCancel("cancel");
        hsaAfterSaleOrder.setRefundType(0);
        hsaAfterSaleOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaAfterSaleOrder> hsaAfterSaleOrders = Arrays.asList(hsaAfterSaleOrder);
        when(mockHsaAfterSaleOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaAfterSaleOrders);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("orderReceiverGuid")).thenReturn(hsaOrderReceiverAddress);

        // Configure HsaOrderAutoConfigMapper.selectOne(...).
        final HsaOrderAutoConfig hsaOrderAutoConfig = new HsaOrderAutoConfig();
        hsaOrderAutoConfig.setOrderCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderReceiveTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderRefundConfirmTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaOrderAutoConfig.setOrderGuid("orderGuid");
        hsaOrderAutoConfig.setCancelState(0);
        when(mockOrderAutoConfigMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaOrderAutoConfig);

        // Run the test
        final MallBaseOrderDetailsVO result = hsaMallBaseOrderServiceImplUnderTest.findMallBaseOrderDetails(
                "071f87ff-4fe9-4f5d-8e75-5113f552f8bb");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testMallBaseOrderExport() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderCount(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderCount(queryMallOrderQO1)).thenReturn(0);

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO2 = new QueryMallOrderQO();
        queryMallOrderQO2.setCurrentPage(0);
        queryMallOrderQO2.setPageSize(0);
        queryMallOrderQO2.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO2.setKeywords("keywords");
        queryMallOrderQO2.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO2)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.mallBaseOrderExport(queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testMallBaseOrderExport_HsaMallBaseOrderMapperFindMallBaseOrderPageReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderCount(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderCount(queryMallOrderQO1)).thenReturn(0);

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final QueryMallOrderQO queryMallOrderQO2 = new QueryMallOrderQO();
        queryMallOrderQO2.setCurrentPage(0);
        queryMallOrderQO2.setPageSize(0);
        queryMallOrderQO2.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO2.setKeywords("keywords");
        queryMallOrderQO2.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO2)).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.mallBaseOrderExport(queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testMallBaseOrderExport_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderCount(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderCount(queryMallOrderQO1)).thenReturn(0);

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO2 = new QueryMallOrderQO();
        queryMallOrderQO2.setCurrentPage(0);
        queryMallOrderQO2.setPageSize(0);
        queryMallOrderQO2.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO2.setKeywords("keywords");
        queryMallOrderQO2.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO2)).thenReturn(mallBaseOrderVOS);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Configure HsaOrderReceiverAddressMapper.selectList(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        final List<HsaOrderReceiverAddress> hsaOrderReceiverAddresses = Arrays.asList(hsaOrderReceiverAddress);
        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaOrderReceiverAddresses);

        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.mallBaseOrderExport(queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testMallBaseOrderExport_HsaOrderReceiverAddressMapperReturnsNoItems() {
        // Setup
        final QueryMallOrderQO queryMallOrderQO = new QueryMallOrderQO();
        queryMallOrderQO.setCurrentPage(0);
        queryMallOrderQO.setPageSize(0);
        queryMallOrderQO.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO.setKeywords("keywords");
        queryMallOrderQO.setOrderNumber("orderNumber");

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderCount(...).
        final QueryMallOrderQO queryMallOrderQO1 = new QueryMallOrderQO();
        queryMallOrderQO1.setCurrentPage(0);
        queryMallOrderQO1.setPageSize(0);
        queryMallOrderQO1.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO1.setKeywords("keywords");
        queryMallOrderQO1.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderCount(queryMallOrderQO1)).thenReturn(0);

        // Configure HsaMallBaseOrderMapper.findMallBaseOrderPage(...).
        final MallBaseOrderVO mallBaseOrderVO = new MallBaseOrderVO();
        mallBaseOrderVO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderVO.setOrderNumber("orderNumber");
        mallBaseOrderVO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setOrderCondition(0);
        mallBaseOrderVO.setDeliveryMethod(0);
        final ProductOrderVO productOrderVO = new ProductOrderVO();
        productOrderVO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        productOrderVO.setProductId(0L);
        productOrderVO.setProductName("product_name");
        productOrderVO.setProductUnitPrice("price");
        productOrderVO.setProductNum(0);
        productOrderVO.setProductDiscountAmount(new BigDecimal("0.00"));
        productOrderVO.setProductPaidAmount(new BigDecimal("0.00"));
        productOrderVO.setProductImg("productImg");
        productOrderVO.setProductDetail("productDetail");
        mallBaseOrderVO.setProductOrderVOS(Arrays.asList(productOrderVO));
        mallBaseOrderVO.setOrderAfterCondition(0);
        mallBaseOrderVO.setOrderProcessJson("orderProcessJson");
        final MallOrderProcessDTO mallOrderProcessDTO = new MallOrderProcessDTO();
        mallOrderProcessDTO.setProcessState(0);
        mallOrderProcessDTO.setProcessTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        mallBaseOrderVO.setMallOrderProcessDTOS(Arrays.asList(mallOrderProcessDTO));
        mallBaseOrderVO.setMemberPhoneNum("memberPhoneNum");
        mallBaseOrderVO.setMemberName("memberName");
        mallBaseOrderVO.setReceiverGuid("receiverGuid");
        mallBaseOrderVO.setReceiverPhone("receiverPhone");
        mallBaseOrderVO.setReceiverName("receiverName");
        mallBaseOrderVO.setReceiverAddress("receiverAddress");
        mallBaseOrderVO.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<MallBaseOrderVO> mallBaseOrderVOS = Arrays.asList(mallBaseOrderVO);
        final QueryMallOrderQO queryMallOrderQO2 = new QueryMallOrderQO();
        queryMallOrderQO2.setCurrentPage(0);
        queryMallOrderQO2.setPageSize(0);
        queryMallOrderQO2.setOperSubjectGuid("operSubjectGuid");
        queryMallOrderQO2.setKeywords("keywords");
        queryMallOrderQO2.setOrderNumber("orderNumber");
        when(mockHsaMallBaseOrderMapper.findMallBaseOrderPage(queryMallOrderQO2)).thenReturn(mallBaseOrderVOS);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        when(mockHsaOrderReceiverAddressMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());
        when(mockFileOssService.upload(any(FileDto.class))).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.mallBaseOrderExport(queryMallOrderQO);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testCheckOrder() {
        // Setup
        final AppletMallOrderQO request = new AppletMallOrderQO();
        request.setCommodity_list(Arrays.asList(0L));
        request.setMemberGuid("memberGuid");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        request.setProductAmountQOS(Arrays.asList(orderProductAmountQO));

        final AppletMallOrderVO expectedResult = new AppletMallOrderVO();
        expectedResult.setMemberGuid("memberGuid");
        expectedResult.setIsCheck(0);
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        expectedResult.setProductAmountQOS(Arrays.asList(orderProductAmountQO1));

        // Configure MemberCommodityFeign.getCommodityDetails(...).
        final CommodityDetailsVO commodityDetailsVO = new CommodityDetailsVO();
        commodityDetailsVO.setS_id(0);
        commodityDetailsVO.setBase_price("base_price");
        commodityDetailsVO.setStore_state("store_state");
        final SelectDataVO selectDataVO = new SelectDataVO();
        final ComDataVO comDataVO = new ComDataVO();
        comDataVO.setCommodity_info_id(0L);
        comDataVO.setConstitute_price(new BigDecimal("0.00"));
        selectDataVO.setCom_data(Arrays.asList(comDataVO));
        commodityDetailsVO.setSelect_data(Arrays.asList(selectDataVO));
        final CommodityDetailsQO commodityDetailsQO = new CommodityDetailsQO();
        commodityDetailsQO.setCommodity_id(0);
        commodityDetailsQO.setCommodityCode("commodityCode");
        commodityDetailsQO.setHttpWithoutRpc(0);
        commodityDetailsQO.setStoreId(0L);
        commodityDetailsQO.setCommodity_list(Arrays.asList(0L));
        when(mockMemberCommodityFeign.getCommodityDetails(commodityDetailsQO)).thenReturn(commodityDetailsVO);

        // Run the test
        final AppletMallOrderVO result = hsaMallBaseOrderServiceImplUnderTest.checkOrder(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testSavePayMallOrder() {
        // Setup
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhone");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        savePayOrderQO.setDeliveryMethod(0);
        savePayOrderQO.setFreightAmount(new BigDecimal("0.00"));
        savePayOrderQO.setReceiverGuid("orderReceiverGuid");
        savePayOrderQO.setPayMethod(0);
        savePayOrderQO.setRemark("remark");
        savePayOrderQO.setProvince("province");
        savePayOrderQO.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        savePayOrderQO.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        savePayOrderQO.setOrderPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderDiscountAmount(new BigDecimal("0.00"));
        savePayOrderQO.setIntegralDeductAmount(new BigDecimal("0.00"));
        savePayOrderQO.setStoreGuid("store_id");
        savePayOrderQO.setStoreName("store_name");
        savePayOrderQO.setDiscountDynamics(new BigDecimal("0.00"));
        savePayOrderQO.setDiscountType(0);
        savePayOrderQO.setGradeName("gradeName");
        savePayOrderQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        savePayOrderQO.setIntegralDeduct(0);

        final PayOrderVO expectedResult = new PayOrderVO();
        expectedResult.setMallOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        expectedResult.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        expectedResult.setOrderNumber("content");
        expectedResult.setIsCheck(0);
        expectedResult.setCheckType(0);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setId(0);
        hsaShoppingCartCommodity.setGuid("59bda453-5402-40bf-b3a6-f98935b523bc");
        hsaShoppingCartCommodity.setBasePrice("basePrice");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        // Configure LogisticsCacheSupport.getOrderFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockLogisticsCacheSupport.getOrderFreightAmount(orderInfo)).thenReturn(mallOrderFreightAmountVO);

        // Configure HsaOrderReceiverAddressService.getByGuid(...).
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("orderReceiverGuid");
        orderReceiverAddressVO.setMemberGuid("memberGuid");
        orderReceiverAddressVO.setMemberAccount("memberAccount");
        orderReceiverAddressVO.setReceiverName("receiverName");
        orderReceiverAddressVO.setCode("code");
        when(mockHsaOrderReceiverAddressService.getByGuid("orderReceiverGuid")).thenReturn(orderReceiverAddressVO);

        // Configure HsaMallOrderTimeRuleService.getMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("75b97789-c843-40c9-b62b-5f9e6b10b4a4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.getMallOrderTimeRule()).thenReturn(mallOrderTimeRuleVO);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("16d75588-01ae-49a1-a1ef-1734cd75a794");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberBaseFeign.saveIntegralDeductDetail(...).
        final OrderIntegralDeductDetailQO deductDetailVO = new OrderIntegralDeductDetailQO();
        deductDetailVO.setCommodityTotalAmount(new BigDecimal("0.00"));
        deductDetailVO.setDiscountDynamicsAmount(new BigDecimal("0.00"));
        deductDetailVO.setOperSubjectGuid("operSubjectGuid");
        deductDetailVO.setDiscountType(0);
        deductDetailVO.setMemberInfoGuid("memberInfoGuid");
        deductDetailVO.setOrderNumber("content");
        deductDetailVO.setIntegralDeduct(0);
        deductDetailVO.setStoreName("store_name");
        deductDetailVO.setStoreGuid("store_id");
        when(mockBaseFeign.saveIntegralDeductDetail(deductDetailVO)).thenReturn(Result.success(false));

        // Run the test
        final PayOrderVO result = hsaMallBaseOrderServiceImplUnderTest.savePayMallOrder(savePayOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "content", 30);

        // Confirm HsaMallBaseOrderMapper.insert(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).insert(t);

        // Confirm HsaProductOrderDetailService.saveBatch(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> entityList = Arrays.asList(hsaProductOrderDetail);
        verify(mockProductOrderDetailService).saveBatch(entityList);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail1 = new HsaProductOrderDetail();
        hsaProductOrderDetail1.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail1.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail1.setProductId(0L);
        hsaProductOrderDetail1.setProductName("product_name");
        hsaProductOrderDetail1.setProductNumber("productCode");
        hsaProductOrderDetail1.setProductDetail("productDetail");
        hsaProductOrderDetail1.setProductUnitPrice("price");
        hsaProductOrderDetail1.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductNum(0);
        hsaProductOrderDetail1.setProductImg("productImg");
        hsaProductOrderDetail1.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail1);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);
    }

    @Test
    public void testSavePayMallOrder_IHsaShoppingCartCommodityServiceReturnsNoItems() {
        // Setup
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhone");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        savePayOrderQO.setDeliveryMethod(0);
        savePayOrderQO.setFreightAmount(new BigDecimal("0.00"));
        savePayOrderQO.setReceiverGuid("orderReceiverGuid");
        savePayOrderQO.setPayMethod(0);
        savePayOrderQO.setRemark("remark");
        savePayOrderQO.setProvince("province");
        savePayOrderQO.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        savePayOrderQO.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        savePayOrderQO.setOrderPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderDiscountAmount(new BigDecimal("0.00"));
        savePayOrderQO.setIntegralDeductAmount(new BigDecimal("0.00"));
        savePayOrderQO.setStoreGuid("store_id");
        savePayOrderQO.setStoreName("store_name");
        savePayOrderQO.setDiscountDynamics(new BigDecimal("0.00"));
        savePayOrderQO.setDiscountType(0);
        savePayOrderQO.setGradeName("gradeName");
        savePayOrderQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        savePayOrderQO.setIntegralDeduct(0);

        final PayOrderVO expectedResult = new PayOrderVO();
        expectedResult.setMallOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        expectedResult.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        expectedResult.setOrderNumber("content");
        expectedResult.setIsCheck(0);
        expectedResult.setCheckType(0);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Configure LogisticsCacheSupport.getOrderFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockLogisticsCacheSupport.getOrderFreightAmount(orderInfo)).thenReturn(mallOrderFreightAmountVO);

        // Configure HsaOrderReceiverAddressService.getByGuid(...).
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("orderReceiverGuid");
        orderReceiverAddressVO.setMemberGuid("memberGuid");
        orderReceiverAddressVO.setMemberAccount("memberAccount");
        orderReceiverAddressVO.setReceiverName("receiverName");
        orderReceiverAddressVO.setCode("code");
        when(mockHsaOrderReceiverAddressService.getByGuid("orderReceiverGuid")).thenReturn(orderReceiverAddressVO);

        // Configure HsaMallOrderTimeRuleService.getMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("75b97789-c843-40c9-b62b-5f9e6b10b4a4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.getMallOrderTimeRule()).thenReturn(mallOrderTimeRuleVO);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("16d75588-01ae-49a1-a1ef-1734cd75a794");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberBaseFeign.saveIntegralDeductDetail(...).
        final OrderIntegralDeductDetailQO deductDetailVO = new OrderIntegralDeductDetailQO();
        deductDetailVO.setCommodityTotalAmount(new BigDecimal("0.00"));
        deductDetailVO.setDiscountDynamicsAmount(new BigDecimal("0.00"));
        deductDetailVO.setOperSubjectGuid("operSubjectGuid");
        deductDetailVO.setDiscountType(0);
        deductDetailVO.setMemberInfoGuid("memberInfoGuid");
        deductDetailVO.setOrderNumber("content");
        deductDetailVO.setIntegralDeduct(0);
        deductDetailVO.setStoreName("store_name");
        deductDetailVO.setStoreGuid("store_id");
        when(mockBaseFeign.saveIntegralDeductDetail(deductDetailVO)).thenReturn(Result.success(false));

        // Run the test
        final PayOrderVO result = hsaMallBaseOrderServiceImplUnderTest.savePayMallOrder(savePayOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "content", 30);

        // Confirm HsaMallBaseOrderMapper.insert(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).insert(t);

        // Confirm HsaProductOrderDetailService.saveBatch(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> entityList = Arrays.asList(hsaProductOrderDetail);
        verify(mockProductOrderDetailService).saveBatch(entityList);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail1 = new HsaProductOrderDetail();
        hsaProductOrderDetail1.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail1.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail1.setProductId(0L);
        hsaProductOrderDetail1.setProductName("product_name");
        hsaProductOrderDetail1.setProductNumber("productCode");
        hsaProductOrderDetail1.setProductDetail("productDetail");
        hsaProductOrderDetail1.setProductUnitPrice("price");
        hsaProductOrderDetail1.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductNum(0);
        hsaProductOrderDetail1.setProductImg("productImg");
        hsaProductOrderDetail1.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail1);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);
    }

    @Test
    public void testSavePayMallOrder_MemberBaseFeignReturnsNoItem() {
        // Setup
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhone");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        savePayOrderQO.setDeliveryMethod(0);
        savePayOrderQO.setFreightAmount(new BigDecimal("0.00"));
        savePayOrderQO.setReceiverGuid("orderReceiverGuid");
        savePayOrderQO.setPayMethod(0);
        savePayOrderQO.setRemark("remark");
        savePayOrderQO.setProvince("province");
        savePayOrderQO.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        savePayOrderQO.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        savePayOrderQO.setOrderPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderDiscountAmount(new BigDecimal("0.00"));
        savePayOrderQO.setIntegralDeductAmount(new BigDecimal("0.00"));
        savePayOrderQO.setStoreGuid("store_id");
        savePayOrderQO.setStoreName("store_name");
        savePayOrderQO.setDiscountDynamics(new BigDecimal("0.00"));
        savePayOrderQO.setDiscountType(0);
        savePayOrderQO.setGradeName("gradeName");
        savePayOrderQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        savePayOrderQO.setIntegralDeduct(0);

        final PayOrderVO expectedResult = new PayOrderVO();
        expectedResult.setMallOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        expectedResult.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        expectedResult.setOrderNumber("content");
        expectedResult.setIsCheck(0);
        expectedResult.setCheckType(0);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setId(0);
        hsaShoppingCartCommodity.setGuid("59bda453-5402-40bf-b3a6-f98935b523bc");
        hsaShoppingCartCommodity.setBasePrice("basePrice");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        // Configure LogisticsCacheSupport.getOrderFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockLogisticsCacheSupport.getOrderFreightAmount(orderInfo)).thenReturn(mallOrderFreightAmountVO);

        // Configure HsaOrderReceiverAddressService.getByGuid(...).
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("orderReceiverGuid");
        orderReceiverAddressVO.setMemberGuid("memberGuid");
        orderReceiverAddressVO.setMemberAccount("memberAccount");
        orderReceiverAddressVO.setReceiverName("receiverName");
        orderReceiverAddressVO.setCode("code");
        when(mockHsaOrderReceiverAddressService.getByGuid("orderReceiverGuid")).thenReturn(orderReceiverAddressVO);

        // Configure HsaMallOrderTimeRuleService.getMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("75b97789-c843-40c9-b62b-5f9e6b10b4a4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.getMallOrderTimeRule()).thenReturn(mallOrderTimeRuleVO);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("16d75588-01ae-49a1-a1ef-1734cd75a794");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberBaseFeign.saveIntegralDeductDetail(...).
        final OrderIntegralDeductDetailQO deductDetailVO = new OrderIntegralDeductDetailQO();
        deductDetailVO.setCommodityTotalAmount(new BigDecimal("0.00"));
        deductDetailVO.setDiscountDynamicsAmount(new BigDecimal("0.00"));
        deductDetailVO.setOperSubjectGuid("operSubjectGuid");
        deductDetailVO.setDiscountType(0);
        deductDetailVO.setMemberInfoGuid("memberInfoGuid");
        deductDetailVO.setOrderNumber("content");
        deductDetailVO.setIntegralDeduct(0);
        deductDetailVO.setStoreName("store_name");
        deductDetailVO.setStoreGuid("store_id");
        when(mockBaseFeign.saveIntegralDeductDetail(deductDetailVO)).thenReturn(Result.success());

        // Run the test
        final PayOrderVO result = hsaMallBaseOrderServiceImplUnderTest.savePayMallOrder(savePayOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "content", 30);

        // Confirm HsaMallBaseOrderMapper.insert(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).insert(t);

        // Confirm HsaProductOrderDetailService.saveBatch(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> entityList = Arrays.asList(hsaProductOrderDetail);
        verify(mockProductOrderDetailService).saveBatch(entityList);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail1 = new HsaProductOrderDetail();
        hsaProductOrderDetail1.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail1.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail1.setProductId(0L);
        hsaProductOrderDetail1.setProductName("product_name");
        hsaProductOrderDetail1.setProductNumber("productCode");
        hsaProductOrderDetail1.setProductDetail("productDetail");
        hsaProductOrderDetail1.setProductUnitPrice("price");
        hsaProductOrderDetail1.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductNum(0);
        hsaProductOrderDetail1.setProductImg("productImg");
        hsaProductOrderDetail1.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail1);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);
    }

    @Test
    public void testSavePayMallOrder_MemberBaseFeignReturnsError() {
        // Setup
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhone");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        savePayOrderQO.setDeliveryMethod(0);
        savePayOrderQO.setFreightAmount(new BigDecimal("0.00"));
        savePayOrderQO.setReceiverGuid("orderReceiverGuid");
        savePayOrderQO.setPayMethod(0);
        savePayOrderQO.setRemark("remark");
        savePayOrderQO.setProvince("province");
        savePayOrderQO.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        savePayOrderQO.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        savePayOrderQO.setOrderPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderDiscountAmount(new BigDecimal("0.00"));
        savePayOrderQO.setIntegralDeductAmount(new BigDecimal("0.00"));
        savePayOrderQO.setStoreGuid("store_id");
        savePayOrderQO.setStoreName("store_name");
        savePayOrderQO.setDiscountDynamics(new BigDecimal("0.00"));
        savePayOrderQO.setDiscountType(0);
        savePayOrderQO.setGradeName("gradeName");
        savePayOrderQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        savePayOrderQO.setIntegralDeduct(0);

        final PayOrderVO expectedResult = new PayOrderVO();
        expectedResult.setMallOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        expectedResult.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        expectedResult.setOrderNumber("content");
        expectedResult.setIsCheck(0);
        expectedResult.setCheckType(0);

        when(mockExternalSupport.itemServer(0)).thenReturn(null);

        // Configure IHsaShoppingCartCommodityService.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setId(0);
        hsaShoppingCartCommodity.setGuid("59bda453-5402-40bf-b3a6-f98935b523bc");
        hsaShoppingCartCommodity.setBasePrice("basePrice");
        hsaShoppingCartCommodity.setGroupType(0);
        hsaShoppingCartCommodity.setSku("sku");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity);
        when(mockShoppingCartCommodityService.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        // Configure LogisticsCacheSupport.getOrderFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockLogisticsCacheSupport.getOrderFreightAmount(orderInfo)).thenReturn(mallOrderFreightAmountVO);

        // Configure HsaOrderReceiverAddressService.getByGuid(...).
        final OrderReceiverAddressVO orderReceiverAddressVO = new OrderReceiverAddressVO();
        orderReceiverAddressVO.setGuid("orderReceiverGuid");
        orderReceiverAddressVO.setMemberGuid("memberGuid");
        orderReceiverAddressVO.setMemberAccount("memberAccount");
        orderReceiverAddressVO.setReceiverName("receiverName");
        orderReceiverAddressVO.setCode("code");
        when(mockHsaOrderReceiverAddressService.getByGuid("orderReceiverGuid")).thenReturn(orderReceiverAddressVO);

        // Configure HsaMallOrderTimeRuleService.getMallOrderTimeRule(...).
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("75b97789-c843-40c9-b62b-5f9e6b10b4a4");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);
        when(mockHsaMallOrderTimeRuleService.getMallOrderTimeRule()).thenReturn(mallOrderTimeRuleVO);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("16d75588-01ae-49a1-a1ef-1734cd75a794");
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure MemberBaseFeign.saveIntegralDeductDetail(...).
        final OrderIntegralDeductDetailQO deductDetailVO = new OrderIntegralDeductDetailQO();
        deductDetailVO.setCommodityTotalAmount(new BigDecimal("0.00"));
        deductDetailVO.setDiscountDynamicsAmount(new BigDecimal("0.00"));
        deductDetailVO.setOperSubjectGuid("operSubjectGuid");
        deductDetailVO.setDiscountType(0);
        deductDetailVO.setMemberInfoGuid("memberInfoGuid");
        deductDetailVO.setOrderNumber("content");
        deductDetailVO.setIntegralDeduct(0);
        deductDetailVO.setStoreName("store_name");
        deductDetailVO.setStoreGuid("store_id");
        when(mockBaseFeign.saveIntegralDeductDetail(deductDetailVO)).thenReturn(Result.error(""));

        // Run the test
        final PayOrderVO result = hsaMallBaseOrderServiceImplUnderTest.savePayMallOrder(savePayOrderQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockCacheService).setMallOrderCache("mallOrderKey", "content", 30);

        // Confirm HsaMallBaseOrderMapper.insert(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).insert(t);

        // Confirm HsaProductOrderDetailService.saveBatch(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> entityList = Arrays.asList(hsaProductOrderDetail);
        verify(mockProductOrderDetailService).saveBatch(entityList);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail1 = new HsaProductOrderDetail();
        hsaProductOrderDetail1.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail1.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail1.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail1.setProductId(0L);
        hsaProductOrderDetail1.setProductName("product_name");
        hsaProductOrderDetail1.setProductNumber("productCode");
        hsaProductOrderDetail1.setProductDetail("productDetail");
        hsaProductOrderDetail1.setProductUnitPrice("price");
        hsaProductOrderDetail1.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail1.setProductNum(0);
        hsaProductOrderDetail1.setProductImg("productImg");
        hsaProductOrderDetail1.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail1);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);
    }

    @Test
    public void testGetSingleFreightAmount() {
        // Setup
        final GetFreightAmount getFreightAmount = new GetFreightAmount();
        getFreightAmount.setProvince("province");
        getFreightAmount.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        getFreightAmount.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));

        when(mockLogisticsCacheSupport.getFreightAmount("productCode", 0, "province", "city"))
                .thenReturn(new BigDecimal("0.00"));

        // Run the test
        final BigDecimal result = hsaMallBaseOrderServiceImplUnderTest.getSingleFreightAmount(getFreightAmount);

        // Verify the results
        assertThat(result).isEqualTo(new BigDecimal("0.00"));
    }

    @Test
    public void testGetFreightAmount() {
        // Setup
        final GetFreightAmount getFreightAmount = new GetFreightAmount();
        getFreightAmount.setProvince("province");
        getFreightAmount.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        getFreightAmount.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));

        final MallOrderFreightAmountVO expectedResult = new MallOrderFreightAmountVO();
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setUnCommodityCodes(Arrays.asList("value"));

        // Configure LogisticsCacheSupport.getOrderFreightAmount(...).
        final MallOrderFreightAmountVO mallOrderFreightAmountVO = new MallOrderFreightAmountVO();
        mallOrderFreightAmountVO.setFreightAmount(new BigDecimal("0.00"));
        mallOrderFreightAmountVO.setUnCommodityCodes(Arrays.asList("value"));
        final GetFreightAmount orderInfo = new GetFreightAmount();
        orderInfo.setProvince("province");
        orderInfo.setCity("city");
        final OrderProductAmountQO orderProductAmountQO1 = new OrderProductAmountQO();
        orderProductAmountQO1.setProductId(0L);
        orderProductAmountQO1.setProductName("product_name");
        orderProductAmountQO1.setProductCode("productCode");
        orderProductAmountQO1.setProductDetails("productDetail");
        orderProductAmountQO1.setProductNum(0);
        orderProductAmountQO1.setProductUnitPrice("price");
        orderProductAmountQO1.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO1.setProductImg(Arrays.asList("value"));
        orderProductAmountQO1.setShoppingCartGuid("shoppingCartGuid");
        orderInfo.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO1));
        when(mockLogisticsCacheSupport.getOrderFreightAmount(orderInfo)).thenReturn(mallOrderFreightAmountVO);

        // Run the test
        final MallOrderFreightAmountVO result = hsaMallBaseOrderServiceImplUnderTest.getFreightAmount(getFreightAmount);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPayMallOrder() {
        // Setup
        final PayOrderQO payOrderQO = new PayOrderQO();
        payOrderQO.setGuid("guid");
        payOrderQO.setOpenId("openId");
        payOrderQO.setAppId("appId");
        final RequestPayInfoDTO requestPayInfoDTO = new RequestPayInfoDTO();
        requestPayInfoDTO.setPayWay(0);
        payOrderQO.setRequestPayInfoList(Arrays.asList(requestPayInfoDTO));

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("guid")).thenReturn(hsaMallBaseOrder);

        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");
        when(mockTradeServiceFactory.build(PayWayEnum.CASH_PAY)).thenReturn(null);

        // Run the test
        final PayOrderVO result = hsaMallBaseOrderServiceImplUnderTest.payMallOrder(payOrderQO);

        // Verify the results
        assertThat(result).isNull();

        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
        verify(mockPushOrderEvent).sendChain("16d75588-01ae-49a1-a1ef-1734cd75a794", 0);
    }

    @Test
    public void testGetMember() {
        // Setup
        final SavePayOrderQO savePayOrderQO = new SavePayOrderQO();
        savePayOrderQO.setMemberInfoGuid("memberInfoGuid");
        savePayOrderQO.setMemberPhoneNum("memberPhone");
        savePayOrderQO.setMemberAccount("memberAccount");
        savePayOrderQO.setMemberName("memberName");
        savePayOrderQO.setDeliveryMethod(0);
        savePayOrderQO.setFreightAmount(new BigDecimal("0.00"));
        savePayOrderQO.setReceiverGuid("orderReceiverGuid");
        savePayOrderQO.setPayMethod(0);
        savePayOrderQO.setRemark("remark");
        savePayOrderQO.setProvince("province");
        savePayOrderQO.setCity("city");
        final OrderProductAmountQO orderProductAmountQO = new OrderProductAmountQO();
        orderProductAmountQO.setProductId(0L);
        orderProductAmountQO.setProductName("product_name");
        orderProductAmountQO.setProductCode("productCode");
        orderProductAmountQO.setProductDetails("productDetail");
        orderProductAmountQO.setProductNum(0);
        orderProductAmountQO.setProductUnitPrice("price");
        orderProductAmountQO.setProductDiscountAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductPaidAmount(new BigDecimal("0.00"));
        orderProductAmountQO.setProductImg(Arrays.asList("value"));
        orderProductAmountQO.setShoppingCartGuid("shoppingCartGuid");
        savePayOrderQO.setOrderProductAmountQOS(Arrays.asList(orderProductAmountQO));
        savePayOrderQO.setOrderPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderRealPaymentAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        savePayOrderQO.setOrderDiscountAmount(new BigDecimal("0.00"));
        savePayOrderQO.setIntegralDeductAmount(new BigDecimal("0.00"));
        savePayOrderQO.setStoreGuid("store_id");
        savePayOrderQO.setStoreName("store_name");
        savePayOrderQO.setDiscountDynamics(new BigDecimal("0.00"));
        savePayOrderQO.setDiscountType(0);
        savePayOrderQO.setGradeName("gradeName");
        savePayOrderQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        savePayOrderQO.setIntegralDeduct(0);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.getMember(savePayOrderQO);

        // Verify the results
        assertThat(result).isEqualTo("memberName");
    }

    @Test
    public void testOrderShipDea() {
        // Setup
        final OrderShipQO orderShipQO = new OrderShipQO();
        orderShipQO.setOrderGuid("orderGuid");
        orderShipQO.setDistributionGuid("distributionGuid");
        orderShipQO.setLogistic("courier_company");
        orderShipQO.setLogisticNum("courierNumber");

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("orderGuid")).thenReturn(hsaMallBaseOrder);

        // Run the test
        final boolean result = hsaMallBaseOrderServiceImplUnderTest.orderShipDea(orderShipQO);

        // Verify the results
        assertThat(result).isTrue();

        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
    }

    @Test
    public void testMemberSynOrder() {
        // Setup
        final MemberSynOrderQO memberSynOrderQO = new MemberSynOrderQO();
        memberSynOrderQO.setMemberInfoGuid("memberInfoGuid");
        memberSynOrderQO.setMemberPhoneNum("memberPhone");
        memberSynOrderQO.setMemberName("memberName");

        // Configure HsaMallBaseOrderMapper.selectList(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        final List<HsaMallBaseOrder> hsaMallBaseOrders = Arrays.asList(hsaMallBaseOrder);
        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrders);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.memberSynOrder(memberSynOrderQO);

        // Verify the results
        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
    }

    @Test
    public void testMemberSynOrder_HsaMallBaseOrderMapperSelectListReturnsNoItems() {
        // Setup
        final MemberSynOrderQO memberSynOrderQO = new MemberSynOrderQO();
        memberSynOrderQO.setMemberInfoGuid("memberInfoGuid");
        memberSynOrderQO.setMemberPhoneNum("memberPhone");
        memberSynOrderQO.setMemberName("memberName");

        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.memberSynOrder(memberSynOrderQO);

        // Verify the results
        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
    }

    @Test
    public void testUpdateOrderCondition() {
        // Setup
        // Configure HsaMallBaseOrderMapper.selectList(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        final List<HsaMallBaseOrder> hsaMallBaseOrders = Arrays.asList(hsaMallBaseOrder);
        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrders);

        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updateOrderCondition();

        // Verify the results
        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
    }

    @Test
    public void testUpdateOrderCondition_HsaMallBaseOrderMapperSelectListReturnsNoItems() {
        // Setup
        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updateOrderCondition();

        // Verify the results
        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
    }

    @Test
    public void testUpdatePayStateByEvent() {
        // Setup
        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updatePayStateByEvent("content");

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updatePayStateByGuid("orderGuid", 0);
    }

    @Test
    public void testUpdatePayStateByGuid() {
        // Setup
        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updatePayStateByGuid("orderGuid", 0);

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updatePayStateByGuid("orderGuid", 0);
    }

    @Test
    public void testQueryPayStateByGuid() {
        // Setup
        when(mockHsaMallBaseOrderMapper.queryPayStateByGuid("orderGuid")).thenReturn(0);

        // Run the test
        final Integer result = hsaMallBaseOrderServiceImplUnderTest.queryPayStateByGuid("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testOrderDelivery1() {
        // Setup
        final MallOrderDeliveryQO request = new MallOrderDeliveryQO();
        request.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        request.setDeliveryType(0);
        request.setDistributionGuid("distributionGuid");
        request.setLogisticsType(0);
        request.setLogisticsName("courier_company");
        request.setLogisticsNumber("courierNumber");

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("16d75588-01ae-49a1-a1ef-1734cd75a794"))
                .thenReturn(hsaMallBaseOrder);

        // Configure HsaDistributionSetMapper.queryByGuid(...).
        final HsaDistributionSet hsaDistributionSet = new HsaDistributionSet();
        hsaDistributionSet.setId(0L);
        hsaDistributionSet.setLinkman("linkman");
        hsaDistributionSet.setLinkmanPhone("linkmanPhone");
        hsaDistributionSet.setAreaJson("areaJson");
        hsaDistributionSet.setDetailAddress("detailAddress");
        when(mockHsaDistributionSetMapper.queryByGuid("distributionGuid")).thenReturn(hsaDistributionSet);

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("orderReceiverGuid")).thenReturn(hsaOrderReceiverAddress);

        when(mockLogisticsServiceFactory.build(ExpressTypeEnum.YTO)).thenReturn(null);

        // Run the test
        final boolean result = hsaMallBaseOrderServiceImplUnderTest.orderDelivery(request);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");
    }

    @Test
    public void testUpdateOrderStatusByGuid() {
        // Setup
        final OrderStatusDTO dto = new OrderStatusDTO();
        dto.setGuid("orderGuid");
        dto.setOrderCondition(0);
        dto.setPayCondition(0);
        dto.setOrderProcessJson("orderProcessJson");
        dto.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updateOrderStatusByGuid(dto);

        // Verify the results
        // Confirm HsaMallBaseOrderMapper.updateOrderStatusByGuid(...).
        final OrderStatusDTO dto1 = new OrderStatusDTO();
        dto1.setGuid("orderGuid");
        dto1.setOrderCondition(0);
        dto1.setPayCondition(0);
        dto1.setOrderProcessJson("orderProcessJson");
        dto1.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockHsaMallBaseOrderMapper).updateOrderStatusByGuid(dto1);
    }

    @Test
    public void testQueryOrderStatusByGuid() {
        // Setup
        final OrderStatusDTO expectedResult = new OrderStatusDTO();
        expectedResult.setGuid("orderGuid");
        expectedResult.setOrderCondition(0);
        expectedResult.setPayCondition(0);
        expectedResult.setOrderProcessJson("orderProcessJson");
        expectedResult.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure HsaMallBaseOrderMapper.queryOrderStatusByGuid(...).
        final OrderStatusDTO orderStatusDTO = new OrderStatusDTO();
        orderStatusDTO.setGuid("orderGuid");
        orderStatusDTO.setOrderCondition(0);
        orderStatusDTO.setPayCondition(0);
        orderStatusDTO.setOrderProcessJson("orderProcessJson");
        orderStatusDTO.setPaymentTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallBaseOrderMapper.queryOrderStatusByGuid("orderGuid")).thenReturn(orderStatusDTO);

        // Run the test
        final OrderStatusDTO result = hsaMallBaseOrderServiceImplUnderTest.queryOrderStatusByGuid("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testOrderCancel() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.orderCancel("orderGuid", "reason", 0);

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updateOrderCancel("16d75588-01ae-49a1-a1ef-1734cd75a794", "reason", 0);

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);

        // Confirm MemberBaseFeign.accumulationDiscountRelease(...).
        final AccumulationReleaseKeyQO accumulationReleaseKeyQO = new AccumulationReleaseKeyQO();
        accumulationReleaseKeyQO.setOperSubjectGuid("operSubjectGuid");
        accumulationReleaseKeyQO.setMemberInfoGuid("memberInfoGuid");
        accumulationReleaseKeyQO.setOrderNum("content");
        accumulationReleaseKeyQO.setConsumptionGuid("consumptionGuid");
        verify(mockBaseFeign).accumulationDiscountRelease(accumulationReleaseKeyQO);
    }

    @Test
    public void testOrderBusinessRefund() {
        // Setup
        final OrderRefundQO request = new OrderRefundQO();
        request.setReason("reason");
        request.setRefundFee(new BigDecimal("0.00"));
        request.setSuccessCode("successCode");
        request.setRefundType(0);
        request.setActualRefundFee(new BigDecimal("0.00"));

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockTradeServiceFactory.build(PayWayEnum.CASH_PAY)).thenReturn(null);

        // Configure HsaAfterSaleOrderService.refundOrderSave(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("content");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("reason");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.refundOrderSave(afterSaleOrderDTO, false)).thenReturn("result");

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.orderBusinessRefund(request);

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updateOrderCancel("16d75588-01ae-49a1-a1ef-1734cd75a794", "reason", 0);

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);

        // Confirm MemberBaseFeign.accumulationDiscountRelease(...).
        final AccumulationReleaseKeyQO accumulationReleaseKeyQO = new AccumulationReleaseKeyQO();
        accumulationReleaseKeyQO.setOperSubjectGuid("operSubjectGuid");
        accumulationReleaseKeyQO.setMemberInfoGuid("memberInfoGuid");
        accumulationReleaseKeyQO.setOrderNum("content");
        accumulationReleaseKeyQO.setConsumptionGuid("consumptionGuid");
        verify(mockBaseFeign).accumulationDiscountRelease(accumulationReleaseKeyQO);
        verify(mockPushOrderEvent).sendChain("16d75588-01ae-49a1-a1ef-1734cd75a794", 0);
        verify(mockPublisher).publish(EventEnum.NEGOTIATION, "content");
    }

    @Test
    public void testOrderRefund() {
        // Setup
        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setOrderGuid("orderGuid");
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setReason("reason");
        orderRefundDTO.setRefundType(0);
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setUpdateAfterOrder(false);

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockTradeServiceFactory.build(PayWayEnum.CASH_PAY)).thenReturn(null);

        // Configure HsaAfterSaleOrderService.refundOrderSave(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("content");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("reason");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.refundOrderSave(afterSaleOrderDTO, false)).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.orderRefund(orderRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockHsaMallBaseOrderMapper).updateOrderCancel("16d75588-01ae-49a1-a1ef-1734cd75a794", "reason", 0);

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);

        // Confirm MemberBaseFeign.accumulationDiscountRelease(...).
        final AccumulationReleaseKeyQO accumulationReleaseKeyQO = new AccumulationReleaseKeyQO();
        accumulationReleaseKeyQO.setOperSubjectGuid("operSubjectGuid");
        accumulationReleaseKeyQO.setMemberInfoGuid("memberInfoGuid");
        accumulationReleaseKeyQO.setOrderNum("content");
        accumulationReleaseKeyQO.setConsumptionGuid("consumptionGuid");
        verify(mockBaseFeign).accumulationDiscountRelease(accumulationReleaseKeyQO);
        verify(mockPushOrderEvent).sendChain("16d75588-01ae-49a1-a1ef-1734cd75a794", 0);
    }

    @Test
    public void testGetAbleRefundOrder() {
        // Setup
        final MallBaseOrderDTO expectedResult = new MallBaseOrderDTO();
        expectedResult.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setOrderNumber("content");
        expectedResult.setOrderCondition(0);
        expectedResult.setPayMethod(0);
        expectedResult.setOrderPaidAmount(new BigDecimal("0.00"));
        expectedResult.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        final MallBaseOrderDTO result = hsaMallBaseOrderServiceImplUnderTest.getAbleRefundOrder("orderGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testRefundToAfterOrder() {
        // Setup
        final MallBaseOrderDTO mallBaseOrderDTO = new MallBaseOrderDTO();
        mallBaseOrderDTO.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        mallBaseOrderDTO.setEnterpriseGuid("enterpriseGuid");
        mallBaseOrderDTO.setOperSubjectGuid("operSubjectGuid");
        mallBaseOrderDTO.setMemberInfoGuid("memberInfoGuid");
        mallBaseOrderDTO.setOrderNumber("content");
        mallBaseOrderDTO.setOrderCondition(0);
        mallBaseOrderDTO.setPayMethod(0);
        mallBaseOrderDTO.setOrderPaidAmount(new BigDecimal("0.00"));
        mallBaseOrderDTO.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final MallOrderRefundDTO orderRefundDTO = new MallOrderRefundDTO();
        orderRefundDTO.setOrderGuid("orderGuid");
        orderRefundDTO.setRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setReason("reason");
        orderRefundDTO.setRefundType(0);
        orderRefundDTO.setActualRefundFee(new BigDecimal("0.00"));
        orderRefundDTO.setUpdateAfterOrder(false);

        when(mockRedissonClient.getLock("s")).thenReturn(null);
        when(mockTradeServiceFactory.build(PayWayEnum.CASH_PAY)).thenReturn(null);

        // Configure HsaAfterSaleOrderService.refundOrderSave(...).
        final AfterSaleOrderDTO afterSaleOrderDTO = new AfterSaleOrderDTO();
        afterSaleOrderDTO.setOperSubjectGuid("operSubjectGuid");
        afterSaleOrderDTO.setEnterpriseGuid("enterpriseGuid");
        afterSaleOrderDTO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        afterSaleOrderDTO.setMemberInfoGuid("memberInfoGuid");
        afterSaleOrderDTO.setRefundCondition(0);
        afterSaleOrderDTO.setOrderNumber("content");
        afterSaleOrderDTO.setPayOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        afterSaleOrderDTO.setOrderCondition(0);
        afterSaleOrderDTO.setRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setActualRefundAmount(new BigDecimal("0.00"));
        afterSaleOrderDTO.setCancel("reason");
        afterSaleOrderDTO.setRefundWay("refundWay");
        afterSaleOrderDTO.setRefundType(0);
        afterSaleOrderDTO.setDealType(0);
        when(mockAfterSaleOrderService.refundOrderSave(afterSaleOrderDTO, false)).thenReturn("result");

        // Run the test
        final String result = hsaMallBaseOrderServiceImplUnderTest.refundToAfterOrder(mallBaseOrderDTO, orderRefundDTO);

        // Verify the results
        assertThat(result).isEqualTo("result");
        verify(mockHsaMallBaseOrderMapper).updateOrderCancel("16d75588-01ae-49a1-a1ef-1734cd75a794", "reason", 0);

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);

        // Confirm MemberBaseFeign.accumulationDiscountRelease(...).
        final AccumulationReleaseKeyQO accumulationReleaseKeyQO = new AccumulationReleaseKeyQO();
        accumulationReleaseKeyQO.setOperSubjectGuid("operSubjectGuid");
        accumulationReleaseKeyQO.setMemberInfoGuid("memberInfoGuid");
        accumulationReleaseKeyQO.setOrderNum("content");
        accumulationReleaseKeyQO.setConsumptionGuid("consumptionGuid");
        verify(mockBaseFeign).accumulationDiscountRelease(accumulationReleaseKeyQO);
        verify(mockPushOrderEvent).sendChain("16d75588-01ae-49a1-a1ef-1734cd75a794", 0);
    }

    @Test
    public void testUpdateRefundConditionByEvent() {
        // Setup
        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updateRefundConditionByEvent("content");

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updateRefundCondition(Arrays.asList("value"), 0);
    }

    @Test
    public void testCancel() {
        // Setup
        final BaseOrderCancelQO baseOrderCancelQO = new BaseOrderCancelQO();
        baseOrderCancelQO.setMemberInfoGuid("memberInfoGuid");
        baseOrderCancelQO.setOrderNum("content");
        baseOrderCancelQO.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseOrderCancelQO.setReason("reason");
        baseOrderCancelQO.setCancelType(0);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.cancel(baseOrderCancelQO);

        // Verify the results
        verify(mockHsaMallBaseOrderMapper).updateOrderCancel("16d75588-01ae-49a1-a1ef-1734cd75a794", "reason", 0);

        // Confirm PushOrderEvent.send(...).
        final HsaMallBaseOrder baseNewOrder = new HsaMallBaseOrder();
        baseNewOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        baseNewOrder.setOperSubjectGuid("operSubjectGuid");
        baseNewOrder.setEnterpriseGuid("enterpriseGuid");
        baseNewOrder.setMemberInfoGuid("memberInfoGuid");
        baseNewOrder.setMemberPhone("memberPhone");
        baseNewOrder.setMemberName("memberName");
        baseNewOrder.setOrderNumber("content");
        baseNewOrder.setOrderCondition(0);
        baseNewOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setPayCondition(0);
        baseNewOrder.setDeliveryMethod(0);
        baseNewOrder.setPayMethod(0);
        baseNewOrder.setFreightAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderProcessJson("orderProcessJson");
        baseNewOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        baseNewOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        baseNewOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        baseNewOrder.setStoreGuid("store_id");
        baseNewOrder.setStoreName("store_name");
        baseNewOrder.setReceiverGuid("orderReceiverGuid");
        baseNewOrder.setLogisticsName("courier_company");
        baseNewOrder.setLogisticsNumber("courierNumber");
        baseNewOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setSenderName("linkman");
        baseNewOrder.setSenderAddress("detailAddress");
        baseNewOrder.setSenderPhone("linkmanPhone");
        baseNewOrder.setOrderAfterCondition(0);
        baseNewOrder.setOrderExternalNumber("orderExternalNumber");
        baseNewOrder.setRemark("remark");
        baseNewOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        baseNewOrder.setDiscountDynamics(new BigDecimal("0.00"));
        baseNewOrder.setDiscountType(0);
        baseNewOrder.setGradeName("gradeName");
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> details = Arrays.asList(hsaProductOrderDetail);
        final OrderReceiverAddressVO hsaOrderReceiverAddress = new OrderReceiverAddressVO();
        hsaOrderReceiverAddress.setGuid("orderReceiverGuid");
        hsaOrderReceiverAddress.setMemberGuid("memberGuid");
        hsaOrderReceiverAddress.setMemberAccount("memberAccount");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setCode("code");
        verify(mockPushOrderEvent).send("16d75588-01ae-49a1-a1ef-1734cd75a794", baseNewOrder, details,
                hsaOrderReceiverAddress);

        // Confirm MemberBaseFeign.accumulationDiscountRelease(...).
        final AccumulationReleaseKeyQO accumulationReleaseKeyQO = new AccumulationReleaseKeyQO();
        accumulationReleaseKeyQO.setOperSubjectGuid("operSubjectGuid");
        accumulationReleaseKeyQO.setMemberInfoGuid("memberInfoGuid");
        accumulationReleaseKeyQO.setOrderNum("content");
        accumulationReleaseKeyQO.setConsumptionGuid("consumptionGuid");
        verify(mockBaseFeign).accumulationDiscountRelease(accumulationReleaseKeyQO);
    }

    @Test
    public void testConfirmReceived() {
        // Setup
        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.confirmReceived("16d75588-01ae-49a1-a1ef-1734cd75a794");

        // Verify the results
        verify(mockPushOrderEvent).sendComplete("16d75588-01ae-49a1-a1ef-1734cd75a794");

        // Confirm MemberBaseFeign.payOrderRightsCallback(...).
        final TerOrderCallbackQO terOrderCallbackQO = new TerOrderCallbackQO();
        terOrderCallbackQO.setMemberConsumptionGuid("memberConsumptionGuid");
        terOrderCallbackQO.setOrderNum("content");
        verify(mockMemberBaseFeign).payOrderRightsCallback(terOrderCallbackQO);
    }

    @Test
    public void testCallBackMemberPayRecord() {
        // Setup
        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Configure MemberBaseFeign.payOrder(...).
        final ConsumptionRespVO consumptionRespVO = new ConsumptionRespVO();
        consumptionRespVO.setMemberConsumptionGuid("memberConsumptionGuid");
        consumptionRespVO.setMemberInfoCardGuid("memberInfoCardGuid");
        consumptionRespVO.setStoreName("storeName");
        consumptionRespVO.setMemberPhone("memberPhone");
        consumptionRespVO.setCardPayMoney(new BigDecimal("0.00"));
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.success(consumptionRespVO);
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("content");
        request.setRefundAmount(new BigDecimal("0.00"));
        final RequestOrderInfoDTO requestOrderInfo = new RequestOrderInfoDTO();
        requestOrderInfo.setOrderNumber("content");
        request.setRequestOrderInfo(requestOrderInfo);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.callBackMemberPayRecord("content");

        // Verify the results
    }

    @Test
    public void testCallBackMemberPayRecord_MemberBaseFeignReturnsNoItem() {
        // Setup
        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Configure MemberBaseFeign.payOrder(...).
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("content");
        request.setRefundAmount(new BigDecimal("0.00"));
        final RequestOrderInfoDTO requestOrderInfo = new RequestOrderInfoDTO();
        requestOrderInfo.setOrderNumber("content");
        request.setRequestOrderInfo(requestOrderInfo);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(Result.success());

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.callBackMemberPayRecord("content");

        // Verify the results
    }

    @Test
    public void testCallBackMemberPayRecord_MemberBaseFeignReturnsError() {
        // Setup
        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Configure MemberBaseFeign.payOrder(...).
        final Result<ConsumptionRespVO> consumptionRespVOResult = Result.error("");
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("content");
        request.setRefundAmount(new BigDecimal("0.00"));
        final RequestOrderInfoDTO requestOrderInfo = new RequestOrderInfoDTO();
        requestOrderInfo.setOrderNumber("content");
        request.setRequestOrderInfo(requestOrderInfo);
        when(mockMemberBaseFeign.payOrder(request)).thenReturn(consumptionRespVOResult);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.callBackMemberPayRecord("content");

        // Verify the results
    }

    @Test
    public void testCallBackMemberPayRights() {
        // Setup
        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.callBackMemberPayRights("content");

        // Verify the results
        // Confirm MemberBaseFeign.payOrderRightsCallback(...).
        final TerOrderCallbackQO terOrderCallbackQO = new TerOrderCallbackQO();
        terOrderCallbackQO.setMemberConsumptionGuid("memberConsumptionGuid");
        terOrderCallbackQO.setOrderNum("content");
        verify(mockMemberBaseFeign).payOrderRightsCallback(terOrderCallbackQO);
    }

    @Test
    public void testRollBackMemberPayRights() {
        // Setup
        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.rollBackMemberPayRights("content");

        // Verify the results
        // Confirm MemberBaseFeign.payOrder(...).
        final RequestConfirmPayVO request = new RequestConfirmPayVO();
        request.setRequestConfirmPayVOS(Arrays.asList(new RequestConfirmPayVO()));
        request.setMemberConsumptionGuid("content");
        request.setRefundAmount(new BigDecimal("0.00"));
        final RequestOrderInfoDTO requestOrderInfo = new RequestOrderInfoDTO();
        requestOrderInfo.setOrderNumber("content");
        request.setRequestOrderInfo(requestOrderInfo);
        verify(mockMemberBaseFeign).payOrder(request);
    }

    @Test
    public void testUpdateOrderState() {
        // Setup
        final AppletOrderStateQO approveState = new AppletOrderStateQO();
        approveState.setOrder_name("order_name");
        approveState.setState(0);
        approveState.setCourier_company("courier_company");
        approveState.setCourier_number("courierNumber");

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.updateOrderState(approveState);

        // Verify the results
        verify(mockPublisher).publish(EventEnum.ORDER_DELIVERY, "content");
    }

    @Test
    public void testOrderDelivery2() {
        // Setup
        // Configure HsaMallBaseOrderMapper.selectOne(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrder);

        when(mockRedissonClient.getLock("s")).thenReturn(null);

        // Configure HsaMallBaseOrderMapper.queryByGuid(...).
        final HsaMallBaseOrder hsaMallBaseOrder1 = new HsaMallBaseOrder();
        hsaMallBaseOrder1.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder1.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder1.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder1.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder1.setMemberPhone("memberPhone");
        hsaMallBaseOrder1.setMemberName("memberName");
        hsaMallBaseOrder1.setOrderNumber("content");
        hsaMallBaseOrder1.setOrderCondition(0);
        hsaMallBaseOrder1.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder1.setPayCondition(0);
        hsaMallBaseOrder1.setDeliveryMethod(0);
        hsaMallBaseOrder1.setPayMethod(0);
        hsaMallBaseOrder1.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder1.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setStoreGuid("store_id");
        hsaMallBaseOrder1.setStoreName("store_name");
        hsaMallBaseOrder1.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder1.setLogisticsName("courier_company");
        hsaMallBaseOrder1.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder1.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder1.setSenderName("linkman");
        hsaMallBaseOrder1.setSenderAddress("detailAddress");
        hsaMallBaseOrder1.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder1.setOrderAfterCondition(0);
        hsaMallBaseOrder1.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder1.setRemark("remark");
        hsaMallBaseOrder1.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder1.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder1.setDiscountType(0);
        hsaMallBaseOrder1.setGradeName("gradeName");
        when(mockHsaMallBaseOrderMapper.queryByGuid("16d75588-01ae-49a1-a1ef-1734cd75a794"))
                .thenReturn(hsaMallBaseOrder1);

        // Configure HsaDistributionSetMapper.queryByGuid(...).
        final HsaDistributionSet hsaDistributionSet = new HsaDistributionSet();
        hsaDistributionSet.setId(0L);
        hsaDistributionSet.setLinkman("linkman");
        hsaDistributionSet.setLinkmanPhone("linkmanPhone");
        hsaDistributionSet.setAreaJson("areaJson");
        hsaDistributionSet.setDetailAddress("detailAddress");
        when(mockHsaDistributionSetMapper.queryByGuid("distributionGuid")).thenReturn(hsaDistributionSet);

        // Configure HsaOrderReceiverAddressMapper.queryByGuid(...).
        final HsaOrderReceiverAddress hsaOrderReceiverAddress = new HsaOrderReceiverAddress();
        hsaOrderReceiverAddress.setGuid("9e909692-d25c-4e75-ba2b-6a4a981a087f");
        hsaOrderReceiverAddress.setRegion("region");
        hsaOrderReceiverAddress.setReceiverName("receiverName");
        hsaOrderReceiverAddress.setReceiverPhone("receiverPhone");
        hsaOrderReceiverAddress.setReceiverAddress("receiverAddress");
        hsaOrderReceiverAddress.setProvince("province");
        hsaOrderReceiverAddress.setCity("city");
        hsaOrderReceiverAddress.setArea("area");
        hsaOrderReceiverAddress.setHouseNumber("houseNumber");
        when(mockHsaOrderReceiverAddressMapper.queryByGuid("orderReceiverGuid")).thenReturn(hsaOrderReceiverAddress);

        when(mockLogisticsServiceFactory.build(ExpressTypeEnum.YTO)).thenReturn(null);

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.orderDelivery("content");

        // Verify the results
        verify(mockPushOrderEvent).sendComplete("16d75588-01ae-49a1-a1ef-1734cd75a794");

        // Confirm MemberBaseFeign.payOrderRightsCallback(...).
        final TerOrderCallbackQO terOrderCallbackQO = new TerOrderCallbackQO();
        terOrderCallbackQO.setMemberConsumptionGuid("memberConsumptionGuid");
        terOrderCallbackQO.setOrderNum("content");
        verify(mockMemberBaseFeign).payOrderRightsCallback(terOrderCallbackQO);

        // Confirm HsaMallBaseOrderMapper.updateByGuid(...).
        final HsaMallBaseOrder t = new HsaMallBaseOrder();
        t.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setEnterpriseGuid("enterpriseGuid");
        t.setMemberInfoGuid("memberInfoGuid");
        t.setMemberPhone("memberPhone");
        t.setMemberName("memberName");
        t.setOrderNumber("content");
        t.setOrderCondition(0);
        t.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setPayCondition(0);
        t.setDeliveryMethod(0);
        t.setPayMethod(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        t.setOrderProcessJson("orderProcessJson");
        t.setOrderPaymentAmount(new BigDecimal("0.00"));
        t.setOrderDiscountAmount(new BigDecimal("0.00"));
        t.setIntegralDeductAmount(new BigDecimal("0.00"));
        t.setOrderPreferentialAmount(new BigDecimal("0.00"));
        t.setOrderPaidAmount(new BigDecimal("0.00"));
        t.setStoreGuid("store_id");
        t.setStoreName("store_name");
        t.setReceiverGuid("orderReceiverGuid");
        t.setLogisticsName("courier_company");
        t.setLogisticsNumber("courierNumber");
        t.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setSenderName("linkman");
        t.setSenderAddress("detailAddress");
        t.setSenderPhone("linkmanPhone");
        t.setOrderAfterCondition(0);
        t.setOrderExternalNumber("orderExternalNumber");
        t.setRemark("remark");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setDiscountDynamics(new BigDecimal("0.00"));
        t.setDiscountType(0);
        t.setGradeName("gradeName");
        verify(mockHsaMallBaseOrderMapper).updateByGuid(t);
        verify(mockPublisher).publish(EventEnum.ORDER_AUTO, "content");
    }

    @Test
    public void testOrderGenerateCallbackProcessing() {
        // Setup
        when(mockCacheService.getMallOrderCache("mallOrderKey")).thenReturn("result");

        // Run the test
        hsaMallBaseOrderServiceImplUnderTest.orderGenerateCallbackProcessing("content");

        // Verify the results
        // Confirm MemberBaseFeign.orderGenerateCallbackProcessing(...).
        final OrderGenerateCallbackQO orderGenerateCallbackQO = new OrderGenerateCallbackQO();
        orderGenerateCallbackQO.setMemberGradePriceDetailGuid("memberGradePriceDetailGuid");
        orderGenerateCallbackQO.setOrderNum("content");
        final List<OrderGenerateCallbackQO> qo = Arrays.asList(orderGenerateCallbackQO);
        verify(mockBaseFeign).orderGenerateCallbackProcessing(qo);
    }

    @Test
    public void testAppletOrderQO() {
        // Setup
        final AppletOrderQO request = new AppletOrderQO();
        request.setPage(0);
        request.setPage_size(0);
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMini_program_type("mini_program_type");

        final CrmMallOrderVO crmMallOrderVO = new CrmMallOrderVO();
        crmMallOrderVO.setOrder_guid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        crmMallOrderVO.setOrder_code("content");
        crmMallOrderVO.setStore_id("store_id");
        crmMallOrderVO.setStore_name("store_name");
        crmMallOrderVO.setAmount_paid(new BigDecimal("0.00"));
        crmMallOrderVO.setOrderPaymentAmount(new BigDecimal("0.00"));
        crmMallOrderVO.setOrderDiscountAmount(new BigDecimal("0.00"));
        crmMallOrderVO.setOrder_type("mall_order");
        crmMallOrderVO.setOrder_source("mall_order");
        crmMallOrderVO.setCreate_date(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        crmMallOrderVO.setOrder_state(0);
        crmMallOrderVO.setRefundCondition(0);
        crmMallOrderVO.set_prepayment(false);
        final CrmMallProductVO crmMallProductVO = new CrmMallProductVO();
        crmMallProductVO.setProduct_name("product_name");
        crmMallProductVO.setPrice("price");
        crmMallProductVO.setQuantity(0);
        crmMallProductVO.setProductDetail("productDetail");
        crmMallProductVO.setImage("image");
        crmMallProductVO.setProductDiscountAmount(new BigDecimal("0.00"));
        crmMallProductVO.setProductPaidAmount(new BigDecimal("0.00"));
        crmMallOrderVO.setProducts(Arrays.asList(crmMallProductVO));
        crmMallOrderVO.setDiscountDynamics(new BigDecimal("0.00"));
        crmMallOrderVO.setDiscountType(0);
        crmMallOrderVO.setGradeName("gradeName");
        crmMallOrderVO.setIntegralDeductAmount(new BigDecimal("0.00"));
        crmMallOrderVO.setOrderPreferentialAmount(new BigDecimal("0.00"));
        crmMallOrderVO.setFreightAmount(new BigDecimal("0.00"));
        final List<CrmMallOrderVO> expectedResult = Arrays.asList(crmMallOrderVO);

        // Configure HsaMallBaseOrderMapper.selectList(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        final List<HsaMallBaseOrder> hsaMallBaseOrders = Arrays.asList(hsaMallBaseOrder);
        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrders);

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Run the test
        final List<CrmMallOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.appletOrderQO(request);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAppletOrderQO_HsaMallBaseOrderMapperReturnsNoItems() {
        // Setup
        final AppletOrderQO request = new AppletOrderQO();
        request.setPage(0);
        request.setPage_size(0);
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMini_program_type("mini_program_type");

        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure HsaProductOrderDetailMapper.selectList(...).
        final HsaProductOrderDetail hsaProductOrderDetail = new HsaProductOrderDetail();
        hsaProductOrderDetail.setGuid("1eb729e7-e256-4dc2-a9fb-7f96d7ce611e");
        hsaProductOrderDetail.setOperSubjectGuid("operSubjectGuid");
        hsaProductOrderDetail.setOrderGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaProductOrderDetail.setProductId(0L);
        hsaProductOrderDetail.setProductName("product_name");
        hsaProductOrderDetail.setProductNumber("productCode");
        hsaProductOrderDetail.setProductDetail("productDetail");
        hsaProductOrderDetail.setProductUnitPrice("price");
        hsaProductOrderDetail.setProductDiscountAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductPaidAmount(new BigDecimal("0.00"));
        hsaProductOrderDetail.setProductNum(0);
        hsaProductOrderDetail.setProductImg("productImg");
        hsaProductOrderDetail.setShoppingCartGuid("shoppingCartGuid");
        final List<HsaProductOrderDetail> hsaProductOrderDetails = Arrays.asList(hsaProductOrderDetail);
        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(hsaProductOrderDetails);

        // Run the test
        final List<CrmMallOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.appletOrderQO(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAppletOrderQO_HsaProductOrderDetailMapperReturnsNoItems() {
        // Setup
        final AppletOrderQO request = new AppletOrderQO();
        request.setPage(0);
        request.setPage_size(0);
        request.setMemberInfoGuid("memberInfoGuid");
        request.setMini_program_type("mini_program_type");

        // Configure HsaMallBaseOrderMapper.selectList(...).
        final HsaMallBaseOrder hsaMallBaseOrder = new HsaMallBaseOrder();
        hsaMallBaseOrder.setGuid("16d75588-01ae-49a1-a1ef-1734cd75a794");
        hsaMallBaseOrder.setOperSubjectGuid("operSubjectGuid");
        hsaMallBaseOrder.setEnterpriseGuid("enterpriseGuid");
        hsaMallBaseOrder.setMemberInfoGuid("memberInfoGuid");
        hsaMallBaseOrder.setMemberPhone("memberPhone");
        hsaMallBaseOrder.setMemberName("memberName");
        hsaMallBaseOrder.setOrderNumber("content");
        hsaMallBaseOrder.setOrderCondition(0);
        hsaMallBaseOrder.setCancelTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setPayCondition(0);
        hsaMallBaseOrder.setDeliveryMethod(0);
        hsaMallBaseOrder.setPayMethod(0);
        hsaMallBaseOrder.setFreightAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderProcessJson("orderProcessJson");
        hsaMallBaseOrder.setOrderPaymentAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderDiscountAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setIntegralDeductAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPreferentialAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setOrderPaidAmount(new BigDecimal("0.00"));
        hsaMallBaseOrder.setStoreGuid("store_id");
        hsaMallBaseOrder.setStoreName("store_name");
        hsaMallBaseOrder.setReceiverGuid("orderReceiverGuid");
        hsaMallBaseOrder.setLogisticsName("courier_company");
        hsaMallBaseOrder.setLogisticsNumber("courierNumber");
        hsaMallBaseOrder.setSenderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setSenderName("linkman");
        hsaMallBaseOrder.setSenderAddress("detailAddress");
        hsaMallBaseOrder.setSenderPhone("linkmanPhone");
        hsaMallBaseOrder.setOrderAfterCondition(0);
        hsaMallBaseOrder.setOrderExternalNumber("orderExternalNumber");
        hsaMallBaseOrder.setRemark("remark");
        hsaMallBaseOrder.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallBaseOrder.setDiscountDynamics(new BigDecimal("0.00"));
        hsaMallBaseOrder.setDiscountType(0);
        hsaMallBaseOrder.setGradeName("gradeName");
        final List<HsaMallBaseOrder> hsaMallBaseOrders = Arrays.asList(hsaMallBaseOrder);
        when(mockHsaMallBaseOrderMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaMallBaseOrders);

        when(mockHsaProductOrderDetailMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<CrmMallOrderVO> result = hsaMallBaseOrderServiceImplUnderTest.appletOrderQO(request);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}
