package com.holderzone.member.mall.support;

import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.common.vo.grade.StoreInfoVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StoreSupportTest {

    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private StringRedisTemplate mockRedisTemplate;

    private StoreSupport storeSupportUnderTest;

    @Before
    public void setUp() throws Exception {
        storeSupportUnderTest = new StoreSupport(mockExternalSupport, mockRedisTemplate);
    }

    @Test
    public void testGetStoreInfo() {
        // Setup
        final StoreInfoVO storeInfoVO = new StoreInfoVO();
        storeInfoVO.setId(0L);
        storeInfoVO.setGuid(0L);
        storeInfoVO.setStore_id(0L);
        storeInfoVO.setStore_name("store_name");
        storeInfoVO.setAddress_point("address_point");
        final List<StoreInfoVO> expectedResult = Arrays.asList(storeInfoVO);
        when(mockExternalSupport.storeServer(0)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<StoreInfoVO> result = storeSupportUnderTest.getStoreInfo();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testGetStoreIds() {
        // Setup
        when(mockExternalSupport.storeServer(0)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<Long> result = storeSupportUnderTest.getStoreIds();

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0L));
        verify(mockRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testGetStoreByIds() {
        // Setup
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        final List<StoreDataInfoVO> expectedResult = Arrays.asList(storeDataInfoVO);
        when(mockExternalSupport.storeServer(0)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final List<StoreDataInfoVO> result = storeSupportUnderTest.getStoreByIds(Arrays.asList(0));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }

    @Test
    public void testGetStoreById() {
        // Setup
        final StoreDataInfoVO expectedResult = new StoreDataInfoVO();
        expectedResult.setId(0L);
        expectedResult.setName("name");
        expectedResult.setAddress_point("address_point");
        expectedResult.setAddress("address");
        expectedResult.setLogo("logo");

        when(mockExternalSupport.storeServer(0)).thenReturn(null);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final StoreDataInfoVO result = storeSupportUnderTest.getStoreById("storeId");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisTemplate).expire("key", 8L, TimeUnit.HOURS);
    }
}
