package com.holderzone.member.mall.service.shoppingcart.impl;

import com.holderzone.member.common.dto.mall.shoppingcart.CartAddNumDTO;
import com.holderzone.member.mall.entity.shoppingcart.HsaShoppingCartCommodity;
import com.holderzone.member.mall.mapper.shoppingcart.HsaShoppingCartCommodityMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaShoppingCartCommodityServiceImplTest {

    @Mock
    private HsaShoppingCartCommodityMapper mockShoppingCartCommodityMapper;

    @InjectMocks
    private HsaShoppingCartCommodityServiceImpl hsaShoppingCartCommodityServiceImplUnderTest;

    @Test
    public void testChangeNum() {
        // Setup
        final CartAddNumDTO cartAddNumDTO = new CartAddNumDTO("shoppingCartGuid", 0, 0,
                LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Run the test
        hsaShoppingCartCommodityServiceImplUnderTest.changeNum(cartAddNumDTO);

        // Verify the results
        verify(mockShoppingCartCommodityMapper).changeNum(
                new CartAddNumDTO("shoppingCartGuid", 0, 0, LocalDateTime.of(2020, 1, 1, 0, 0, 0)));
    }

    @Test
    public void testRemoveAll() {
        // Setup
        // Run the test
        hsaShoppingCartCommodityServiceImplUnderTest.removeAll("operSubjectGuid");

        // Verify the results
    }

    @Test
    public void testListShoppingCartCommodity1() {
        // Setup
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        hsaShoppingCartCommodity.setIsDelete(false);
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setMemberInfoGuid("memberInfoGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> expectedResult = Arrays.asList(hsaShoppingCartCommodity);

        // Run the test
        final List<HsaShoppingCartCommodity> result = hsaShoppingCartCommodityServiceImplUnderTest.listShoppingCartCommodity(
                "memberInfoGuid", "operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListShoppingCartCommodity2() {
        // Setup
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        hsaShoppingCartCommodity.setIsDelete(false);
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setMemberInfoGuid("memberInfoGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> expectedResult = Arrays.asList(hsaShoppingCartCommodity);

        // Configure HsaShoppingCartCommodityMapper.listShoppingCartCommodity(...).
        final HsaShoppingCartCommodity hsaShoppingCartCommodity1 = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity1.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        hsaShoppingCartCommodity1.setIsDelete(false);
        hsaShoppingCartCommodity1.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity1.setMemberInfoGuid("memberInfoGuid");
        hsaShoppingCartCommodity1.setCommodityId(0L);
        hsaShoppingCartCommodity1.setSku("sku");
        hsaShoppingCartCommodity1.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity1.setAttr("attr");
        final List<HsaShoppingCartCommodity> hsaShoppingCartCommodities = Arrays.asList(hsaShoppingCartCommodity1);
        when(mockShoppingCartCommodityMapper.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(hsaShoppingCartCommodities);

        // Run the test
        final List<HsaShoppingCartCommodity> result = hsaShoppingCartCommodityServiceImplUnderTest.listShoppingCartCommodity(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListShoppingCartCommodity2_HsaShoppingCartCommodityMapperReturnsNoItems() {
        // Setup
        when(mockShoppingCartCommodityMapper.listShoppingCartCommodity(Arrays.asList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<HsaShoppingCartCommodity> result = hsaShoppingCartCommodityServiceImplUnderTest.listShoppingCartCommodity(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testListStoreId() {
        // Setup
        when(mockShoppingCartCommodityMapper.listStoreId("memberInfoGuid", "operSubjectGuid"))
                .thenReturn(Arrays.asList(0));

        // Run the test
        final List<Integer> result = hsaShoppingCartCommodityServiceImplUnderTest.listStoreId("memberInfoGuid",
                "operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList(0));
    }

    @Test
    public void testListStoreId_HsaShoppingCartCommodityMapperReturnsNoItems() {
        // Setup
        when(mockShoppingCartCommodityMapper.listStoreId("memberInfoGuid", "operSubjectGuid"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<Integer> result = hsaShoppingCartCommodityServiceImplUnderTest.listStoreId("memberInfoGuid",
                "operSubjectGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryShoppingCartCommodityNum() {
        assertThat(hsaShoppingCartCommodityServiceImplUnderTest.queryShoppingCartCommodityNum("memberInfoGuid",
                "operSubjectGuid")).isEqualTo(0);
    }

    @Test
    public void testRemoveByShoppingCartGuidList() {
        // Setup
        // Run the test
        hsaShoppingCartCommodityServiceImplUnderTest.removeByShoppingCartGuidList(Arrays.asList("value"));

        // Verify the results
    }

    @Test
    public void testQueryRepeatCommodity() {
        // Setup
        final HsaShoppingCartCommodity expectedResult = new HsaShoppingCartCommodity();
        expectedResult.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        expectedResult.setIsDelete(false);
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setCommodityId(0L);
        expectedResult.setSku("sku");
        expectedResult.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAttr("attr");

        // Run the test
        final HsaShoppingCartCommodity result = hsaShoppingCartCommodityServiceImplUnderTest.queryRepeatCommodity(
                "operSubjectGuid", "skuJson", "attrJson", "memberInfoGuid", 0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryByGuid() {
        // Setup
        final HsaShoppingCartCommodity expectedResult = new HsaShoppingCartCommodity();
        expectedResult.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        expectedResult.setIsDelete(false);
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setMemberInfoGuid("memberInfoGuid");
        expectedResult.setCommodityId(0L);
        expectedResult.setSku("sku");
        expectedResult.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setAttr("attr");

        // Run the test
        final HsaShoppingCartCommodity result = hsaShoppingCartCommodityServiceImplUnderTest.queryByGuid(
                "shoppingCartGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testListByGuid() {
        // Setup
        final HsaShoppingCartCommodity hsaShoppingCartCommodity = new HsaShoppingCartCommodity();
        hsaShoppingCartCommodity.setGuid("c4d64eff-6072-4dd9-b1b8-5c4c0fb99b5b");
        hsaShoppingCartCommodity.setIsDelete(false);
        hsaShoppingCartCommodity.setOperSubjectGuid("operSubjectGuid");
        hsaShoppingCartCommodity.setMemberInfoGuid("memberInfoGuid");
        hsaShoppingCartCommodity.setCommodityId(0L);
        hsaShoppingCartCommodity.setSku("sku");
        hsaShoppingCartCommodity.setUpdateTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaShoppingCartCommodity.setAttr("attr");
        final List<HsaShoppingCartCommodity> expectedResult = Arrays.asList(hsaShoppingCartCommodity);

        // Run the test
        final List<HsaShoppingCartCommodity> result = hsaShoppingCartCommodityServiceImplUnderTest.listByGuid(
                Arrays.asList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCountLimit() {
        assertThat(hsaShoppingCartCommodityServiceImplUnderTest.countLimit("memberInfoGuid")).isEqualTo(0);
    }
}
