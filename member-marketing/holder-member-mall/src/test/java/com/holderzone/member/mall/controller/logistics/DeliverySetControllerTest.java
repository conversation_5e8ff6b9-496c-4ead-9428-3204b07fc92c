package com.holderzone.member.mall.controller.logistics;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.vo.commodity.DeliverySetVO;
import com.holderzone.member.mall.service.commodity.HsaDeliverySetService;
import com.holderzone.member.mall.service.order.HsaMallBaseOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(DeliverySetController.class)
public class DeliverySetControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaDeliverySetService mockHsaDeliverySetService;
    @MockBean
    private HsaMallBaseOrderService mockMallBaseOrderService;

    @Test
    public void testGetDeliverySet() throws Exception {
        // Setup
        // Configure HsaDeliverySetService.getDeliverySet(...).
        final DeliverySetVO deliverySetVO = new DeliverySetVO();
        deliverySetVO.setGuid("ed530fe4-20b5-49a6-968b-b5e7ce6857e1");
        deliverySetVO.setIsExpressDelivery(0);
        deliverySetVO.setIsSelectAll(0);
        deliverySetVO.setLogistics(Arrays.asList("value"));
        deliverySetVO.setFreightMode(0);
        when(mockHsaDeliverySetService.getDeliverySet("operSubjectGuid")).thenReturn(deliverySetVO);

        // Run the test and verify the results
        mockMvc.perform(get("/delivery_set/get_delivery_set")
                        .param("operSubjectGuid", "operSubjectGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateDeliverySet() throws Exception {
        // Setup
        when(mockMallBaseOrderService.count(any(LambdaQueryWrapper.class))).thenReturn(0);

        // Configure HsaDeliverySetService.updateDeliverySet(...).
        final DeliverySetVO deliverySet = new DeliverySetVO();
        deliverySet.setGuid("ed530fe4-20b5-49a6-968b-b5e7ce6857e1");
        deliverySet.setIsExpressDelivery(0);
        deliverySet.setIsSelectAll(0);
        deliverySet.setLogistics(Arrays.asList("value"));
        deliverySet.setFreightMode(0);
        when(mockHsaDeliverySetService.updateDeliverySet(deliverySet)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/delivery_set/update_delivery_set")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testUpdateExpressState() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/delivery_set/update_express_state")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm HsaDeliverySetService.updateExpressState(...).
        final DeliverySetVO vo = new DeliverySetVO();
        vo.setGuid("ed530fe4-20b5-49a6-968b-b5e7ce6857e1");
        vo.setIsExpressDelivery(0);
        vo.setIsSelectAll(0);
        vo.setLogistics(Arrays.asList("value"));
        vo.setFreightMode(0);
        verify(mockHsaDeliverySetService).updateExpressState(vo);
    }

    @Test
    public void testGetAllExpressType() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(get("/delivery_set/get_all_express_type")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
