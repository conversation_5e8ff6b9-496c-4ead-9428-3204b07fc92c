package com.holderzone.member.mall.service.impl;

import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.HsaMallTest;
import com.holderzone.member.mall.mapper.HsaMallTestMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMallTestServiceImplTest {

    @Mock
    private HsaMallTestMapper mockHsaMallTestMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;

    @InjectMocks
    private HsaMallTestServiceImpl hsaMallTestServiceImplUnderTest;

    @Test
    public void testSaveTest() {
        // Setup
        final HsaMallTest hsaMallTest = new HsaMallTest();
        hsaMallTest.setId(0L);
        hsaMallTest.setGuid("56275bf2-3ac3-4c8a-9a08-a766d9af79c9");
        hsaMallTest.setOperSubjectGuid("operSubjectGuid");
        hsaMallTest.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallTest.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("56275bf2-3ac3-4c8a-9a08-a766d9af79c9");

        // Configure HsaMallTestMapper.insert(...).
        final HsaMallTest t = new HsaMallTest();
        t.setId(0L);
        t.setGuid("56275bf2-3ac3-4c8a-9a08-a766d9af79c9");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        t.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallTestMapper.insert(t)).thenReturn(0);

        // Run the test
        final boolean result = hsaMallTestServiceImplUnderTest.saveTest(hsaMallTest);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSave() {
        // Setup
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("56275bf2-3ac3-4c8a-9a08-a766d9af79c9");

        // Run the test
        final boolean result = hsaMallTestServiceImplUnderTest.save(0);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testDelete() {
        // Setup
        // Run the test
        hsaMallTestServiceImplUnderTest.delete(0L);

        // Verify the results
    }

    @Test
    public void testList() {
        // Setup
        final HsaMallTest hsaMallTest = new HsaMallTest();
        hsaMallTest.setId(0L);
        hsaMallTest.setGuid("56275bf2-3ac3-4c8a-9a08-a766d9af79c9");
        hsaMallTest.setOperSubjectGuid("operSubjectGuid");
        hsaMallTest.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallTest.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMallTest> expectedResult = Arrays.asList(hsaMallTest);

        // Run the test
        final List<HsaMallTest> result = hsaMallTestServiceImplUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
