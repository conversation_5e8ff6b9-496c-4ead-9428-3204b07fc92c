package com.holderzone.member.mall.controller.store;

import com.holderzone.member.common.vo.grade.StoreDataInfoVO;
import com.holderzone.member.mall.support.StoreSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(StoreController.class)
public class StoreControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StoreSupport mockStoreSupport;

    @Test
    public void testSaveOrUpdate() throws Exception {
        // Setup
        // Configure StoreSupport.getStoreById(...).
        final StoreDataInfoVO storeDataInfoVO = new StoreDataInfoVO();
        storeDataInfoVO.setId(0L);
        storeDataInfoVO.setName("name");
        storeDataInfoVO.setAddress_point("address_point");
        storeDataInfoVO.setAddress("address");
        storeDataInfoVO.setLogo("logo");
        when(mockStoreSupport.getStoreById("storeGuid")).thenReturn(storeDataInfoVO);

        // Run the test and verify the results
        mockMvc.perform(get("/store/get/{storeGuid}", "storeGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
