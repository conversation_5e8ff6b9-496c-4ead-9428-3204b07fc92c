package com.holderzone.member.mall.service.commodity.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.dto.label.LabelAreaJson;
import com.holderzone.member.common.dto.page.PageDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.mall.DistributionSetDTO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.commodity.HsaDistributionSet;
import com.holderzone.member.mall.mapper.commodity.HsaDistributionSetMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaDistributionSetServiceImplTest {

    @Mock
    private HsaDistributionSetMapper mockHsaDistributionSetMapper;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;

    @InjectMocks
    private HsaDistributionSetServiceImpl hsaDistributionSetServiceImplUnderTest;

    @Test
    public void testSaveOrUpdateDistributionSet() {
        // Setup
        final DistributionSetDTO request = new DistributionSetDTO();
        request.setGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3");
        request.setLinkman("linkman");
        request.setLinkmanPhone("linkmanPhone");
        final LabelAreaJson areaJson = new LabelAreaJson();
        request.setAreaJson(areaJson);
        request.setDetailAddress("detailAddress");
        request.setSendGoodsChoose(0);
        request.setSendGoodsDefault(0);
        request.setReceiveGoodsChoose(0);
        request.setReceiveGoodsDefault(0);

        // Configure HsaDistributionSetMapper.queryByGuid(...).
        final HsaDistributionSet hsaDistributionSet = new HsaDistributionSet();
        hsaDistributionSet.setGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3");
        hsaDistributionSet.setOperSubjectGuid("operSubjectGuid");
        hsaDistributionSet.setLinkman("linkman");
        hsaDistributionSet.setLinkmanPhone("linkmanPhone");
        hsaDistributionSet.setAreaJson("areaJson");
        hsaDistributionSet.setDetailAddress("detailAddress");
        hsaDistributionSet.setSendGoodsChoose(0);
        hsaDistributionSet.setSendGoodsDefault(0);
        hsaDistributionSet.setReceiveGoodsChoose(0);
        hsaDistributionSet.setReceiveGoodsDefault(0);
        hsaDistributionSet.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaDistributionSet.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaDistributionSetMapper.queryByGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3"))
                .thenReturn(hsaDistributionSet);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("a459fcad-74bb-4513-9db5-fddb36f05ad3");

        // Run the test
        final boolean result = hsaDistributionSetServiceImplUnderTest.saveOrUpdateDistributionSet(request);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockHsaDistributionSetMapper).updateSendGoodsDefaultStatus("operSubjectGuid");
        verify(mockHsaDistributionSetMapper).updateReceiveGoodsDefaultStatus("operSubjectGuid");
    }

    @Test
    public void testQueryDistributionSetDetail() {
        // Setup
        final DistributionSetDTO expectedResult = new DistributionSetDTO();
        expectedResult.setGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3");
        expectedResult.setLinkman("linkman");
        expectedResult.setLinkmanPhone("linkmanPhone");
        final LabelAreaJson areaJson = new LabelAreaJson();
        expectedResult.setAreaJson(areaJson);
        expectedResult.setDetailAddress("detailAddress");
        expectedResult.setSendGoodsChoose(0);
        expectedResult.setSendGoodsDefault(0);
        expectedResult.setReceiveGoodsChoose(0);
        expectedResult.setReceiveGoodsDefault(0);

        // Configure HsaDistributionSetMapper.queryByGuid(...).
        final HsaDistributionSet hsaDistributionSet = new HsaDistributionSet();
        hsaDistributionSet.setGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3");
        hsaDistributionSet.setOperSubjectGuid("operSubjectGuid");
        hsaDistributionSet.setLinkman("linkman");
        hsaDistributionSet.setLinkmanPhone("linkmanPhone");
        hsaDistributionSet.setAreaJson("areaJson");
        hsaDistributionSet.setDetailAddress("detailAddress");
        hsaDistributionSet.setSendGoodsChoose(0);
        hsaDistributionSet.setSendGoodsDefault(0);
        hsaDistributionSet.setReceiveGoodsChoose(0);
        hsaDistributionSet.setReceiveGoodsDefault(0);
        hsaDistributionSet.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaDistributionSet.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaDistributionSetMapper.queryByGuid("b5efb29f-e658-4243-bf4d-def0679c3973"))
                .thenReturn(hsaDistributionSet);

        // Run the test
        final DistributionSetDTO result = hsaDistributionSetServiceImplUnderTest.queryDistributionSetDetail(
                "b5efb29f-e658-4243-bf4d-def0679c3973");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryDistributionSetList() {
        // Setup
        final PageDTO request = new PageDTO();
        request.setCurrentPage(0);
        request.setPageSize(0);

        // Configure HsaDistributionSetMapper.selectList(...).
        final HsaDistributionSet hsaDistributionSet = new HsaDistributionSet();
        hsaDistributionSet.setGuid("a459fcad-74bb-4513-9db5-fddb36f05ad3");
        hsaDistributionSet.setOperSubjectGuid("operSubjectGuid");
        hsaDistributionSet.setLinkman("linkman");
        hsaDistributionSet.setLinkmanPhone("linkmanPhone");
        hsaDistributionSet.setAreaJson("areaJson");
        hsaDistributionSet.setDetailAddress("detailAddress");
        hsaDistributionSet.setSendGoodsChoose(0);
        hsaDistributionSet.setSendGoodsDefault(0);
        hsaDistributionSet.setReceiveGoodsChoose(0);
        hsaDistributionSet.setReceiveGoodsDefault(0);
        hsaDistributionSet.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaDistributionSet.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaDistributionSet> hsaDistributionSets = Arrays.asList(hsaDistributionSet);
        when(mockHsaDistributionSetMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(hsaDistributionSets);

        // Run the test
        final PageResult result = hsaDistributionSetServiceImplUnderTest.queryDistributionSetList(request);

        // Verify the results
    }

    @Test
    public void testQueryDistributionSetList_HsaDistributionSetMapperReturnsNoItems() {
        // Setup
        final PageDTO request = new PageDTO();
        request.setCurrentPage(0);
        request.setPageSize(0);

        when(mockHsaDistributionSetMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final PageResult result = hsaDistributionSetServiceImplUnderTest.queryDistributionSetList(request);

        // Verify the results
    }

    @Test
    public void testDeleteDistributionSet() {
        // Setup
        // Run the test
        final boolean result = hsaDistributionSetServiceImplUnderTest.deleteDistributionSet(
                "e1dd8167-0c87-435d-937a-e2319dde8d91");

        // Verify the results
        assertThat(result).isTrue();
        verify(mockHsaDistributionSetMapper).delete(any(LambdaQueryWrapper.class));
    }
}
