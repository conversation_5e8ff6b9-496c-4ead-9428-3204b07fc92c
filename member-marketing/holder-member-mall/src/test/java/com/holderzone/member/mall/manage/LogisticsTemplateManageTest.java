package com.holderzone.member.mall.manage;

import com.holderzone.member.common.dto.logistics.LogisticsCommodityDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberCommodityFeign;
import com.holderzone.member.common.qo.logistics.LogisticsCommodityQO;
import com.holderzone.member.common.qo.logistics.LogisticsTemplateQO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import com.holderzone.member.mall.manage.bo.LogisticsProductBO;
import com.holderzone.member.mall.manage.bo.LogisticsTemplateBO;
import com.holderzone.member.mall.service.logistics.HsaLogisticsChargeService;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class LogisticsTemplateManageTest {

    @Mock
    private HsaLogisticsTemplateService mockLogisticsTemplateService;
    @Mock
    private HsaLogisticsChargeService mockLogisticsChargeService;
    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private MemberCommodityFeign mockMemberCommodityFeign;
    @Mock
    private LogisticsCacheSupport mockLogisticsCacheSupport;

    private LogisticsTemplateManage logisticsTemplateManageUnderTest;

    @Before
    public void setUp() {
        logisticsTemplateManageUnderTest = new LogisticsTemplateManage(mockLogisticsTemplateService,
                mockLogisticsChargeService, mockGuidGeneratorUtil, mockMemberCommodityFeign, mockLogisticsCacheSupport);
    }

    @Test
    public void testList() {
        // Setup
        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("2ce996d2-91a3-4745-b801-12bc59c6b2a0");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setProductNum(0);
        final List<LogisticsTemplateVO> expectedResult = Arrays.asList(logisticsTemplateVO);

        // Configure HsaLogisticsTemplateService.list(...).
        final LogisticsTemplateVO logisticsTemplateVO1 = new LogisticsTemplateVO();
        logisticsTemplateVO1.setGuid("2ce996d2-91a3-4745-b801-12bc59c6b2a0");
        logisticsTemplateVO1.setTemplateName("templateName");
        logisticsTemplateVO1.setDefaultFlag(false);
        logisticsTemplateVO1.setChargeType(0);
        logisticsTemplateVO1.setProductNum(0);
        final List<LogisticsTemplateVO> logisticsTemplateVOS = Arrays.asList(logisticsTemplateVO1);
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("templateGuid");
        when(mockLogisticsTemplateService.list(query)).thenReturn(logisticsTemplateVOS);

        when(mockMemberCommodityFeign.countLogisticsCommodity(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        final List<LogisticsTemplateVO> result = logisticsTemplateManageUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testList_HsaLogisticsTemplateServiceReturnsNoItems() {
        // Setup
        // Configure HsaLogisticsTemplateService.list(...).
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("templateGuid");
        when(mockLogisticsTemplateService.list(query)).thenReturn(Collections.emptyList());

        // Run the test
        final List<LogisticsTemplateVO> result = logisticsTemplateManageUnderTest.list();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGuidList() {
        // Setup
        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("2ce996d2-91a3-4745-b801-12bc59c6b2a0");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setProductNum(0);
        final List<LogisticsTemplateVO> expectedResult = Arrays.asList(logisticsTemplateVO);

        // Configure HsaLogisticsTemplateService.guidList(...).
        final LogisticsTemplateVO logisticsTemplateVO1 = new LogisticsTemplateVO();
        logisticsTemplateVO1.setGuid("2ce996d2-91a3-4745-b801-12bc59c6b2a0");
        logisticsTemplateVO1.setTemplateName("templateName");
        logisticsTemplateVO1.setDefaultFlag(false);
        logisticsTemplateVO1.setChargeType(0);
        logisticsTemplateVO1.setProductNum(0);
        final List<LogisticsTemplateVO> logisticsTemplateVOS = Arrays.asList(logisticsTemplateVO1);
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("templateGuid");
        when(mockLogisticsTemplateService.guidList(query)).thenReturn(logisticsTemplateVOS);

        // Run the test
        final List<LogisticsTemplateVO> result = logisticsTemplateManageUnderTest.guidList("templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGuidList_HsaLogisticsTemplateServiceReturnsNoItems() {
        // Setup
        // Configure HsaLogisticsTemplateService.guidList(...).
        final LogisticsTemplateQO query = new LogisticsTemplateQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setExcludeTemplateGuid("templateGuid");
        when(mockLogisticsTemplateService.guidList(query)).thenReturn(Collections.emptyList());

        // Run the test
        final List<LogisticsTemplateVO> result = logisticsTemplateManageUnderTest.guidList("templateGuid");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testSave() {
        // Setup
        final LogisticsTemplateBO biz = new LogisticsTemplateBO();
        final HsaLogisticsTemplate template = new HsaLogisticsTemplate();
        template.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        template.setOperSubjectGuid("operSubjectGuid");
        template.setTemplateName("默认模板");
        template.setDefaultFlag(false);
        template.setChargeType(0);
        template.setFreightAmount(new BigDecimal("0.00"));
        biz.setTemplate(template);
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        biz.setCharges(Arrays.asList(hsaLogisticsCharge));

        // Run the test
        logisticsTemplateManageUnderTest.save(biz);

        // Verify the results
        verify(mockLogisticsTemplateService).clearDefaultFlag();

        // Confirm HsaLogisticsTemplateService.save(...).
        final HsaLogisticsTemplate t = new HsaLogisticsTemplate();
        t.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setTemplateName("默认模板");
        t.setDefaultFlag(false);
        t.setChargeType(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        verify(mockLogisticsTemplateService).save(t);

        // Confirm HsaLogisticsChargeService.saveBatch(...).
        final HsaLogisticsCharge hsaLogisticsCharge1 = new HsaLogisticsCharge();
        hsaLogisticsCharge1.setId(0L);
        hsaLogisticsCharge1.setGuid("c123bafd-4094-49bf-97d1-7749db384840");
        hsaLogisticsCharge1.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge1.setTemplateGuid("templateGuid");
        hsaLogisticsCharge1.setRegion("region");
        final List<HsaLogisticsCharge> entityList = Arrays.asList(hsaLogisticsCharge1);
        verify(mockLogisticsChargeService).saveBatch(entityList);

        // Confirm LogisticsCacheSupport.setTemplate(...).
        final LogisticsTemplateDetailsVO detailsVO = new LogisticsTemplateDetailsVO();
        detailsVO.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        detailsVO.setTemplateName("默认模板");
        detailsVO.setChargeType(0);
        detailsVO.setFreightAmount(new BigDecimal("0.00"));
        detailsVO.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        detailsVO.setCharges(Arrays.asList(innerCharge));
        verify(mockLogisticsCacheSupport).setTemplate(detailsVO);
    }

    @Test
    public void testUpdate() {
        // Setup
        final LogisticsTemplateBO biz = new LogisticsTemplateBO();
        final HsaLogisticsTemplate template = new HsaLogisticsTemplate();
        template.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        template.setOperSubjectGuid("operSubjectGuid");
        template.setTemplateName("默认模板");
        template.setDefaultFlag(false);
        template.setChargeType(0);
        template.setFreightAmount(new BigDecimal("0.00"));
        biz.setTemplate(template);
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        biz.setCharges(Arrays.asList(hsaLogisticsCharge));

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsTemplate);

        // Run the test
        logisticsTemplateManageUnderTest.update(biz);

        // Verify the results
        verify(mockLogisticsTemplateService).clearDefaultFlag();

        // Confirm HsaLogisticsTemplateService.updateById(...).
        final HsaLogisticsTemplate t = new HsaLogisticsTemplate();
        t.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setTemplateName("默认模板");
        t.setDefaultFlag(false);
        t.setChargeType(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        verify(mockLogisticsTemplateService).updateById(t);
        verify(mockLogisticsChargeService).removeByTemplateGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");

        // Confirm HsaLogisticsChargeService.saveBatch(...).
        final HsaLogisticsCharge hsaLogisticsCharge1 = new HsaLogisticsCharge();
        hsaLogisticsCharge1.setId(0L);
        hsaLogisticsCharge1.setGuid("c123bafd-4094-49bf-97d1-7749db384840");
        hsaLogisticsCharge1.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge1.setTemplateGuid("templateGuid");
        hsaLogisticsCharge1.setRegion("region");
        final List<HsaLogisticsCharge> entityList = Arrays.asList(hsaLogisticsCharge1);
        verify(mockLogisticsChargeService).saveBatch(entityList);

        // Confirm LogisticsCacheSupport.setTemplate(...).
        final LogisticsTemplateDetailsVO detailsVO = new LogisticsTemplateDetailsVO();
        detailsVO.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        detailsVO.setTemplateName("默认模板");
        detailsVO.setChargeType(0);
        detailsVO.setFreightAmount(new BigDecimal("0.00"));
        detailsVO.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        detailsVO.setCharges(Arrays.asList(innerCharge));
        verify(mockLogisticsCacheSupport).setTemplate(detailsVO);
    }

    @Test
    public void testRemove() {
        // Setup
        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("templateGuid")).thenReturn(hsaLogisticsTemplate);

        when(mockMemberCommodityFeign.countLogisticsCommodity(Arrays.asList("value"))).thenReturn(new HashMap<>());

        // Run the test
        logisticsTemplateManageUnderTest.remove("templateGuid");

        // Verify the results
        verify(mockLogisticsTemplateService).removeById("templateGuid");
        verify(mockLogisticsChargeService).removeByTemplateGuid("templateGuid");
        verify(mockLogisticsCacheSupport).removeTemplate("templateGuid");
    }

    @Test
    public void testTransferTemplateDetailsVO() {
        // Setup
        final LogisticsTemplateBO biz = new LogisticsTemplateBO();
        final HsaLogisticsTemplate template = new HsaLogisticsTemplate();
        template.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        template.setOperSubjectGuid("operSubjectGuid");
        template.setTemplateName("默认模板");
        template.setDefaultFlag(false);
        template.setChargeType(0);
        template.setFreightAmount(new BigDecimal("0.00"));
        biz.setTemplate(template);
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        biz.setCharges(Arrays.asList(hsaLogisticsCharge));

        final LogisticsTemplateDetailsVO expectedResult = new LogisticsTemplateDetailsVO();
        expectedResult.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        expectedResult.setTemplateName("默认模板");
        expectedResult.setChargeType(0);
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        expectedResult.setCharges(Arrays.asList(innerCharge));

        // Run the test
        final LogisticsTemplateDetailsVO result = logisticsTemplateManageUnderTest.transferTemplateDetailsVO(biz);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testPageLogisticsCommodity() {
        // Setup
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");

        // Configure MemberCommodityFeign.pageLogisticsCommodity(...).
        final LogisticsCommodityQO query1 = new LogisticsCommodityQO();
        query1.setOperSubjectGuid("operSubjectGuid");
        query1.setEnterpriseGuid("enterpriseGuid");
        query1.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query1.setStoreIdList(Arrays.asList(0L));
        query1.setCommodityName("commodityName");
        when(mockMemberCommodityFeign.pageLogisticsCommodity(query1)).thenReturn(new PageResult<>(0, 0, 0));

        // Configure HsaLogisticsTemplateService.listByIds(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        final Collection<HsaLogisticsTemplate> hsaLogisticsTemplates = Arrays.asList(hsaLogisticsTemplate);
        when(mockLogisticsTemplateService.listByIds(Arrays.asList("value"))).thenReturn(hsaLogisticsTemplates);

        // Run the test
        final PageResult result = logisticsTemplateManageUnderTest.pageLogisticsCommodity(query);

        // Verify the results
    }

    @Test
    public void testPageLogisticsCommodity_HsaLogisticsTemplateServiceReturnsNoItems() {
        // Setup
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");

        // Configure MemberCommodityFeign.pageLogisticsCommodity(...).
        final LogisticsCommodityQO query1 = new LogisticsCommodityQO();
        query1.setOperSubjectGuid("operSubjectGuid");
        query1.setEnterpriseGuid("enterpriseGuid");
        query1.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query1.setStoreIdList(Arrays.asList(0L));
        query1.setCommodityName("commodityName");
        when(mockMemberCommodityFeign.pageLogisticsCommodity(query1)).thenReturn(new PageResult<>(0, 0, 0));

        when(mockLogisticsTemplateService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult result = logisticsTemplateManageUnderTest.pageLogisticsCommodity(query);

        // Verify the results
    }

    @Test
    public void testPageExcludeLogisticsCommodity() {
        // Setup
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");

        // Configure MemberCommodityFeign.pageExcludeLogisticsCommodity(...).
        final LogisticsCommodityQO query1 = new LogisticsCommodityQO();
        query1.setOperSubjectGuid("operSubjectGuid");
        query1.setEnterpriseGuid("enterpriseGuid");
        query1.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query1.setStoreIdList(Arrays.asList(0L));
        query1.setCommodityName("commodityName");
        when(mockMemberCommodityFeign.pageExcludeLogisticsCommodity(query1)).thenReturn(new PageResult<>(0, 0, 0));

        // Configure HsaLogisticsTemplateService.listByIds(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        final Collection<HsaLogisticsTemplate> hsaLogisticsTemplates = Arrays.asList(hsaLogisticsTemplate);
        when(mockLogisticsTemplateService.listByIds(Arrays.asList("value"))).thenReturn(hsaLogisticsTemplates);

        // Run the test
        final PageResult result = logisticsTemplateManageUnderTest.pageExcludeLogisticsCommodity(query);

        // Verify the results
    }

    @Test
    public void testPageExcludeLogisticsCommodity_HsaLogisticsTemplateServiceReturnsNoItems() {
        // Setup
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");

        // Configure MemberCommodityFeign.pageExcludeLogisticsCommodity(...).
        final LogisticsCommodityQO query1 = new LogisticsCommodityQO();
        query1.setOperSubjectGuid("operSubjectGuid");
        query1.setEnterpriseGuid("enterpriseGuid");
        query1.setGuid("2fbec003-930b-450a-94dd-be3fc0dba2a3");
        query1.setStoreIdList(Arrays.asList(0L));
        query1.setCommodityName("commodityName");
        when(mockMemberCommodityFeign.pageExcludeLogisticsCommodity(query1)).thenReturn(new PageResult<>(0, 0, 0));

        when(mockLogisticsTemplateService.listByIds(Arrays.asList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final PageResult result = logisticsTemplateManageUnderTest.pageExcludeLogisticsCommodity(query);

        // Verify the results
    }

    @Test
    public void testSaveCommodity() {
        // Setup
        final LogisticsProductBO biz = new LogisticsProductBO();
        biz.setTemplateGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        final LogisticsCommodityDTO logisticsCommodityDTO = new LogisticsCommodityDTO();
        logisticsCommodityDTO.setTemplateGuid("templateGuid");
        logisticsCommodityDTO.setCommodityCode("commodityCode");
        logisticsCommodityDTO.setOperSubjectGuid("operSubjectGuid");
        biz.setCommodities(Arrays.asList(logisticsCommodityDTO));

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsTemplate);

        // Run the test
        logisticsTemplateManageUnderTest.saveCommodity(biz);

        // Verify the results
        // Confirm MemberCommodityFeign.saveBatchLogisticsCommodity(...).
        final LogisticsCommodityDTO logisticsCommodityDTO1 = new LogisticsCommodityDTO();
        logisticsCommodityDTO1.setTemplateGuid("templateGuid");
        logisticsCommodityDTO1.setCommodityCode("commodityCode");
        logisticsCommodityDTO1.setOperSubjectGuid("operSubjectGuid");
        final List<LogisticsCommodityDTO> commodityDTOList = Arrays.asList(logisticsCommodityDTO1);
        verify(mockMemberCommodityFeign).saveBatchLogisticsCommodity(commodityDTOList);
    }

    @Test
    public void testUpdateCommodity() {
        // Setup
        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsTemplate);

        // Run the test
        logisticsTemplateManageUnderTest.updateCommodity("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb", "commodityCode");

        // Verify the results
        verify(mockMemberCommodityFeign).updateTemplateGuidByCommodityCode("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb",
                "commodityCode");
    }

    @Test
    public void testGet() {
        // Setup
        final LogisticsTemplateDetailsVO expectedResult = new LogisticsTemplateDetailsVO();
        expectedResult.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        expectedResult.setTemplateName("默认模板");
        expectedResult.setChargeType(0);
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        expectedResult.setCharges(Arrays.asList(innerCharge));

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsTemplate);

        // Configure HsaLogisticsChargeService.findByTemplateGuid(...).
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setId(0L);
        hsaLogisticsCharge.setGuid("c123bafd-4094-49bf-97d1-7749db384840");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("templateGuid");
        hsaLogisticsCharge.setRegion("region");
        final List<HsaLogisticsCharge> hsaLogisticsCharges = Arrays.asList(hsaLogisticsCharge);
        when(mockLogisticsChargeService.findByTemplateGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsCharges);

        // Run the test
        final LogisticsTemplateDetailsVO result = logisticsTemplateManageUnderTest.get(
                "0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGet_HsaLogisticsChargeServiceReturnsNoItems() {
        // Setup
        final LogisticsTemplateDetailsVO expectedResult = new LogisticsTemplateDetailsVO();
        expectedResult.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        expectedResult.setTemplateName("默认模板");
        expectedResult.setChargeType(0);
        expectedResult.setFreightAmount(new BigDecimal("0.00"));
        expectedResult.setDefaultFlag(false);
        final LogisticsTemplateDetailsVO.InnerCharge innerCharge = new LogisticsTemplateDetailsVO.InnerCharge();
        innerCharge.setRegion("region");
        innerCharge.setRegions(Arrays.asList("value"));
        expectedResult.setCharges(Arrays.asList(innerCharge));

        // Configure HsaLogisticsTemplateService.getById(...).
        final HsaLogisticsTemplate hsaLogisticsTemplate = new HsaLogisticsTemplate();
        hsaLogisticsTemplate.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        hsaLogisticsTemplate.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsTemplate.setTemplateName("默认模板");
        hsaLogisticsTemplate.setDefaultFlag(false);
        hsaLogisticsTemplate.setChargeType(0);
        hsaLogisticsTemplate.setFreightAmount(new BigDecimal("0.00"));
        when(mockLogisticsTemplateService.getById("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(hsaLogisticsTemplate);

        when(mockLogisticsChargeService.findByTemplateGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final LogisticsTemplateDetailsVO result = logisticsTemplateManageUnderTest.get(
                "0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testInitLogisticsTemplate() {
        // Setup
        when(mockLogisticsTemplateService.queryDefaultTemplateGuid("operSubjectGuid")).thenReturn("result");
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");

        // Run the test
        logisticsTemplateManageUnderTest.initLogisticsTemplate(Arrays.asList("value"));

        // Verify the results
        // Confirm HsaLogisticsTemplateService.save(...).
        final HsaLogisticsTemplate t = new HsaLogisticsTemplate();
        t.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setTemplateName("默认模板");
        t.setDefaultFlag(false);
        t.setChargeType(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        verify(mockLogisticsTemplateService).save(t);
    }

    @Test
    public void testCreateDefaultTemplate() {
        // Setup
        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");

        // Run the test
        logisticsTemplateManageUnderTest.createDefaultTemplate("operSubjectGuid");

        // Verify the results
        // Confirm HsaLogisticsTemplateService.save(...).
        final HsaLogisticsTemplate t = new HsaLogisticsTemplate();
        t.setGuid("0b3b6cb8-1fea-475f-bd9a-c24d3235d6cb");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setTemplateName("默认模板");
        t.setDefaultFlag(false);
        t.setChargeType(0);
        t.setFreightAmount(new BigDecimal("0.00"));
        verify(mockLogisticsTemplateService).save(t);
    }
}
