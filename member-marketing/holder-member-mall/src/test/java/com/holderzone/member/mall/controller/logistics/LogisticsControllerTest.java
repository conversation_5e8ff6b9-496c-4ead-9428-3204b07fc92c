package com.holderzone.member.mall.controller.logistics;

import com.holderzone.member.common.dto.logistics.LogisticsCommodityDTO;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.qo.logistics.LogisticsCommodityQO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateDetailsVO;
import com.holderzone.member.common.vo.logistics.LogisticsTemplateVO;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsCharge;
import com.holderzone.member.mall.entity.logistics.HsaLogisticsTemplate;
import com.holderzone.member.mall.manage.LogisticsTemplateManage;
import com.holderzone.member.mall.manage.bo.LogisticsProductBO;
import com.holderzone.member.mall.manage.bo.LogisticsTemplateBO;
import com.holderzone.member.mall.service.logistics.HsaLogisticsTemplateService;
import com.holderzone.member.mall.support.LogisticsCacheSupport;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(LogisticsController.class)
public class LogisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaLogisticsTemplateService mockLogisticsTemplateService;
    @MockBean
    private LogisticsTemplateManage mockLogisticsTemplateManage;
    @MockBean
    private LogisticsCacheSupport mockLogisticsCacheSupport;

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure LogisticsTemplateManage.list(...).
        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("461b3d7e-e521-427d-9fa8-e2948200537e");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setFreightAmount(new BigDecimal("0.00"));
        final List<LogisticsTemplateVO> logisticsTemplateVOS = Arrays.asList(logisticsTemplateVO);
        when(mockLogisticsTemplateManage.list()).thenReturn(logisticsTemplateVOS);

        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/page")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testList_LogisticsTemplateManageReturnsNoItems() throws Exception {
        // Setup
        when(mockLogisticsTemplateManage.list()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/page")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGuidList() throws Exception {
        // Setup
        // Configure LogisticsTemplateManage.guidList(...).
        final LogisticsTemplateVO logisticsTemplateVO = new LogisticsTemplateVO();
        logisticsTemplateVO.setGuid("461b3d7e-e521-427d-9fa8-e2948200537e");
        logisticsTemplateVO.setTemplateName("templateName");
        logisticsTemplateVO.setDefaultFlag(false);
        logisticsTemplateVO.setChargeType(0);
        logisticsTemplateVO.setFreightAmount(new BigDecimal("0.00"));
        final List<LogisticsTemplateVO> logisticsTemplateVOS = Arrays.asList(logisticsTemplateVO);
        when(mockLogisticsTemplateManage.guidList("templateGuid")).thenReturn(logisticsTemplateVOS);

        // Run the test and verify the results
        mockMvc.perform(get("/logistics/template/guid/list")
                        .param("templateGuid", "templateGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGuidList_LogisticsTemplateManageReturnsNoItems() throws Exception {
        // Setup
        when(mockLogisticsTemplateManage.guidList("templateGuid")).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(get("/logistics/template/guid/list")
                        .param("templateGuid", "templateGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("[]", true));
    }

    @Test
    public void testGetById() throws Exception {
        // Setup
        // Configure LogisticsTemplateManage.get(...).
        final LogisticsTemplateDetailsVO logisticsTemplateDetailsVO = new LogisticsTemplateDetailsVO();
        logisticsTemplateDetailsVO.setGuid("a07daae4-1229-4dc5-9072-58b99000e893");
        logisticsTemplateDetailsVO.setTemplateName("templateName");
        logisticsTemplateDetailsVO.setChargeType(0);
        logisticsTemplateDetailsVO.setFreightAmount(new BigDecimal("0.00"));
        logisticsTemplateDetailsVO.setDefaultFlag(false);
        when(mockLogisticsTemplateManage.get("890c4b4b-1edc-40f7-a187-d7011f812bc8"))
                .thenReturn(logisticsTemplateDetailsVO);

        // Run the test and verify the results
        mockMvc.perform(get("/logistics/template/{guid}", "890c4b4b-1edc-40f7-a187-d7011f812bc8")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSave() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm LogisticsTemplateManage.save(...).
        final LogisticsTemplateBO biz = new LogisticsTemplateBO();
        final HsaLogisticsTemplate template = new HsaLogisticsTemplate();
        template.setGuid("guid");
        template.setOperSubjectGuid("operSubjectGuid");
        template.setTemplateName("templateName");
        template.setDefaultFlag(false);
        template.setChargeType(0);
        template.setFreightAmount(new BigDecimal("0.00"));
        template.setCharges("charges");
        biz.setTemplate(template);
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setGuid("dabb6a3d-0fc7-47a3-a642-27fe0187f191");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("guid");
        hsaLogisticsCharge.setRegion("region");
        hsaLogisticsCharge.setWeight(new BigDecimal("0.00"));
        hsaLogisticsCharge.setPrice(new BigDecimal("0.00"));
        hsaLogisticsCharge.setSecondWeight(new BigDecimal("0.00"));
        hsaLogisticsCharge.setSecondPrice(new BigDecimal("0.00"));
        biz.setCharges(Arrays.asList(hsaLogisticsCharge));
        verify(mockLogisticsTemplateManage).save(biz);
    }

    @Test
    public void testUpdate() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm LogisticsTemplateManage.update(...).
        final LogisticsTemplateBO biz = new LogisticsTemplateBO();
        final HsaLogisticsTemplate template = new HsaLogisticsTemplate();
        template.setGuid("guid");
        template.setOperSubjectGuid("operSubjectGuid");
        template.setTemplateName("templateName");
        template.setDefaultFlag(false);
        template.setChargeType(0);
        template.setFreightAmount(new BigDecimal("0.00"));
        template.setCharges("charges");
        biz.setTemplate(template);
        final HsaLogisticsCharge hsaLogisticsCharge = new HsaLogisticsCharge();
        hsaLogisticsCharge.setGuid("dabb6a3d-0fc7-47a3-a642-27fe0187f191");
        hsaLogisticsCharge.setOperSubjectGuid("operSubjectGuid");
        hsaLogisticsCharge.setTemplateGuid("guid");
        hsaLogisticsCharge.setRegion("region");
        hsaLogisticsCharge.setWeight(new BigDecimal("0.00"));
        hsaLogisticsCharge.setPrice(new BigDecimal("0.00"));
        hsaLogisticsCharge.setSecondWeight(new BigDecimal("0.00"));
        hsaLogisticsCharge.setSecondPrice(new BigDecimal("0.00"));
        biz.setCharges(Arrays.asList(hsaLogisticsCharge));
        verify(mockLogisticsTemplateManage).update(biz);
    }

    @Test
    public void testRemove() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(delete("/logistics/template/{guid}", "642d5c35-d112-4d1e-a081-d16eaadbb6fd")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockLogisticsTemplateManage).remove("642d5c35-d112-4d1e-a081-d16eaadbb6fd");
    }

    @Test
    public void testProductPage() throws Exception {
        // Setup
        // Configure LogisticsTemplateManage.pageLogisticsCommodity(...).
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("3f28c5d4-5ab1-4b41-a215-85ddfeaa17f1");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");
        when(mockLogisticsTemplateManage.pageLogisticsCommodity(query)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/product/page")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testChoiceProduct() throws Exception {
        // Setup
        // Configure LogisticsTemplateManage.pageExcludeLogisticsCommodity(...).
        final LogisticsCommodityQO query = new LogisticsCommodityQO();
        query.setOperSubjectGuid("operSubjectGuid");
        query.setEnterpriseGuid("enterpriseGuid");
        query.setGuid("3f28c5d4-5ab1-4b41-a215-85ddfeaa17f1");
        query.setStoreIdList(Arrays.asList(0L));
        query.setCommodityName("commodityName");
        when(mockLogisticsTemplateManage.pageExcludeLogisticsCommodity(query)).thenReturn(new PageResult<>(0, 0, 0));

        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/product/choice")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSaveCommodity() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/product/save")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));

        // Confirm LogisticsTemplateManage.saveCommodity(...).
        final LogisticsProductBO biz = new LogisticsProductBO();
        biz.setTemplateGuid("templateGuid");
        final LogisticsCommodityDTO logisticsCommodityDTO = new LogisticsCommodityDTO();
        logisticsCommodityDTO.setTemplateGuid("templateGuid");
        logisticsCommodityDTO.setCommodityCode("commodityCode");
        logisticsCommodityDTO.setOperSubjectGuid("operSubjectGuid");
        biz.setCommodities(Arrays.asList(logisticsCommodityDTO));
        verify(mockLogisticsTemplateManage).saveCommodity(biz);
    }

    @Test
    public void testUpdateCommodity() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/logistics/template/product/update")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockLogisticsTemplateManage).updateCommodity("templateGuid", "commodityCode");
    }

    @Test
    public void testQueryDefaultTemplateGuid() throws Exception {
        // Setup
        when(mockLogisticsTemplateService.queryDefaultTemplateGuid("operSubjectGuid")).thenReturn("result");

        // Run the test and verify the results
        mockMvc.perform(get("/logistics/template/default")
                        .param("operSubjectGuid", "operSubjectGuid")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testGetFreightAmount() throws Exception {
        // Setup
        when(mockLogisticsCacheSupport.getFreightAmount("commodityCode", 0, "province", "city"))
                .thenReturn(new BigDecimal("0.00"));

        // Run the test and verify the results
        mockMvc.perform(
                        get("/logistics/template/freight/{commodityCode}/{productNum}/{province}/{city}", "commodityCode", 0,
                                "province", "city")
                                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
