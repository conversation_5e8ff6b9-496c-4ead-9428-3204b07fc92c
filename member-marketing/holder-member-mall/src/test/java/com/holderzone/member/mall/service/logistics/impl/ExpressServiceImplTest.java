package com.holderzone.member.mall.service.logistics.impl;

import com.holderzone.member.common.dto.logistics.KdhTrackDetail;
import com.holderzone.member.common.qo.logistics.ExpressTrackQO;
import com.holderzone.member.mall.config.KdhConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExpressServiceImplTest {

    @Mock
    private KdhConfig mockKdhConfig;
    @Mock
    private RestTemplate mockRestTemplate;

    private ExpressServiceImpl expressServiceImplUnderTest;

    @Before
    public void setUp() {
        expressServiceImplUnderTest = new ExpressServiceImpl(mockKdhConfig, mockRestTemplate);
    }

    @Test
    public void testQueryExpress() {
        // Setup
        final ExpressTrackQO expressTrackQO = new ExpressTrackQO();
        expressTrackQO.setLogisticsNumber("logisticsNumber");
        expressTrackQO.setLogisticsName("logisticsName");
        expressTrackQO.setLogisticsCode("logisticsCode");

        final KdhTrackDetail kdhTrackDetail = new KdhTrackDetail();
        kdhTrackDetail.setTime("time");
        kdhTrackDetail.setContext("context");
        kdhTrackDetail.setStatus("status");
        final List<KdhTrackDetail> expectedResult = Arrays.asList(kdhTrackDetail);
        when(mockKdhConfig.getAppId()).thenReturn("result");
        when(mockKdhConfig.getMethod()).thenReturn("result");
        when(mockKdhConfig.getAppKey()).thenReturn("result");
        when(mockKdhConfig.getResultSort()).thenReturn("sort");
        when(mockKdhConfig.getUrl()).thenReturn("url");
        when(mockRestTemplate.postForObject("url",
                new HttpEntity<>(new LinkedMultiValueMap(new HashMap<>()), new HttpHeaders()),
                String.class)).thenReturn("result");

        // Run the test
        final List<KdhTrackDetail> result = expressServiceImplUnderTest.queryExpress(expressTrackQO);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryExpress_RestTemplateReturnsNull() {
        // Setup
        final ExpressTrackQO expressTrackQO = new ExpressTrackQO();
        expressTrackQO.setLogisticsNumber("logisticsNumber");
        expressTrackQO.setLogisticsName("logisticsName");
        expressTrackQO.setLogisticsCode("logisticsCode");

        when(mockKdhConfig.getAppId()).thenReturn("result");
        when(mockKdhConfig.getMethod()).thenReturn("result");
        when(mockKdhConfig.getAppKey()).thenReturn("result");
        when(mockKdhConfig.getResultSort()).thenReturn("sort");
        when(mockKdhConfig.getUrl()).thenReturn("url");
        when(mockRestTemplate.postForObject("url",
                new HttpEntity<>(new LinkedMultiValueMap(new HashMap<>()), new HttpHeaders()),
                String.class)).thenReturn(null);

        // Run the test
        final List<KdhTrackDetail> result = expressServiceImplUnderTest.queryExpress(expressTrackQO);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryExpress_RestTemplateThrowsRestClientException() {
        // Setup
        final ExpressTrackQO expressTrackQO = new ExpressTrackQO();
        expressTrackQO.setLogisticsNumber("logisticsNumber");
        expressTrackQO.setLogisticsName("logisticsName");
        expressTrackQO.setLogisticsCode("logisticsCode");

        when(mockKdhConfig.getAppId()).thenReturn("result");
        when(mockKdhConfig.getMethod()).thenReturn("result");
        when(mockKdhConfig.getAppKey()).thenReturn("result");
        when(mockKdhConfig.getResultSort()).thenReturn("sort");
        when(mockKdhConfig.getUrl()).thenReturn("url");
        when(mockRestTemplate.postForObject("url",
                new HttpEntity<>(new LinkedMultiValueMap(new HashMap<>()), new HttpHeaders()), String.class))
                .thenThrow(RestClientException.class);

        // Run the test
        assertThatThrownBy(() -> expressServiceImplUnderTest.queryExpress(expressTrackQO))
                .isInstanceOf(RestClientException.class);
    }
}
