package com.holderzone.member.mall.controller;

import com.holderzone.member.mall.entity.HsaMallTest;
import com.holderzone.member.mall.service.HsaMallTestService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@WebMvcTest(TestController.class)
public class TestControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private HsaMallTestService mockHsaMallTestService;

    @Test
    public void testTestSave() throws Exception {
        // Setup
        // Configure HsaMallTestService.saveTest(...).
        final HsaMallTest hsaMallTest = new HsaMallTest();
        hsaMallTest.setId(0L);
        hsaMallTest.setGuid("02558791-b5bd-4314-bc87-de0938557709");
        hsaMallTest.setOperSubjectGuid("operSubjectGuid");
        hsaMallTest.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallTest.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockHsaMallTestService.saveTest(hsaMallTest)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_test/testSave")
                        .content("content").contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testSave() throws Exception {
        // Setup
        when(mockHsaMallTestService.save(0)).thenReturn(false);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_test/save")
                        .param("size", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testDelete() throws Exception {
        // Setup
        // Run the test and verify the results
        mockMvc.perform(post("/mall_test/delete")
                        .param("id", "0")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
        verify(mockHsaMallTestService).delete(0L);
    }

    @Test
    public void testList() throws Exception {
        // Setup
        // Configure HsaMallTestService.list(...).
        final HsaMallTest hsaMallTest = new HsaMallTest();
        hsaMallTest.setId(0L);
        hsaMallTest.setGuid("02558791-b5bd-4314-bc87-de0938557709");
        hsaMallTest.setOperSubjectGuid("operSubjectGuid");
        hsaMallTest.setGmtCreate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        hsaMallTest.setGmtModified(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<HsaMallTest> hsaMallTests = Arrays.asList(hsaMallTest);
        when(mockHsaMallTestService.list()).thenReturn(hsaMallTests);

        // Run the test and verify the results
        mockMvc.perform(post("/mall_test/list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }

    @Test
    public void testList_HsaMallTestServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockHsaMallTestService.list()).thenReturn(Collections.emptyList());

        // Run the test and verify the results
        mockMvc.perform(post("/mall_test/list")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().json("{}", true));
    }
}
