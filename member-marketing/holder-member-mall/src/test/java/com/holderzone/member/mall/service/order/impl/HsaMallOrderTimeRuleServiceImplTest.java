package com.holderzone.member.mall.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.holderzone.member.common.qo.mall.order.MallOrderTimeRuleVO;
import com.holderzone.member.common.util.number.GuidGeneratorUtil;
import com.holderzone.member.mall.entity.order.HsaMallOrderTimeRule;
import com.holderzone.member.mall.mapper.order.HsaMallOrderTimeRuleMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HsaMallOrderTimeRuleServiceImplTest {

    @Mock
    private GuidGeneratorUtil mockGuidGeneratorUtil;
    @Mock
    private HsaMallOrderTimeRuleMapper mockHsaMallOrderTimeRuleMapper;
    @Mock
    private RedisTemplate<String, String> mockStringRedisTemplate;

    @InjectMocks
    private HsaMallOrderTimeRuleServiceImpl hsaMallOrderTimeRuleServiceImplUnderTest;

    @Test
    public void testSaveMallOrderTimeRule() {
        // Setup
        final MallOrderTimeRuleVO mallOrderTimeRuleVO = new MallOrderTimeRuleVO();
        mallOrderTimeRuleVO.setGuid("cff17c69-7801-45b6-beb0-2b21dd5da224");
        mallOrderTimeRuleVO.setPayTimeValue(0);
        mallOrderTimeRuleVO.setTakeTimeValue(0);
        mallOrderTimeRuleVO.setAutomaticRefund(0);
        mallOrderTimeRuleVO.setAutomaticTimeValue(0);

        // Configure HsaMallOrderTimeRuleMapper.selectOne(...).
        final HsaMallOrderTimeRule hsaMallOrderTimeRule = new HsaMallOrderTimeRule();
        hsaMallOrderTimeRule.setId(0L);
        hsaMallOrderTimeRule.setGuid("aedecc65-dc9b-4f6f-8a72-6f66c3c34712");
        hsaMallOrderTimeRule.setOperSubjectGuid("operSubjectGuid");
        hsaMallOrderTimeRule.setPayTimeValue(0);
        hsaMallOrderTimeRule.setTakeTimeValue(0);
        when(mockHsaMallOrderTimeRuleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallOrderTimeRule);

        when(mockGuidGeneratorUtil.getStringGuid("tab")).thenReturn("aedecc65-dc9b-4f6f-8a72-6f66c3c34712");
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = hsaMallOrderTimeRuleServiceImplUnderTest.saveMallOrderTimeRule(mallOrderTimeRuleVO);

        // Verify the results
        assertThat(result).isFalse();

        // Confirm HsaMallOrderTimeRuleMapper.updateById(...).
        final HsaMallOrderTimeRule t = new HsaMallOrderTimeRule();
        t.setId(0L);
        t.setGuid("aedecc65-dc9b-4f6f-8a72-6f66c3c34712");
        t.setOperSubjectGuid("operSubjectGuid");
        t.setPayTimeValue(0);
        t.setTakeTimeValue(0);
        verify(mockHsaMallOrderTimeRuleMapper).updateById(t);

        // Confirm HsaMallOrderTimeRuleMapper.insert(...).
        final HsaMallOrderTimeRule t1 = new HsaMallOrderTimeRule();
        t1.setId(0L);
        t1.setGuid("aedecc65-dc9b-4f6f-8a72-6f66c3c34712");
        t1.setOperSubjectGuid("operSubjectGuid");
        t1.setPayTimeValue(0);
        t1.setTakeTimeValue(0);
        verify(mockHsaMallOrderTimeRuleMapper).insert(t1);
    }

    @Test
    public void testGetMallOrderTimeRule() {
        // Setup
        final MallOrderTimeRuleVO expectedResult = new MallOrderTimeRuleVO();
        expectedResult.setGuid("cff17c69-7801-45b6-beb0-2b21dd5da224");
        expectedResult.setPayTimeValue(0);
        expectedResult.setTakeTimeValue(0);
        expectedResult.setAutomaticRefund(0);
        expectedResult.setAutomaticTimeValue(0);

        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Configure HsaMallOrderTimeRuleMapper.selectOne(...).
        final HsaMallOrderTimeRule hsaMallOrderTimeRule = new HsaMallOrderTimeRule();
        hsaMallOrderTimeRule.setId(0L);
        hsaMallOrderTimeRule.setGuid("aedecc65-dc9b-4f6f-8a72-6f66c3c34712");
        hsaMallOrderTimeRule.setOperSubjectGuid("operSubjectGuid");
        hsaMallOrderTimeRule.setPayTimeValue(0);
        hsaMallOrderTimeRule.setTakeTimeValue(0);
        when(mockHsaMallOrderTimeRuleMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(hsaMallOrderTimeRule);

        // Run the test
        final MallOrderTimeRuleVO result = hsaMallOrderTimeRuleServiceImplUnderTest.getMallOrderTimeRule();

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }
}
