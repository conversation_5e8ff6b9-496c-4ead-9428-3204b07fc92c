spring:
  cloud:
    nacos:
      discovery:
        server-addr: holder-member-nacos
        namespace: 4e5d9d20-c230-4007-ac4b-16dbe8f6c8de
#        group: DEVELOP_GROUP
      config:
        server-addr: holder-member-nacos
        namespace: 4e5d9d20-c230-4007-ac4b-16dbe8f6c8de
#        group: DEVELOP_GROUP
        file-extension: yaml
        shared-configs:
          - data-id: datasource-commom.yaml # 配置文件名-Data Id
            #           group: DEVELOP_GROUP   # 默认为DEFAULT_GROUP
            refresh: true   # 是否动态刷新，默认为false
          - data-id: common-feign.yaml
            refresh: true