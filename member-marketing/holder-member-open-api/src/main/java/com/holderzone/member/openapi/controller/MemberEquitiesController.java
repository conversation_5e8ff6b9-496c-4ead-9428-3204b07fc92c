package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.enums.equities.EquitiesSourceTypeEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.vo.grade.GradeEquitiesPreviewVO;
import com.holderzone.member.common.vo.grade.GradePreviewVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.common.vo.equities.EquitiesInfoDetailVO;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * 权益api
 * <AUTHOR>
 * @version 1.0, 2025/5/13
 */
@RestController
@RequestMapping("/equities")
@Slf4j
public class MemberEquitiesController {

    @Resource
    private MemberBaseFeign memberBaseFeign;




    /**
     * 根据会员等级id获取权益弹窗预览信息
     * @param memberGradeGuid 会员等级id
     * @param memberInfoGuid 会员guid
     * @return 权益基础信息列表
     */
    @GetMapping ("/preview/info")
    public Result<GradePreviewVO> getGradeEquitiesPreview(@RequestParam("memberGradeGuid") String memberGradeGuid,
                                                          @RequestParam("memberInfoGuid") String memberInfoGuid) {
        return memberBaseFeign.getEquitiesPreviewInfo(memberGradeGuid, memberInfoGuid, EquitiesSourceTypeEnum.MEMBER_GRADE.getDes(), 0);
    }

    /**
     * 通过guid获取权益详情
     * @param guid 权益guid
     * @return 权益详情
     */
    @GetMapping("/info")
    public Result<EquitiesInfoDetailVO> getEquitiesInfoByGuid(@RequestParam("equitiesGuid") String guid) {
        return memberBaseFeign.getEquitiesInfoByGuid(guid);
    }

}
