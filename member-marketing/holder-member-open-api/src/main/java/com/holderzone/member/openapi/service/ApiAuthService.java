package com.holderzone.member.openapi.service;

import javax.servlet.http.HttpServletRequest;

/**
 * api认证服务接口
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
public interface ApiAuthService {
    /**
     * 验证api请求签名
     * @param request HTTP请求
     * @return 验证是否通过
     */
    boolean verifySignature(HttpServletRequest request);
    
    /**
     * 生成签名（用于给第三方系统提供调用示例）
     * @param appId 应用ID
     * @param secretKey 密钥
     * @param timestamp 时间戳（毫秒）
     * @return 生成的签名
     */
    String generateSignature(String appId, String secretKey, String timestamp);
} 