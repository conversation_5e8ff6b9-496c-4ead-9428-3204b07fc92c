package com.holderzone.member.openapi.config;

import com.holderzone.member.openapi.interceptor.ApiAuthInterceptor;
import com.holderzone.member.openapi.interceptor.UserInfoInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 配置类
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {
    private final UserInfoInterceptor userInfoInterceptor;
    private final ApiAuthInterceptor apiAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加api认证拦截器
        registry.addInterceptor(apiAuthInterceptor)
                .addPathPatterns("/**");

        // 添加userInfoInterceptor
        registry.addInterceptor(userInfoInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/auth/**");
    }
}
