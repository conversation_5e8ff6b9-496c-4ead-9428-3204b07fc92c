package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.card.MemberCardOpenDTO;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.enums.member.PayWayEnum;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.*;
import com.holderzone.member.common.qo.equities.MemberCalculatePreMoneyQO;
import com.holderzone.member.common.vo.card.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 会员卡api
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@RestController
@RequestMapping("/card")
@Slf4j
public class MemberCardController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 查询支付会员卡
     *
     * @param dto 请求参数
     * @return 会员卡列表
     */
    @PostMapping("/payList")
    public Result<List<PayMemberCardVO>> listPayMemberCard(@RequestBody MemberCardQueryDTO dto) {
        return memberBaseFeign.listPayMemberCard(dto);
    }

    /**
     * 查询我的会员卡
     *
     * @param qo 请求参数
     * @return 会员卡列表
     */
    @PostMapping("/myList")
    public Result<List<MiniProgramCardDTO>> listMyMemberCard(@RequestBody ListMiniProgramCardQO qo) {
        qo.setOperSubjectGuid(ThreadLocalCache.getOperSubjectGuid());
        return memberBaseFeign.listMyMemberCard(qo);
    }

    @ApiOperation("查询所有可开通得电子会员卡")
    @GetMapping("/ableECards")
    public Result<List<AbleECardVO>> ableECards() {
        return memberBaseFeign.listAllAbleECardByChannel();
    }

    /**
     * 查询会员卡发卡状态
     *
     * @param dto 请求参数
     * @return 会员卡详情
     */
    @PostMapping("/sendStatus")
    public Result<Integer> getSendStatus(@RequestBody MemberCardQueryDTO dto) {
        return memberBaseFeign.queryOpenCardRuleStatus(dto.getCardGuid());
    }

    /**
     * 开通会员卡
     *
     * @param dto 请求参数
     * @return 开卡结果
     */
    @PostMapping(value = "/openCard")
    public Result<AppletOpenCardVO> openCard(@RequestBody MemberCardOpenDTO dto) {
        return memberBaseFeign.openCard(dto);
    }

    /**
     * 开通会员卡（校验）
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    @PostMapping(value = "/openCardPayCheck")
    public Result<Void> openCardPayCheck(@RequestBody MemberCardOpenCardPayCheckDTO dto) {
        return memberBaseFeign.openCardPayCheck(dto);
    }

    /**
     * 开通会员卡（付费）
     *
     * @param dto 请求参数
     * @return 订单号
     */
    @PostMapping(value = "/openCardPay")
    public Result<String> openCardPay(@RequestBody MemberCardOpenCardPayDTO dto) {
        return memberBaseFeign.openCardPay(dto);
    }

    /**
     * 查询会员卡详情
     *
     * @param dto 请求参数
     * @return 会员卡详情
     */
    @PostMapping("/info")
    public Result<AppletMemberCardDetail> getMemberCardInfo(@RequestBody MemberCardInfoQueryDTO dto) {
        return memberBaseFeign.getMemberCardInfo(dto);
    }

    /**
     * 查询会员卡余额合计
     *
     * @param dto 请求参数
     * @return 余额合计
     */
    @PostMapping("/balanceTotal")
    public Result<BalanceDetailTotalVO> getMemberCardBalanceTotal(@RequestBody MemberCardBalanceQueryDTO dto) {
        AppletBalanceDetailQO qo = new AppletBalanceDetailQO();
        qo.setCardGuid(dto.getMemberInfoCardGuid());
        qo.setMemberGuid(dto.getMemberInfoGuid());
        return memberBaseFeign.getMemberCardBalanceTotal(qo);
    }

    /**
     * 查询会员卡余额记录
     *
     * @param dto 请求参数
     * @return 余额合计
     */
    @PostMapping("/balanceRecord")
    public Result<PageResult> getMemberCardBalanceRecord(@RequestBody MemberCardBalanceQueryDTO dto) {
        AppletBalanceDetailQO qo = new AppletBalanceDetailQO();
        qo.setCardGuid(dto.getMemberInfoCardGuid());
        qo.setMemberGuid(dto.getMemberInfoGuid());
        qo.setCurrentPage(dto.getCurrentPage());
        qo.setPageSize(dto.getPageSize());
        return memberBaseFeign.getMemberCardBalanceRecord(qo);
    }

    /**
     * 查询会员卡二维码
     *
     * @param dto 请求参数
     * @return 会员卡二维码
     */
    @PostMapping("/qrcode")
    public Result<MemberCardQrCodeVO> getQrCode(@RequestBody MemberCardInfoQueryDTO dto) {
        return memberBaseFeign.getQrCode(dto);
    }

    /**
     * 修改会员卡密码
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping("/updatePwd")
    public Result<Boolean> updatePwd(@RequestBody MemberCardPwdUpdateDTO dto) {
        return memberBaseFeign.updatePwd(dto);
    }

    /**
     * 修改默认会员卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/updateDefault")
    public Result<Boolean> updateDefault(@RequestBody MemberCardDefaultUpdateDTO dto) {
        return memberBaseFeign.updateDefault(dto);
    }

    /**
     * 绑定实体卡
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/bindPhysicalCard")
    public Result<PhysicalCardResultVO> bindPhysicalCard(@RequestBody MemberCardBindPhysicalDTO dto) {
        BindingPhysicalCardQO bindingPhysicalCardQO = new BindingPhysicalCardQO();
        bindingPhysicalCardQO.setMemberCardGuid(dto.getMemberInfoCardGuid());
        bindingPhysicalCardQO.setMemberInfoGuid(dto.getMemberInfoGuid());
        bindingPhysicalCardQO.setCardNum(dto.getCardNum());
        bindingPhysicalCardQO.setCardBindingNum(dto.getCardBindingNum());
        return memberBaseFeign.bindPhysicalCard(bindingPhysicalCardQO);
    }

    /**
     * 会员卡充值页面
     *
     * @param dto 请求参数
     * @return 充值页面
     */
    @PostMapping(value = "/rechargePage")
    public Result<CardWeChatRechargeDataVO> rechargePage(@RequestBody MemberCardInfoQueryDTO dto) {
        return memberBaseFeign.rechargePage(dto.getMemberInfoCardGuid(), dto.getStoreGuid());
    }

    /**
     * 计算预计到账金额
     *
     * @param preMoneyQO 请求参数
     * @return 查询结果
     */
    @PostMapping(value = "/calculatePreMoney")
    public Result<RechargeThresholdVO> calculatePreMoney(@RequestBody MemberCalculatePreMoneyQO preMoneyQO) {
        return memberBaseFeign.calculatePreMoney(preMoneyQO);
    }

    /**
     * 会员卡充值
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/recharge")
    public Result<RechargeRespVO> recharge(@RequestBody MemberCardRechargeDTO dto) {
        TerMemberCardRechargeQO qo = new TerMemberCardRechargeQO();
        qo.setOrderNumber(dto.getOrderNumber());
        qo.setMemberInfoGuid(dto.getMemberInfoGuid());
        qo.setMemberInfoCardGuid(dto.getMemberInfoCardGuid());
        qo.setRechargeMoney(dto.getRechargeMoney());
        qo.setPayWay(Optional.ofNullable(dto.getPayWay()).orElse(PayWayEnum.WE_CHANT_PAY.getCode()));
        qo.setCardType(NumberConstant.NUMBER_1);
        qo.setStoreGuid(dto.getStoreGuid());
        qo.setStoreName(dto.getStoreName());
        return memberBaseFeign.recharge(qo);
    }

    /**
     * 会员卡充值-现金/其他支付方式
     *
     * @param dto 请求参数
     * @return 修改结果
     */
    @PostMapping(value = "/cash_recharge")
    public Result<RechargeRespVO> cashRecharge(@RequestBody MemberCardRechargeDTO dto) {
        TerMemberCardRechargeQO qo = new TerMemberCardRechargeQO();
        qo.setMemberInfoGuid(dto.getMemberInfoGuid());
        qo.setMemberInfoCardGuid(dto.getMemberInfoCardGuid());
        qo.setRechargeMoney(dto.getRechargeMoney());
        qo.setPayWay(Optional.ofNullable(dto.getPayWay()).orElse(PayWayEnum.CASH_PAY.getCode()));
        qo.setPayName(dto.getPayName());
        qo.setCardType(NumberConstant.NUMBER_1);
        qo.setStoreGuid(dto.getStoreGuid());
        qo.setStoreName(dto.getStoreName());
        return memberBaseFeign.cashRecharge(qo);
    }

}
