package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.member.MemberCardPayCallbackDTO;
import com.holderzone.member.common.dto.member.MemberCardPayDTO;
import com.holderzone.member.common.dto.member.MemberCardPwdCheckDTO;
import com.holderzone.member.common.dto.member.MemberCardRefundDTO;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.RequestConfirmPayVO;
import com.holderzone.member.common.qo.card.RequestMemberCardPayVO;
import com.holderzone.member.common.qo.card.RequestMemberOrderRefundVO;
import com.holderzone.member.common.vo.card.ConsumptionRespVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 支付api
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@RestController
@RequestMapping("/pay")
public class PayController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 会员卡密码校验
     *
     * @param dto 请求参数
     * @return 校验结果
     */
    @PostMapping("/memberCardPwdCheck")
    public Result<Boolean> memberCardPwdCheck(@RequestBody MemberCardPwdCheckDTO dto) {
        return memberBaseFeign.memberCardPwdCheck(dto);
    }

    /**
     * 会员卡支付回调
     *
     * @param dtoList 请求参数
     * @return 操作结果
     */
    @PostMapping("/memberCardPayCallback")
    public Result<Boolean> memberCardPayCallback(@RequestBody List<MemberCardPayCallbackDTO> dtoList) {
        return memberBaseFeign.memberCardPayCallback(dtoList);
    }

    /**
     * 会员卡支付
     *
     * @param payDTO 请求参数
     * @return 操作结果
     */
    @PostMapping("/member_card_pay")
    public Result<ConsumptionRespVO> memberCardPay(@RequestBody RequestMemberCardPayVO payDTO) {
        return memberBaseFeign.memberCardPay(payDTO);
    }


    /**
     * 会员支付记录
     *
     */
    @ApiOperation("会员支付记录")
    @PostMapping("/cash_pay_order")
    public Result<ConsumptionRespVO> cashPayOrder(@RequestBody RequestConfirmPayVO request) {
        return memberBaseFeign.cashPayOrder(request);
    }


    /**
     * 会员卡退款
     *
     * @param payDTO 请求参数
     * @return 操作结果
     */
    @PostMapping("/member_order_refund")
    public Result<ConsumptionRespVO> memberOrderRefund(@RequestBody RequestMemberOrderRefundVO payDTO) {
        return memberBaseFeign.memberOrderRefund(payDTO);
    }

    /**
     * 会员卡支付 - 商城
     *
     * @param dtoList 请求参数
     * @return 操作结果
     */
    @PostMapping("/memberCardPay")
    public Result<Boolean> memberCardPay(@RequestBody List<MemberCardPayDTO> dtoList) {
        return memberBaseFeign.memberCardPay(dtoList);
    }

    /**
     * 会员卡退款 - 商城
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/memberCardRefund")
    public Result<Boolean> memberCardRefund(@RequestBody MemberCardRefundDTO dto) {
        return memberBaseFeign.memberCardRefund(dto);
    }

    /**
     * 会员支付记录批量
     *
     * @param dtoList
     * @return
     */
    @PostMapping(value = "/member_order_record_list", produces = "application/json;charset=utf-8")
    public Result<Boolean> memberCardPayRecordRequest(@RequestBody List<MemberCardPayDTO> dtoList) {
        return memberBaseFeign.memberCardPayRecordRequest(dtoList);
    }


}
