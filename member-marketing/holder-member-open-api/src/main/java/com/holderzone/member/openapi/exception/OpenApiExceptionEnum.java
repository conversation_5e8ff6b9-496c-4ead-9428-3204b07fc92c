package com.holderzone.member.openapi.exception;

import lombok.Getter;

/**
 * 自定义常错误码
 */
@Getter
public enum OpenApiExceptionEnum {

    XXX_ERROR(550, "xxx异常"),
    ;

    /**
     * 错误码
     */
    private final int code;

    /**
     * 错误信息
     */
    private final String message;

    OpenApiExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
} 