package com.holderzone.member.openapi.constant;

/**
 * API认证常量
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
public class ApiAuthConstant {
    /**
     * 应用ID请求头
     */
    public static final String APP_ID_HEADER = "App-Id";

    /**
     * 时间戳请求头
     */
    public static final String TIMESTAMP_HEADER = "Timestamp";

    /**
     * 签名请求头
     */
    public static final String SIGNATURE_HEADER = "Signature";

}