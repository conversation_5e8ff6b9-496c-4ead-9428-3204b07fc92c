package com.holderzone.member.openapi.interceptor;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.ThreadLocalCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;

/**
 * 用户信息拦截器
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Slf4j
@Component
public class UserInfoInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userInfo = request.getHeader(FilterConstant.USER_INFO);

        // 获取用户信息
        if (StringUtils.isNotEmpty(userInfo)) {
            userInfo = URLDecoder.decode(userInfo, "UTF-8");
            ThreadLocalCache.put(userInfo);
        }

        return true;
    }
}
