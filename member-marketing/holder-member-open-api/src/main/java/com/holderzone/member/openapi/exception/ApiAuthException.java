package com.holderzone.member.openapi.exception;

import lombok.Getter;

/**
 * API认证异常
 *
 * <AUTHOR>
 * @date 2025/3/27
 */
@Getter
public class ApiAuthException extends RuntimeException {
    private static final long serialVersionUID = 2589938852449007247L;

    private final int code;
    private final ApiAuthExceptionEnum exceptionEnum;
    
    public ApiAuthException(ApiAuthExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
        this.exceptionEnum = exceptionEnum;
    }
    
    public ApiAuthException(String message) {
        super(message);
        this.code = 540;
        this.exceptionEnum = null;
    }
    
    public ApiAuthException(int code, String message) {
        super(message);
        this.code = code;
        this.exceptionEnum = null;
    }
} 