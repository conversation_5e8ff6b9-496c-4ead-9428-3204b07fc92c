package com.holderzone.member.openapi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * API认证配置
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api.auth")
public class ApiAuthProperties {
    /**
     * 是否启用API签名认证
     */
    private Boolean enable = true;
    
    /**
     * API密钥配置, key为appId, value为secretKey
     */
    private Map<String, String> keys;
    
    /**
     * 签名有效时间(秒), 默认10分钟
     */
    private Long expireTime = 600L;

    /**
     * 忽略认证的路径
     */
    private String[] excludePaths;
}