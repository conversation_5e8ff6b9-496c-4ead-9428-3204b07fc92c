package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMallToolFeign;
import com.holderzone.member.common.qo.tool.MessagesSendQO;
import com.holderzone.member.common.qo.tool.MessagesSendbatchQO;
import com.holderzone.member.common.qo.wechat.WeChatConfigInfoQO;
import com.holderzone.member.common.vo.tool.MessagesConfigVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("wechat/message")
@RequiredArgsConstructor
@Slf4j
public class WechatMessageController {
    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private MemberMallToolFeign memberMallToolFeign;

    @ApiOperation("小程序、公众号订阅消息批量发送")
    @PostMapping(value = "/send_batch", produces = "application/json;charset=utf-8")
    public void wechatMessageSendBatch(@RequestBody MessagesSendQO messagesSendQO) {
        memberBaseFeign.wechatMessageSendBatch(new MessagesSendbatchQO(Collections.singletonList(messagesSendQO)));
    }

    @ApiOperation("获取消息集合")
    @PostMapping("/msg/getMessagesConfig")
    public Result<List<MessagesConfigVO>> getMessagesConfig() {
        return memberMallToolFeign.getMessagesConfig();
    }


    @ApiOperation("保存小程序配置")
    @PostMapping("config/save")
    Result<Boolean> saveOrUpdateWeChatConfig(@RequestBody WeChatConfigInfoQO request){
        log.info("保存小程序配置: {}", request);
        return memberMallToolFeign.saveOrUpdateWeChatConfig(request);
    }
}
