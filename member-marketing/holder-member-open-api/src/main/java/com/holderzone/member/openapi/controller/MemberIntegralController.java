package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.growth.AppletGrowthDetailPageQO;
import com.holderzone.member.common.qo.growth.AppletGrowthQO;
import com.holderzone.member.common.qo.integral.MallOrderIntegralQO;
import com.holderzone.member.common.qo.integral.UsableIntegralQO;
import com.holderzone.member.common.vo.integral.AppletIntegralDetailVO;
import com.holderzone.member.common.vo.integral.open.AppletOpenIntegralVO;
import com.holderzone.member.common.vo.integral.open.CalculateSignIntegralVO;
import com.holderzone.member.common.vo.integral.open.IntegralTaskInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员积分api
 */
@RestController
@RequestMapping("/member/integral")
@Slf4j
public class MemberIntegralController {

    @Resource
    private MemberBaseFeign memberBaseFeign;


    /**
     * 获取积分任务详情
     */
    @PostMapping("/get/task_info")
    Result<IntegralTaskInfoVO> getTaskInfo(@RequestBody AppletGrowthQO qo) {
        return memberBaseFeign.getTaskInfo(qo);
    }

    /**
     * 查询积分明细
     */
    @PostMapping("/get/detail")
    Result<PageResult<AppletIntegralDetailVO>> getAppletIntegralDetail(@RequestBody AppletGrowthDetailPageQO qo) {
        return memberBaseFeign.getAppletIntegralDetail(qo);
    }

    /**
     * 小程序查询积分统计
     *
     * @param qo （积分）传递参数
     * @return 查询结果
     */
    @PostMapping("/get/total/detail")
    Result<AppletOpenIntegralVO> getIntegralTotalDetail(@RequestBody AppletGrowthQO qo) {
        return memberBaseFeign.getIntegralTotalDetail(qo);
    }

    /**
     * 计算签到任务 七天赠送积分
     */
    @PostMapping("/calculate/task/day")
    Result<CalculateSignIntegralVO> calculateSignInTaskSevenDayIntegral() {
        return memberBaseFeign.calculateSignInTaskSevenDayIntegral();
    }

    /**
     * 获取会员当前可用积分
     * @param memberInfoGuid 会员guid
     * @return 可用积分
     */
    @GetMapping("/get/usable/integral")
    Result<Integer> getUsableIntegral(@RequestParam(value = "memberInfoGuid") String memberInfoGuid) {
        return memberBaseFeign.getUsableIntegral(new UsableIntegralQO().setMemberInfoGuid(memberInfoGuid));
    }

    /**
     * 商城订单调整积分
     *
     * @param request 调整积分请求参数
     * @return 操作结果
     */
    @PostMapping("/update/member/integral")
    Result<Boolean> updateMemberIntegral(@RequestBody MallOrderIntegralQO request) {
        return memberBaseFeign.updateMemberIntegral(request);
    }
}
