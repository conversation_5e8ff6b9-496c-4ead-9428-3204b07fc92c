package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import lombok.Data;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 时间序列化测试Controller
 * 用于验证Jackson时间序列化配置是否正常工作
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/test")
public class TimeTestController {

    /**
     * 测试时间序列化
     */
    @GetMapping("/time")
    public Result<TimeTestVO> getTimeTest() {
        TimeTestVO vo = new TimeTestVO();
        vo.setCurrentDateTime(LocalDateTime.now());
        vo.setCurrentDate(LocalDate.now());
        vo.setCurrentTime(LocalTime.now());
        vo.setMessage("时间序列化测试");
        return Result.success(vo);
    }

    /**
     * 测试时间反序列化
     */
    @PostMapping("/time")
    public Result<TimeTestVO> postTimeTest(@RequestBody TimeTestVO request) {
        return Result.success(request);
    }

    @Data
    public static class TimeTestVO {
        private String message;
        private LocalDateTime currentDateTime;
        private LocalDate currentDate;
        private LocalTime currentTime;
    }
}
