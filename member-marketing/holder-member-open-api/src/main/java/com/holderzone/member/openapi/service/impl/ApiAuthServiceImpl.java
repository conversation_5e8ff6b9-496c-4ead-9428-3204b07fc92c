package com.holderzone.member.openapi.service.impl;

import com.holderzone.member.openapi.config.ApiAuthProperties;
import com.holderzone.member.openapi.constant.ApiAuthConstant;
import com.holderzone.member.openapi.exception.ApiAuthException;
import com.holderzone.member.openapi.exception.ApiAuthExceptionEnum;
import com.holderzone.member.openapi.service.ApiAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Objects;

/**
 * API认证服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiAuthServiceImpl implements ApiAuthService {

    private final ApiAuthProperties apiAuthProperties;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public boolean verifySignature(HttpServletRequest request) {
        String requestURI = request.getRequestURI();

        // 检查是否需要进行认证
        if (!apiAuthProperties.getEnable()) {
            return true;
        }

        // 检查是否在排除路径中
        if (apiAuthProperties.getExcludePaths() != null) {
            for (String pattern : apiAuthProperties.getExcludePaths()) {
                if (pathMatcher.match(pattern, requestURI)) {
                    return true;
                }
            }
        }

        // 获取请求头中的认证信息
        String appId = request.getHeader(ApiAuthConstant.APP_ID_HEADER);
        String timestamp = request.getHeader(ApiAuthConstant.TIMESTAMP_HEADER);
        String signature = request.getHeader(ApiAuthConstant.SIGNATURE_HEADER);

        // 校验请求头是否完整
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(timestamp) || StringUtils.isBlank(signature)) {
            throw new ApiAuthException(ApiAuthExceptionEnum.MISSING_AUTH_PARAMS);
        }

        // 校验AppId是否存在
        String secretKey = apiAuthProperties.getKeys().get(appId);
        if (StringUtils.isBlank(secretKey)) {
            throw new ApiAuthException(ApiAuthExceptionEnum.INVALID_APP_ID);
        }

        // 校验请求是否已过期
        long currentTime = System.currentTimeMillis() / 1000;
        long requestTime;
        try {
            requestTime = Long.parseLong(timestamp);
        } catch (NumberFormatException e) {
            throw new ApiAuthException(ApiAuthExceptionEnum.INVALID_TIMESTAMP);
        }

        if (Math.abs(currentTime - requestTime) > apiAuthProperties.getExpireTime()) {
            throw new ApiAuthException(ApiAuthExceptionEnum.REQUEST_EXPIRED);
        }

        // 获取请求内容并验证签名
        try {
            String calculatedSignature = generateSignature(appId, secretKey, timestamp);

            if (!Objects.equals(signature, calculatedSignature)) {
                throw new ApiAuthException(ApiAuthExceptionEnum.SIGNATURE_VERIFICATION_FAILED);
            }

            return true;
        } catch (Exception e) {
            log.error("验证签名时发生错误", e);
            throw new ApiAuthException(ApiAuthExceptionEnum.SIGNATURE_VERIFICATION_FAILED);
        }
    }

    @Override
    public String generateSignature(String appId, String secretKey, String timestamp) {
        try {
            // 构造签名字符串，格式：appId + timestamp
            String signStr = appId + timestamp;

            // 使用密钥加盐
            signStr += secretKey;

            // 使用SHA-256进行哈希
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(signStr.getBytes(StandardCharsets.UTF_8));

            // Base64编码
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            log.error("生成签名时发生错误", e);
            return null;
        }
    }
} 