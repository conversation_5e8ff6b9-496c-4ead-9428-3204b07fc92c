package com.holderzone.member.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.log.annotation.LogRecord;
import com.holderzone.log.enums.LogType;
import com.holderzone.log.enums.OperateType;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.*;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.card.TerLoginMemberCardQO;
import com.holderzone.member.common.qo.member.MemberListQO;
import com.holderzone.member.common.vo.card.BaseLoginMemberCardVO;
import com.holderzone.member.common.vo.member.DataItemSetVO;
import com.holderzone.member.common.vo.member.MemberAccountInfoVO;
import com.holderzone.member.common.vo.member.MemberBasicInfoVO;
import com.holderzone.member.common.vo.member.MemberInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员api
 *
 * <AUTHOR>
 * @date 2025/4/7
 */
@RestController
@RequestMapping("/member")
@Slf4j
public class MemberController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 获取会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping("/info")
    @LogRecord(operateType = OperateType.VIEW, extraParams = "#dto.memberGuid + ':' + #dto.phoneNum", eventName = "获取会员信息", logType = LogType.USER_BEHAVIOR)
    public Result<MemberBasicInfoVO> getMemberInfo(@RequestBody MemberQueryDTO dto) {
        return memberBaseFeign.getMemberInfo(dto);
    }

    /**
     * 获取会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping("/getMemberByOpenid")
    public Result<MemberBasicInfoVO> getMemberByOpenid(@RequestBody MemberQueryDTO dto) {
        return memberBaseFeign.getMemberByOpenid(dto);
    }


    /**
     * 登录会员
     */
    @PostMapping("/loginMember")
    public Result<BaseLoginMemberCardVO> loginMember(@RequestBody TerLoginMemberCardQO terLoginMemberCardQO) {
        return memberBaseFeign.loginMemberCard(terLoginMemberCardQO);
    }

    /**
     * 批量获取会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping("/batch/info")
    public Result<List<MemberBasicInfoVO>> batchGetMemberInfo(@RequestBody MemberQueryDTO dto) {
        return memberBaseFeign.batchGetMemberInfo(dto);
    }

    /**
     * 通过会员手机号查询当前运营主体下会员列表
     * 分页列表
     */
    @PostMapping(value = "/getMembersByPhone")
    public Result<PageResult<MemberInfoVO>> getMembersByPhone(@RequestBody MemberListQO qeury) {
        log.info("通过会员手机号查询当前运营主体下会员列表:{}", JacksonUtils.writeValueAsString(qeury));
        return memberBaseFeign.getOperationMemberInfoListByPhone(qeury);
    }

    /**
     * 获取用户账户信息 （优惠券、积分、余额、会员卡、会员等级、成长值）
     *
     * @param dto 请求参数
     * @return 会员账户信息
     */
    @PostMapping("/accountInfo")
    @LogRecord(operateType = OperateType.VIEW, extraParams = "#dto.memberGuid + ':' + #dto.phoneNum", eventName = "获取用户账户信息", logType = LogType.USER_BEHAVIOR)
    public Result<MemberAccountInfoVO> memberAccountInfo(@RequestBody MemberQueryDTO dto) {
        return memberBaseFeign.memberAccountInfo(dto);
    }

    /**
     * 新增会员信息
     *
     * @param dto 请求参数
     * @return 会员信息
     */
    @PostMapping("/add")
    @LogRecord(operateType = OperateType.CREATE, extraParams = "#dto.phoneNum", eventName = "新增会员信息")
    public Result<MemberBasicInfoVO> addMemberInfo(@RequestBody MemberAddDTO dto) {
        return memberBaseFeign.addMemberInfo(dto);
    }

    /**
     * 修改会员信息
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping("/update")
    @LogRecord(operateType = OperateType.EDIT, extraParams = "#dto.memberGuid", eventName = "修改会员信息")
    public Result<Void> updateMemberInfo(@RequestBody MemberUpdateDTO dto) {
        return memberBaseFeign.updateMemberInfo(dto);
    }

    /**
     * 通过运营主体获取对应资料项设置的列表信息
     *
     * @return 资料项列表
     */
    @PostMapping("/memberDataItemList")
    @LogRecord(operateType = OperateType.PAGE_STAY, eventName = "资料项设置的列表")
    public Result<List<DataItemSetVO>> memberDataItemList() {
        return memberBaseFeign.listInfo(ThreadLocalCache.getOperSubjectGuid());
    }

    /**
     * 校验会员资料项
     */
    @PostMapping("/checkUserDataItem")
    @LogRecord(operateType = OperateType.FORM_SUBMIT, extraParams = "#dataItemDTO.memberGuid", eventName = "校验会员资料项")
    public Result<Boolean> checkUserDataItem(@RequestBody CheckDataItemDTO dataItemDTO) {
        log.info("校验会员资料项,{}", JSON.toJSONString(dataItemDTO));
        return Result.success(memberBaseFeign.checkUserDataItem(dataItemDTO));
    }

    /**
     * 获取会员列表（按等级类型：付费或免费）
     *
     * @param gradeType 等级类型 1:付费会员 0:免费会员
     * @return 会员列表
     */
    @PostMapping("/listByGradeType")
    @LogRecord(operateType = OperateType.SEARCH, extraParams = "#gradeType", eventName = "获取会员列表")
    public Result<List<MemberBasicInfoVO>> listMembersByGradeType(@RequestParam("gradeType") Integer gradeType) {
        return memberBaseFeign.listMembersByGradeType(gradeType);
    }

}
