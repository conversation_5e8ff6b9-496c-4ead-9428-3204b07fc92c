package com.holderzone.member.openapi.controller;

import com.holderzone.log.annotation.LogRecord;
import com.holderzone.log.enums.OperateType;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.page.PageResult;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.card.ConsumptionDetailQO;
import com.holderzone.member.common.vo.card.ConsumptionDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员消费记录api
 */
@RestController
@RequestMapping("/member/consumption")
@Slf4j
public class MemberConsumptionController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @PostMapping("/query")
    @LogRecord(operateType = OperateType.SEARCH, extraParams = "#request.memberGuid", eventName = "查询会员消费记录")
    public Result<PageResult<ConsumptionDetailVO>> getConsumptionDetail(@RequestBody ConsumptionDetailQO request) {
        return memberBaseFeign.getConsumptionDetail(request);
    }

}
