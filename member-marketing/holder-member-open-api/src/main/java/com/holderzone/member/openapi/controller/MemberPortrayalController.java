package com.holderzone.member.openapi.controller;

import com.holderzone.log.annotation.LogRecord;
import com.holderzone.log.enums.OperateType;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.member.MemberQueryDTO;
import com.holderzone.member.common.dto.portrayal.MemberPortrayalDetailsDTO;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.vo.portrayal.MemberPortrayalDetailsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员画像api
 *
 */
@RestController
@RequestMapping("/member/portrayal")
@Slf4j
public class MemberPortrayalController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    /**
     * 查询会员画像配置
     */
    @GetMapping("/querySetting")
    @LogRecord(operateType = OperateType.SEARCH, eventName = "查询会员画像配置")
    public Result<MemberPortrayalDetailsVO> querySetting() {
        MemberPortrayalDetailsVO memberPortrayalDetailsVO = memberMarketingFeign.queryApplySetting(ThreadLocalCache.getOperSubjectGuid());
        return Result.success(memberPortrayalDetailsVO);
    }

    /**
     * 查询会员画像
     */
    @PostMapping("/query")
    @LogRecord(operateType = OperateType.SEARCH, extraParams = "#memberQueryDTO.memberGuid", eventName = "查询会员画像")
    public Result<MemberPortrayalDetailsDTO> queryMemberPortrayal(@RequestBody MemberQueryDTO memberQueryDTO) {
        return memberBaseFeign.queryMemberPortrayal(memberQueryDTO);
    }

}
