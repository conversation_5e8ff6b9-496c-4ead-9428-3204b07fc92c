package com.holderzone.member.openapi.controller.settlement;

import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.order.MemberOrderDiscountDTO;
import com.holderzone.member.common.dto.order.SettlementUnLockedDiscountDTO;

import java.util.List;

import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberSettlementFeign;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementApplyOrderDTO;
import com.holderzone.member.common.module.settlement.apply.dto.SettlementOrderLockDTO;
import com.holderzone.member.common.module.settlement.apply.vo.SettlementApplyOrderVO;
import com.holderzone.member.common.module.settlement.apply.vo.show.SettlementApplyOrderShowVO;
import com.holderzone.member.common.qo.card.AfterOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.card.BarkOrderDiscountCallbackQO;
import com.holderzone.member.common.qo.card.CheckMemberCardPayQO;
import com.holderzone.member.common.qo.card.TerOrderCallbackQO;
import com.holderzone.member.common.qo.equities.MemberPriceApplyCommodityQO;
import com.holderzone.member.common.vo.equities.MemberPriceApplyCommodityVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 会员结算台开放接口
 */
@RestController
@Slf4j
@RequestMapping("/member/settlement")
public class SettlementController {

    @Resource
    private MemberSettlementFeign settlementFeign;

    @Resource
    private MemberBaseFeign memberBaseFeign;


    /**
     * 获取结算台优惠列表
     * @param dto 结算台优惠列表查询条件
     * @return 结算台优惠列表
     */
    @PostMapping("/get/discount/list")
    public Result<SettlementApplyOrderShowVO> getDiscountApplyList(@RequestBody @Validated SettlementApplyOrderDTO dto) {
        log.info("获取结算台优惠列表, 入参: {}", JSON.toJSONString(dto));
        return settlementFeign.list(dto);
    }

    /**
     * 获取结算台优惠列表
     * @param dto 结算台优惠列表查询条件
     * @return 结算台优惠列表
     */
    @PostMapping("/get/discount/unableRuleList")
    public Result<SettlementApplyOrderShowVO> getUnableRuleList(@RequestBody @Validated SettlementApplyOrderDTO dto) {
        log.info("获取优惠列表, 入参: {}", JacksonUtils.writeValueAsString(dto));
        return settlementFeign.unableRuleList(dto);
    }

    /**
     * 下单前计算优惠
     *
     * @param dto 订单入参
     * @return 优惠结果对象
     */
    @PostMapping(value = "/discount/calculate")
    Result<SettlementApplyOrderShowVO> discountCalculate(@RequestBody @Validated SettlementApplyOrderDTO dto) {
        log.info("下单前结算台计算优惠, 入参: {}", JSON.toJSONString(dto));
        Result<SettlementApplyOrderShowVO> settlementApplyOrderShowVOResult = settlementFeign.calculate(dto);
        log.info("下单前结算台计算优惠, 返回: {}", JSON.toJSONString(settlementApplyOrderShowVOResult));
        return settlementApplyOrderShowVOResult;
    }

    /**
     * 下单后锁定优惠
     *
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/discount/locked")
    Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementOrderLockDTO dto) {
        log.info("下单后结算台锁定优惠, 入参: {}", JSON.toJSONString(dto));
        return memberBaseFeign.lockedDiscount(dto);
    }

    /**
     * 取消订单、退款：释放优惠
     * 食堂调用的是 ： /accumulation_discount_release_key
     * @param dto 订单参数
     * @return 优惠列表
     */
    @PostMapping(value = "/discount/unLocked")
    Result<Boolean> lockedDiscount(@RequestBody @Validated SettlementUnLockedDiscountDTO dto) {
        log.info("取消订单、退款：释放优惠, 入参: {}", JSON.toJSONString(dto));
        return memberBaseFeign.lockedDiscount(dto);
    }

    /**
     * 订单退款 优惠活动记录回调
     */
    @ApiOperation("订单退款 优惠活动记录回调")
    @PostMapping(value = "/order/bark/discount_callback", produces = "application/json;charset=utf-8")
    Result<Void> barkOrderDiscountCallback(@RequestBody BarkOrderDiscountCallbackQO barkOrderDiscountCallbackQO) {
        log.info("订单退款 优惠活动记录回调, 入参: {}", JSON.toJSONString(barkOrderDiscountCallbackQO));
        memberBaseFeign.barkOrderDiscountCallback(barkOrderDiscountCallbackQO);
        return Result.success();
    }

    /**
     *  订单完成 优惠活动记录回调
     */
    @ApiOperation("优惠活动记录回调")
    @PostMapping(value = "/order/after/discount_callback", produces = "application/json;charset=utf-8")
    Result<Void> afterOrderDiscountCallback(@RequestBody AfterOrderDiscountCallbackQO afterOrderDiscountCallbackQO) {
        log.info("订单完成 优惠活动记录回调, 入参: {}", JSON.toJSONString(afterOrderDiscountCallbackQO));
        memberBaseFeign.afterOrderDiscountCallback(afterOrderDiscountCallbackQO);
        return Result.success();
    }

    /**
     * 消费权益回调
     *
     * @param terOrderCallbackQO memberConsumptionGuid
     */
    @ApiOperation("消费权益回调")
    @PostMapping(value = "/order_rights_callback", produces = "application/json;charset=utf-8")
    Result<Void> payOrderRightsCallback(@RequestBody TerOrderCallbackQO terOrderCallbackQO) {
        log.info("消费权益回调, 入参: {}", JSON.toJSONString(terOrderCallbackQO));
        memberBaseFeign.payOrderRightsCallback(terOrderCallbackQO);
        return Result.success();
    }

    /**
     * 获取会员等级折扣商品
     *
     * @param qo MemberPriceApplyCommodityQO
     * @return MemberPriceApplyCommodityVO
     */
    @ApiOperation("获取会员等级折扣商品")
    @PostMapping(value = "/get/member/price/commodity", produces = "application/json;charset=utf-8")
    Result<MemberPriceApplyCommodityVO> getMemberPriceApplyCommodity(@RequestBody MemberPriceApplyCommodityQO qo) {
        log.info("获取会员等级折扣商品, 入参: {}", JSON.toJSONString(qo));
        return memberBaseFeign.getMemberPriceApplyCommodity(qo);
    }


    /**
     * 计算会员折扣 - 单独计算
     */
    @PostMapping(value = "/calculateMemberDiscount")
    public Result<SettlementApplyOrderVO> calculateMemberDiscount(@RequestBody SettlementApplyOrderDTO settlementApplyOrderDTO) {
        log.info("计算会员折扣, 入参: {}", JSON.toJSONString(settlementApplyOrderDTO));
        return memberBaseFeign.calculateMemberDiscount(settlementApplyOrderDTO);
    }

    /**
     * 会员卡支付 前置校验
     */
    @PostMapping(value = "/preCheckMemberCardPay", produces = "application/json;charset=utf-8")
    public Result<String> preCheckMemberCardPay(@RequestBody CheckMemberCardPayQO checkMemberCardPayQO) {
        log.info("会员卡支付 前置校验, 入参: {}", JSON.toJSONString(checkMemberCardPayQO));
        String result = memberBaseFeign.preCheckMemberCardPay(checkMemberCardPayQO);
        log.info("会员卡支付 前置校验, 返回: {}", JSON.toJSONString(result));
        return Result.success(result);
    }

    /**
     * 查询订单优惠记录列表
     *
     * @param dto 订单锁定参数
     * @return 订单优惠记录列表
     */
    @PostMapping(value = "/discount/listOrderDiscount")
    public Result<List<MemberOrderDiscountDTO>> listOrderDiscount(@RequestBody SettlementOrderLockDTO dto) {
        log.info("查询订单优惠记录列表, 入参: {}", JSON.toJSONString(dto));
        return memberBaseFeign.listOrderDiscount(dto);
    }
}
