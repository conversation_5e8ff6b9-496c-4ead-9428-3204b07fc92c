package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.coupon.SelfReceiveCouponPackageQO;
import com.holderzone.member.common.vo.coupon.CouponPackageSelfDetailsVO;
import com.holderzone.member.common.vo.coupon.SelfReceiveCouponPackageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 发券宝
 *
 * <AUTHOR>
 * @date 2025/07/23
 */
@RestController
@RequestMapping("/coupon_package")
@Slf4j
public class MemberCouponActivityController {

    @Resource
    private MemberMarketingFeign memberMarketingFeign;

    @Resource
    private MemberBaseFeign memberBaseFeign;

    /**
     * 查询自助领券活动详情
     *
     * @param guid 活动GUID
     * @param memberGuid 会员GUID
     * @return 自助领券活动详情
     */
    @GetMapping(value = "/self_details")
    public Result<CouponPackageSelfDetailsVO> getCouponPackageSelfDetails(
            @RequestParam(value = "guid") String guid,
            @RequestParam(value = "memberGuid", required = false) String memberGuid) {
        return memberMarketingFeign.getCouponPackageSelfDetails(guid, memberGuid);
    }

    /**
     * 用户主动领券接口
     *
     * @param request 领券请求参数
     * @return 领券结果
     */
    @PostMapping(value = "/self_receive")
    Result<SelfReceiveCouponPackageVO> selfReceiveCouponPackage(@RequestBody SelfReceiveCouponPackageQO request) {
        return memberBaseFeign.selfReceiveCouponPackage(request);
    }
}