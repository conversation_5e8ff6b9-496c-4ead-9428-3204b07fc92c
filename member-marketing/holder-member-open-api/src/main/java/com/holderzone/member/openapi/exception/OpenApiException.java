package com.holderzone.member.openapi.exception;

import lombok.Getter;


/**
 * 自定义异常
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Getter
public class OpenApiException extends RuntimeException {

    private static final long serialVersionUID = 952738188853063970L;

    private final int code;
    private final OpenApiExceptionEnum exceptionEnum;

    public OpenApiException(OpenApiExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMessage());
        this.code = exceptionEnum.getCode();
        this.exceptionEnum = exceptionEnum;
    }

    public OpenApiException(String message) {
        super(message);
        this.code = 550;
        this.exceptionEnum = null;
    }

    public OpenApiException(int code, String message) {
        super(message);
        this.code = code;
        this.exceptionEnum = null;
    }

}
