package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.member.MemberEverydayRecordQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 会员积分开放接口
 */
@RestController
@Slf4j
@RequestMapping("open/member/everyday/record")
public class HsaMemberEverydayRecordController {

    @Resource
    private MemberBaseFeign memberBaseFeign;


    /**
     * 保存或更新记录
     * @param recordDTO 请求参数 DTO
     * @return 保存或更新后的记录
     */
    @PostMapping("/saveOrUpdate")
    public Result<String> saveOrUpdate(@RequestBody MemberEverydayRecordQO recordDTO) {
        log.info("保存或更新记录: {}", recordDTO);
        // 调用服务层方法保存或更新记录
        return memberBaseFeign.saveOrUpdate(recordDTO);
    }

    @PostMapping("/calculateIntegralCheck")
    Result<Integer> calculateIntegralCheck(@RequestBody MemberEverydayRecordQO recordDTO){
        log.info("计算实际赠送积分值: {}", recordDTO);
        // 调用服务层方法计算实际赠送积分值
        return memberBaseFeign.calculateIntegralCheck(recordDTO);
    }
}
