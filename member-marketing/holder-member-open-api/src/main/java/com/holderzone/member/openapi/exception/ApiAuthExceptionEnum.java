package com.holderzone.member.openapi.exception;

import lombok.Getter;

/**
 * API认证异常错误码
 */
@Getter
public enum ApiAuthExceptionEnum {
    
    /**
     * 认证参数相关错误
     */
    SIGNATURE_VERIFICATION_FAILED(540, "签名验证失败"),
    MISSING_AUTH_PARAMS(541, "缺少认证参数"),
    INVALID_APP_ID(542, "无效的AppId"),
    INVALID_TIMESTAMP(543, "无效的时间戳"),
    REQUEST_EXPIRED(544, "请求已过期"),
    ;

    /**
     * 错误码
     */
    private final int code;
    
    /**
     * 错误信息
     */
    private final String message;

    ApiAuthExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
} 