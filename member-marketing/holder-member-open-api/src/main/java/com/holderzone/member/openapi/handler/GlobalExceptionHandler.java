package com.holderzone.member.openapi.handler;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.exception.ServerException;
import com.holderzone.member.openapi.exception.ApiAuthException;
import com.holderzone.member.openapi.exception.OpenApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * 统一异常处理
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * api认证异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = ApiAuthException.class)
    public Result<Void> handleApiAuthException(ApiAuthException e) {
        log.error(e.getMessage(), e);
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 自定义异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = OpenApiException.class)
    public Result<Void> handleMemberOpenApiException(OpenApiException e) {
        log.error(e.getMessage(), e);
        return Result.error(e.getCode(), e.getMessage());
    }

    /**
     * 自定义异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = ServerException.class)
    public Result<Void> handleServerException(ServerException e) {
        log.error("服务器调用异常，{}", e.getMessage());
        return Result.error("服务器正忙，请稍后再试!");
    }

    /**
     * 系统异常
     *
     * @param e 异常
     * @return 结果集
     */
    @ExceptionHandler(value = Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常，{}", e.getMessage());
        return Result.error("系统异常，请联系管理员!");
    }

}
