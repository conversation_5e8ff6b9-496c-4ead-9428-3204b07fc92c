package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.CardRechargeGiftQO;
import com.holderzone.member.common.vo.gift.RechargeSuccessGiftDetailVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/recharge/gift")
public class RechargeGiftActivityController {

    @Resource
    private MemberBaseFeign memberBaseFeign;

    @GetMapping(value = "/get_recharge_success_gift_detail")
    Result<RechargeSuccessGiftDetailVO> getRechargeSuccessGiftDetail(String orderNumber) {
        return memberBaseFeign.getRechargeSuccessGiftDetail(new CardRechargeGiftQO(orderNumber));
    }
}
