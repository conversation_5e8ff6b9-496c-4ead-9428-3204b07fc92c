package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.openapi.service.ApiAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * API认证测试控制器
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class ApiAuthController {

    private final ApiAuthService apiAuthService;

    /**
     * 生成签名示例接口（用于第三方系统测试签名）
     *
     * @param appId     应用ID
     * @param secretKey 密钥
     * @param timestamp 时间戳（秒）
     * @return 签名结果
     */
    @PostMapping("/signature")
    public Result<Map<String, String>> generateSignature(
            @RequestParam String appId,
            @RequestParam String secretKey,
            @RequestParam String timestamp) {

        String signature = apiAuthService.generateSignature(appId, secretKey, timestamp);

        Map<String, String> result = new HashMap<>();
        result.put("signature", signature);
        result.put("appId", appId);
        result.put("timestamp", timestamp);

        return Result.success(result);
    }
} 