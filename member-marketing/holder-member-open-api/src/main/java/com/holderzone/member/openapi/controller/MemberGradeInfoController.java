package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.grade.GradeCardQueryDTO;
import com.holderzone.member.common.dto.grade.MemberGradeQueryDTO;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.qo.grade.MemberGradePurchaseHistoryQO;
import com.holderzone.member.common.vo.grade.GradeLevelVO;
import com.holderzone.member.common.vo.grade.HsaMemberGradeInfoVO;
import com.holderzone.member.common.vo.grade.MemberGradeCardVO;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 会员等级服务相关接口
 * <AUTHOR>
 * @version 1.0, 2025/4/24
 */
@RestController
@RequestMapping ("/grade")
@Slf4j
public class MemberGradeInfoController {


	@Resource
	private MemberBaseFeign memberBaseFeign;
	/**
	 * 会员等级列表
	 */
	@ApiOperation("会员等级列表")
	@PostMapping("/infoList")
	public Result<List<HsaMemberGradeInfoVO>> queryMemberGradeInfoList(@RequestBody MemberGradeQueryDTO memberGradeQueryDTO) {
		return memberBaseFeign.queryMemberGradeInfoList(memberGradeQueryDTO.getRoleType(), memberGradeQueryDTO.getGradeType());
	}

	/**
	 * 会员等级列表
	 */
	@ApiOperation("会员等级列表")
	@PostMapping("/info")
	public Result<HsaMemberGradeInfoVO> getMemberGradeInfo(@RequestBody MemberGradeQueryDTO memberGradeQueryDTO) {
		if (memberGradeQueryDTO.getGuid() != null) {
			return memberBaseFeign.getMemberGradeInfoByGuid(memberGradeQueryDTO.getGuid());
		}
		return memberBaseFeign.queryMemberGradeInfo(memberGradeQueryDTO.getGradeType(), memberGradeQueryDTO.getVipGrade());
	}

	/**
	 * 新增会员等级购买流水
	 */
	@ApiOperation("新增会员等级购买流水")
	@PostMapping("/addPurchaseHistory")
	public Result<Void> addPurchaseHistory(@RequestBody MemberGradePurchaseHistoryQO qo) {
		return memberBaseFeign.addMemberGradePurchaseHistory(qo);
	}

	/**
	 * 会员等级卡列表
	 */
	@ApiOperation("根据查询条件获取会员等级卡列表")
	@PostMapping("/card/get")
	public Result<List<MemberGradeCardVO>> getGradeCardList(@RequestBody GradeCardQueryDTO gradeCardQueryDTO) {
		return memberBaseFeign.getGradeCardList(gradeCardQueryDTO);
	}

	/**
	 * 更新会员等级卡
	 */
	@ApiOperation("更新会员等级卡")
	@PostMapping("/card/update")
	public Result<Void> updateGradeCard(@RequestBody GradeCardQueryDTO gradeCardQueryDTO) {
		return memberBaseFeign.updateGradeCard(gradeCardQueryDTO);
	}


	@ApiOperation("新增会员等级卡")
	@PostMapping("/card/add")
	public Result<Void> addGradeCard(@RequestBody GradeCardQueryDTO gradeCardQueryDTO) {
		return memberBaseFeign.addGradeCard(gradeCardQueryDTO);
	}

	@ApiOperation("根据memberGuid查询会员当前付费等级")
	@GetMapping(value = "/user/paid/info")
	public Result<GradeLevelVO> member_paid_grade_level(@RequestParam("memberGuid") String memberGuid, @RequestParam(value = "roleType", required = false) String roleType) {
		return memberBaseFeign.getMemberPaidGradeLevel(memberGuid, roleType);
	}

	@ApiOperation("根据memberGuid查询会员当前免费等级信息")
	@GetMapping(value = "/user/free/info")
	public Result<GradeLevelVO> member_free_grade_level(@RequestParam("memberGuid") String memberGuid, @RequestParam(value = "roleType", required = false) String roleType) {
		return memberBaseFeign.getMemberFreeGradeLevel(memberGuid, roleType);
	}
}
