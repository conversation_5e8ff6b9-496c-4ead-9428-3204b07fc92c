package com.holderzone.member.openapi.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.feign.MemberMarketingFeign;
import com.holderzone.member.common.qo.redeem.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


/**
 * 兑换活动控制器
 */
@Slf4j
@RestController
@RequestMapping("/redeem")
@RequiredArgsConstructor
public class RedeemCodeActiveController {

    private final MemberMarketingFeign memberMarketingFeign;

    @PostMapping
    public Result<RespondCheckRedeemApplyVO> redeem(@RequestBody RequestCheckRedeemApplyQO request) {
        return memberMarketingFeign.redeem(request);
    }
}
