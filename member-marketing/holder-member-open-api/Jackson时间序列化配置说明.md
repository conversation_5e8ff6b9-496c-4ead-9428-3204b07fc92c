# Jackson时间序列化配置说明

## 问题背景
外部请求member-open-api模块时，java.time包下的类（如LocalDateTime、LocalDate、LocalTime等）存在序列化报错问题。

## 解决方案
在member-open-api模块中新增了全局的Jackson时间序列化配置类 `JacksonConfig.java`，用于统一处理java.time包下时间类型的序列化和反序列化。

## 配置详情

### 文件位置
```
holder-member-open-api/src/main/java/com/holderzone/member/openapi/config/JacksonConfig.java
```

### 主要功能
1. **全局时间格式统一**：
   - LocalDateTime: `yyyy-MM-dd HH:mm:ss`
   - LocalDate: `yyyy-MM-dd`
   - LocalTime: `HH:mm:ss`

2. **自动序列化/反序列化**：
   - 自动处理请求参数中的时间字段反序列化
   - 自动处理响应结果中的时间字段序列化

3. **覆盖默认配置**：
   - 禁用时间戳格式输出
   - 使用可读的字符串格式

## 使用方法

### 1. 在DTO/VO类中直接使用
```java
public class ExampleVO {
    private LocalDateTime createTime;  // 自动序列化为 "2025-07-22 10:30:00"
    private LocalDate birthDate;      // 自动序列化为 "2025-07-22"
    private LocalTime workTime;       // 自动序列化为 "10:30:00"
}
```

### 2. 特殊格式需求
如果某个字段需要特殊格式，仍可使用注解覆盖：
```java
@JsonFormat(pattern = "yyyy/MM/dd")
private LocalDate specialDate;
```

## 测试验证

### 测试接口
创建了测试Controller `TimeTestController.java` 用于验证配置是否正常工作：

- GET `/test/time` - 测试时间序列化输出
- POST `/test/time` - 测试时间反序列化输入

### 预期结果
```json
{
  "code": 200,
  "data": {
    "message": "时间序列化测试",
    "currentDateTime": "2025-07-22 10:30:00",
    "currentDate": "2025-07-22",
    "currentTime": "10:30:00"
  }
}
```

## 注意事项

1. **优先级**：全局配置的优先级低于字段级别的@JsonFormat注解
2. **兼容性**：配置向后兼容，不会影响已有的@JsonFormat注解
3. **作用范围**：仅对member-open-api模块生效
4. **时区**：使用系统默认时区

## 相关文件
- 配置类：`JacksonConfig.java`
- 测试类：`TimeTestController.java`
- 启动类：`HolderMemberOpenApiApplication.java`（自动扫描配置）

## 验证方法
1. 启动member-open-api服务
2. 访问 `/test/time` 接口查看序列化结果
3. 使用POST请求测试反序列化功能
4. 检查现有接口的时间字段输出格式
