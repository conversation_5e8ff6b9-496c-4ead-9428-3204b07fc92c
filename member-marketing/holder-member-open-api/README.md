# Holder Member OpenAPI

Holder Member OpenAPI 是一个基于 Spring Boot 的开放 API 认证服务，用于保护和管理 API 接口的访问权限。

## 功能特性

- API 认证开关控制
- 请求路径白名单配置
- 基于 AppId 和 SecretKey 的认证机制
- 请求时效性验证
- 签名验证机制

## 认证流程

1. 请求头中必须包含以下信息：
   - `X-App-Id`: 应用标识
   - `X-Timestamp`: 请求时间戳（秒）
   - `X-Signature`: 请求签名

2. 签名生成规则：
   - 签名字符串格式：`appId + timestamp + secretKey`
   - 使用 SHA-256 算法进行哈希
   - 使用 Base64 编码

3. 认证验证流程：
   - 检查认证开关是否开启
   - 检查请求路径是否在白名单中
   - 验证请求头参数完整性
   - 验证 AppId 有效性
   - 验证请求时间戳有效性
   - 验证请求是否过期
   - 验证签名是否正确

## 配置说明

在 `application.yml` 中配置以下参数：

```yaml
holder:
  openapi:
    auth:
      enable: true  # 是否启用API认证
      expire-time: 300  # 请求过期时间（秒）
      exclude-paths:  # 排除认证的路径
        - /api/v1/public/**
      keys:  # AppId和SecretKey配置
        app1: "secret1"
        app2: "secret2"
```

## 异常说明

系统定义了以下异常类型：

- `MISSING_AUTH_PARAMS`: 缺少认证参数
- `INVALID_APP_ID`: 无效的AppId
- `INVALID_TIMESTAMP`: 无效的时间戳
- `REQUEST_EXPIRED`: 请求已过期
- `SIGNATURE_VERIFICATION_FAILED`: 签名验证失败
- `SIGNATURE_GENERATION_FAILED`: 生成签名失败

## 使用示例

1. 生成签名示例：

```java
String signature = apiAuthService.generateSignature(appId, secretKey, timestamp);
```

2. 验证签名示例：

```java
boolean isValid = apiAuthService.verifySignature(request);
```

## 注意事项

1. 时间戳使用秒级时间戳
2. 请求过期时间默认 300 秒
3. 签名验证失败会抛出 ApiAuthException 异常
4. 建议在生产环境中启用认证机制
5. 定期更换 SecretKey 以提高安全性 