package com.holderzone.member.gateway.filter;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.member.AccountStateEnum;
import com.holderzone.member.common.enums.member.MemberAccountExceptionEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.gateway.client.OperatingSubjectService;
import com.holderzone.member.gateway.config.WhiteListConfig;
import com.holderzone.member.gateway.utils.JwtParserUtil;
import com.holderzone.member.gateway.utils.VerifyUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 接口请求过滤
 * @date 2021/8/12
 */
@Slf4j
@Component
public class HttpRequestFilter extends BaseFilter implements GlobalFilter, Ordered {

    @Resource
    private JwtParserUtil jwtParserUtil;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private WhiteListConfig whiteListConfig;

    @Resource
    private OperatingSubjectService operatingSubjectService;

    private static final String USER_INFO_CACHE_KEY = "userinfo_%s:%s";

    private static final int CACHE_DURATION = 30;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        HttpHeaders headers = request.getHeaders();
        String url = request.getPath().toString();

        // 构建用户信息对象
        HeaderUserInfo headerUserInfo = buildHeaderUserInfo(headers);
        log.info("Processing request - url:{}, userInfo:{}", url, headerUserInfo);

        try {
            // 前置处理
            Mono<Void> preCheckResponse = beforeHandle(exchange, chain, headerUserInfo);
            if (preCheckResponse != null) {
                return preCheckResponse;
            }

            // 处理请求来源
            if (VerifyUtil.isAppletRequest(headerUserInfo.getSource())) {
                return handleAppletRequest(exchange, chain, headerUserInfo);
            } else {
                return handleNormalRequest(exchange, chain, headerUserInfo);
            }
        } catch (Exception e) {
            log.error("Error processing request for url: {}", url, e);
            return unAuthenResponse(exchange, FilterConstant.ERROR_SUBJECT_FAIL);
        }
    }

    private HeaderUserInfo buildHeaderUserInfo(HttpHeaders headers) {
        return HeaderUserInfo.builder()
                .operSubjectGuid(getHeaderFistValue(headers, FilterConstant.OPER_SUBJECT_GUID))
                .source(Optional.ofNullable(getHeaderFistValue(headers, FilterConstant.SOURCE))
                        .map(Integer::parseInt)
                        .orElse(null))
                .enterpriseGuid(getEnterpriseGuid(headers))
                .system(ObjectUtil.objToInt(getHeaderFistValue(headers, FilterConstant.SYSTEM_TYPE)))
                .token(getHeaderFistValue(headers, FilterConstant.TOKEN))
                .build();
    }

    private Mono<Void> handleAppletRequest(ServerWebExchange exchange, GatewayFilterChain chain, HeaderUserInfo headerUserInfo) {
        String memberGuid = jwtParserUtil.getMemberGuid(headerUserInfo.getToken(), headerUserInfo.getSource());

        if (memberGuid == null) {
            return unAuthenResponse(exchange, FilterConstant.ERROR_AUTH_OUT);
        }

        HeaderUserInfo memberInfo = jwtParserUtil.getMember(memberGuid);
        if (isAccountState(memberInfo)) {
            return errorResponse(exchange, MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED);
        }
        headerUserInfo.setUserGuid(memberInfo.getUserGuid());
        headerUserInfo.setUserName(memberInfo.getUserName());
        headerUserInfo.setTel(memberInfo.getTel());
        return chainFilter(exchange, chain, headerUserInfo);
    }

    private Mono<Void> handleNormalRequest(ServerWebExchange exchange, GatewayFilterChain chain, HeaderUserInfo headerUserInfo) {
        try {
            HeaderUserInfo userInfo = getUserInformation(headerUserInfo,
                    headerUserInfo.getToken(),
                    headerUserInfo.getSource(),
                    headerUserInfo.getOperSubjectGuid());

            afterHandleField(userInfo,
                    headerUserInfo.getEnterpriseGuid(),
                    headerUserInfo.getOperSubjectGuid(),
                    headerUserInfo.getSource(),
                    headerUserInfo.getSystem());

            return chainFilter(exchange, chain, userInfo);
        } catch (Exception e) {
            log.error("Error handling normal request", e);
            return unAuthenResponse(exchange, FilterConstant.ERROR_SUBJECT_FAIL);
        }
    }

    private static boolean isAccountState(HeaderUserInfo headerUserInfo) {
        return !Objects.isNull(headerUserInfo.getAccountState()) && headerUserInfo.getAccountState() == AccountStateEnum.FREEZE.getCode();
    }

    private Mono<Void> chainFilter(ServerWebExchange exchange, GatewayFilterChain chain, HeaderUserInfo headerUserInfo) {
        ServerHttpRequest request = getNewHttpRequest(exchange, headerUserInfo);
        return chain.filter(exchange.mutate().request(request).build());
    }

    private HeaderUserInfo getUserInformation(HeaderUserInfo headerUserInfo, String token, Integer source, String operSubjectGuid) {
        // 处理小程序请求
        if (isWechatMiniProgram(source)) {
            return getWechatMiniProgramUserInfo(headerUserInfo, token);
        }

        // 尝试从缓存获取用户信息
        String userKey = generateUserCacheKey(headerUserInfo.getEnterpriseGuid(), token);
        HeaderUserInfo cachedUserInfo = getCachedUserInfo(userKey, headerUserInfo, operSubjectGuid, source);
        if (cachedUserInfo != null) {
            return cachedUserInfo;
        }

        // 查询并缓存用户信息
        return queryAndCacheUserInfo(headerUserInfo, token, operSubjectGuid, source, userKey);
    }

    private boolean isWechatMiniProgram(Integer source) {
        return source == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode();
    }

    private HeaderUserInfo getWechatMiniProgramUserInfo(HeaderUserInfo headerUserInfo, String token) {
        String memberGuid = jwtParserUtil.getMemberGuid(token, FilterConstant.MEMBER_APPLET);
        HeaderUserInfo member = jwtParserUtil.getMember(memberGuid);
        headerUserInfo.setUserGuid(memberGuid);
        headerUserInfo.setUserName(member.getUserName());
        headerUserInfo.setTel(member.getTel());
        return headerUserInfo;
    }

    private String generateUserCacheKey(String enterpriseGuid, String token) {
        return String.format(USER_INFO_CACHE_KEY,
                Optional.ofNullable(enterpriseGuid).orElse(""),
                token);
    }

    private HeaderUserInfo getCachedUserInfo(String userKey, HeaderUserInfo headerUserInfo,
                                             String operSubjectGuid, Integer source) {
        Object userObj = redisTemplate.opsForValue().get(userKey);
        if (userObj != null) {
            log.info("从缓存获取用户信息 - token:{}, result:{}", headerUserInfo.getToken(), JSON.toJSONString(userObj));
            HeaderUserInfo cachedInfo = JSONUtil.toBean(userObj.toString(), HeaderUserInfo.class);
            if (!StringUtils.isEmpty(cachedInfo.getUserGuid())) {
                cachedInfo.setOperSubjectGuid(operSubjectGuid);
                cachedInfo.setSource(source);
                return cachedInfo;
            }
        }
        return null;
    }

    private HeaderUserInfo queryAndCacheUserInfo(HeaderUserInfo headerUserInfo, String token,
                                                 String operSubjectGuid, Integer source, String userKey) {
        HeaderUserInfo userInfo = operatingSubjectService.queryUserInformation(headerUserInfo, token);
        if (userInfo != null) {
            updateUserInfo(headerUserInfo, userInfo);
        }

        headerUserInfo.setOperSubjectGuid(operSubjectGuid);
        headerUserInfo.setSource(source);

        // 缓存用户信息
        redisTemplate.opsForValue().set(userKey, JSONUtil.toJsonStr(headerUserInfo), CACHE_DURATION, TimeUnit.MINUTES);
        return headerUserInfo;
    }

    private void updateUserInfo(HeaderUserInfo headerUserInfo, HeaderUserInfo userInfo) {
        headerUserInfo.setUserGuid(userInfo.getUserGuid());
        headerUserInfo.setUserName(userInfo.getUserName());
        headerUserInfo.setEnterpriseName(userInfo.getEnterpriseName());
        headerUserInfo.setTel(userInfo.getTel());
        headerUserInfo.setUserAccount(userInfo.getUserAccount());
    }

    private ServerHttpRequest getNewHttpRequest(ServerWebExchange exchange, HeaderUserInfo userInfo) {
        return exchange.getRequest().mutate().headers(httpHeaders -> {
            try {
                httpHeaders.set(FilterConstant.USER_INFO, URLEncoder.encode(JSON.toJSONString(userInfo), "utf-8"));
            } catch (UnsupportedEncodingException e) {
                log.error("userInfo 添加到httpHeaders出错", e);
            }
        }).build();
    }

    @Override
    public int getOrder() {
        return -200;
    }

    /**
     * 校验token是否存在
     *
     * @param exchange
     * @return
     */
    @Override
    public String validateTokenExist(ServerWebExchange exchange) {
        List<String> tokenList = exchange.getRequest().getHeaders().get(FilterConstant.TOKEN);
        if (tokenList == null || tokenList.isEmpty()) {
            return "";
        }
        return tokenList.stream().findFirst().orElse(null);
    }

    /**
     * 校验主体是否存在
     *
     * @param exchange
     * @return
     */
    public String validateOperSubjectGuidExist(ServerWebExchange exchange) {
        List<String> operSubjectGuidList = exchange.getRequest().getHeaders().get(FilterConstant.OPER_SUBJECT_GUID);
        if (operSubjectGuidList == null || operSubjectGuidList.isEmpty()) {
            return "";
        }
        return operSubjectGuidList.stream().findFirst().orElse(null);
    }

    /**
     * 从header中获取source
     *
     * @param exchange
     * @return
     */
    @Override
    public Integer retrieveSourceFromHeader(ServerWebExchange exchange) {
        List<String> sourceList = exchange.getRequest().getHeaders().get(FilterConstant.SOURCE);
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        return Integer.valueOf(sourceList.get(0));
    }

    /**
     * 获取企业guid
     *
     * @return 企业guid
     */
    public String getEnterpriseGuid(HttpHeaders headers) {
        //优先取
        final String value = getHeaderFistValue(headers, FilterConstant.ENTERPRISE_GUID);
        if (StringUtils.isEmpty(value)) {

            //没有就取
            return getHeaderFistValue(headers, FilterConstant.ODOO_ENTERPRISE_GUID);
        }
        return value;
    }

    public String getHeaderFistValue(HttpHeaders headers, String key) {
        return Optional.ofNullable(headers.get(key)).map(h -> h.get(0)).orElse(null);
    }

    /**
     * 认证失败返回结果
     *
     * @param exchange
     * @return
     */
    public Mono<Void> unAuthenResponse(ServerWebExchange exchange, String message) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result result = Result.error(HttpStatus.UNAUTHORIZED.value(), message);
        byte[] resultBytes = JacksonUtils.toJsonByte(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

    /**
     * 异常返回结果
     *
     * @param exchange ServerWebExchange
     * @return 返回信息
     */
    public Mono<Void> errorResponse(ServerWebExchange exchange, ResponseBase responseBase) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result<Objects> result = Result.error(responseBase.getCode(), responseBase.getDes());
        byte[] resultBytes = JacksonUtils.toJsonByte(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

    private Mono<Void> beforeHandle(ServerWebExchange exchange, GatewayFilterChain chain, HeaderUserInfo headerUserInfo) {
        RequestContext context = buildRequestContext(exchange, headerUserInfo);

        // 初始化主体数据
        initializeSubject(context);

        // 检查白名单和基础验证
        Mono<Void> basicCheckResult = performBasicChecks(exchange, chain, context);
        if (basicCheckResult != null) {
            return basicCheckResult;
        }
        // 处理特殊路径
        Mono<Void> specialPathResult = handleSpecialPaths(exchange, chain, context);
        if (specialPathResult != null) {
            return specialPathResult;
        }

        // Token 相关验证
        return handleTokenValidation(exchange, chain, context);
    }

    @Data
    @AllArgsConstructor
    private static class RequestContext {
        final String url;
        final String token;
        final Integer source;
        final String operSubjectGuid;
        final Integer system;
        final String enterpriseGuid;
        HeaderUserInfo headerUserInfo;
    }

    private RequestContext buildRequestContext(ServerWebExchange exchange, HeaderUserInfo headerUserInfo) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        return new RequestContext(
                exchange.getRequest().getPath().toString(),
                getHeaderFistValue(headers, FilterConstant.TOKEN),
                headerUserInfo.getSource(),
                headerUserInfo.getOperSubjectGuid(),
                headerUserInfo.getSystem(),
                headerUserInfo.getEnterpriseGuid(),
                headerUserInfo
        );
    }

    private void initializeSubject(RequestContext context) {
        operatingSubjectService.initSubjectData(context.system, context.operSubjectGuid, context.enterpriseGuid);
        if (context.system == SystemEnum.RETAIL.getCode()) {
            context.headerUserInfo.setToken(context.token);
        }
    }

    private Mono<Void> performBasicChecks(ServerWebExchange exchange, GatewayFilterChain chain, RequestContext context) {
        // 白名单检查
        if (whiteListConfig.withoutAll(context.url)) {
            return chainFilter(exchange, chain, context.headerUserInfo);
        }

        // 主体状态检查
        if (isSubjectDisabled(context)) {
            return unAuthenResponse(exchange, FilterConstant.ERROR_SUBJECT_MEMBER_STOP);
        }

        // 来源检查
        if (Objects.isNull(context.source)) {
            return unAuthenResponse(exchange, FilterConstant.ERROR_SOURCE_AUTH_OUT);
        }

        return null;
    }

    private boolean isSubjectDisabled(RequestContext context) {
        return !StringUtils.isEmpty(context.operSubjectGuid) &&
                checkSubjectState(context.headerUserInfo, context.source, context.operSubjectGuid);
    }

    private Mono<Void> handleSpecialPaths(ServerWebExchange exchange, GatewayFilterChain chain, RequestContext context) {
        // 营销中心路径处理
        if (context.url.startsWith(FilterConstant.MARKETING_URL)) {
            if (VerifyUtil.isAppletRequest(context.source)) {
                String memberGuid = jwtParserUtil.getMemberGuid(context.token, context.source);

                if (memberGuid == null) {
                    return unAuthenResponse(exchange, FilterConstant.ERROR_AUTH_OUT);
                }

                HeaderUserInfo memberInfo = jwtParserUtil.getMember(memberGuid);
                if (isAccountState(memberInfo)) {
                    return errorResponse(exchange, MemberAccountExceptionEnum.ERROR_ACCOUNT_IS_DISABLED);
                }
                context.headerUserInfo.setUserGuid(memberInfo.getUserGuid());
                context.headerUserInfo.setUserName(memberInfo.getUserName());
                context.headerUserInfo.setTel(memberInfo.getTel());
            }
            return chainFilter(exchange, chain, context.headerUserInfo);
        }

        return null;
    }

    private Mono<Void> handleTokenValidation(ServerWebExchange exchange, GatewayFilterChain chain, RequestContext context) {
        // 白名单token处理
        if (whiteListConfig.withoutToken(context.url)) {
            return handleWhiteListToken(exchange, chain, context);
        }

        // token必需检查
        if (StringUtils.isEmpty(context.token)) {
            return unAuthenResponse(exchange, FilterConstant.ERROR_TOKEN_AUTH_FAIL);
        }

        // 无需运营主体的token处理
        if (whiteListConfig.withoutOperSubject(context.url)) {
            return handleTokenWithoutOperSubject(exchange, chain, context);
        }

        // 运营主体检查
        if (StringUtils.isEmpty(context.operSubjectGuid)) {
            return unAuthenResponse(exchange, FilterConstant.ERROR_SUBJECT_FAIL);
        }

        return null;
    }

    private Mono<Void> handleWhiteListToken(ServerWebExchange exchange, GatewayFilterChain chain, RequestContext context) {
        if (!StringUtils.isEmpty(context.token)) {
            context.headerUserInfo = getUserInformation(context.headerUserInfo, context.token,
                    context.source, context.operSubjectGuid);
            context.headerUserInfo.setSystem(context.system);
            log.info("url:{},headerUserInfo={}", context.url, JSON.toJSONString(context.headerUserInfo));
        }
        return chainFilter(exchange, chain, context.headerUserInfo);
    }

    private Mono<Void> handleTokenWithoutOperSubject(ServerWebExchange exchange, GatewayFilterChain chain, RequestContext context) {
        context.headerUserInfo = getUserInformation(context.headerUserInfo, context.token,
                context.source, context.operSubjectGuid);
        context.headerUserInfo.setSystem(context.system);
        context.headerUserInfo.setToken(context.token);
        return chainFilter(exchange, chain, context.headerUserInfo);
    }

    private boolean checkSubjectState(HeaderUserInfo headerUserInfo, Integer source, String operSubjectGuid) {
        return headerUserInfo.getSystem() == SystemEnum.RETAIL.getCode()
                && (source == SourceTypeEnum.ADD_ONE_MACHINE.getCode() || source == SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode())
                && Boolean.TRUE.equals(operatingSubjectService.checkSubjectState(headerUserInfo.getSystem(), Integer.parseInt(operSubjectGuid)));
    }

    private void afterHandleField(HeaderUserInfo headerUserInfo, String enterpriseGuid, String operSubjectGuid,
                                  Integer source, int system) {
        if (StringUtils.isEmpty(headerUserInfo.getEnterpriseGuid())) {
            headerUserInfo.setEnterpriseGuid(enterpriseGuid);
        }
        if (StringUtils.isEmpty(headerUserInfo.getOperSubjectGuid())) {
            headerUserInfo.setOperSubjectGuid(operSubjectGuid);
        }
        if (Objects.isNull(headerUserInfo.getSource())) {
            headerUserInfo.setSource(source);
        }
        if (Objects.isNull(headerUserInfo.getSystem()) || headerUserInfo.getSystem() != system) {
            headerUserInfo.setSystem(system);
        }
    }
}
