package com.holderzone.member.gateway.utils;

import com.alibaba.fastjson.JSON;
import com.holderzone.member.common.constant.Result;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/13
 */
public class ResponseUtil {

    /**
     * 认证失败返回结果
     *
     * @param exchange
     * @return
     */
    public static Mono<Void> unAuthenResponse(ServerWebExchange exchange) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result result = Result.error(HttpStatus.UNAUTHORIZED.value(), "认证失败");
        byte[] resultBytes = JSON.toJSONBytes(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

    /**
     * 错误请求返回结果
     *
     * @param exchange
     * @param exceptionMessage
     * @return
     */
    public static Mono<Void> badRequestResponse(ServerWebExchange exchange, String exceptionMessage) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result result = Result.error(HttpStatus.BAD_REQUEST.value(), exceptionMessage);
        byte[] resultBytes = JSON.toJSONBytes(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

    /**
     * 授权失败返回结果
     *
     * @param exchange
     * @param exceptionMessage
     * @return
     */
    public static Mono<Void> unAuthorResponse(ServerWebExchange exchange, String exceptionMessage) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result result = Result.error(HttpStatus.METHOD_NOT_ALLOWED.value(), exceptionMessage);
        byte[] resultBytes = JSON.toJSONBytes(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

    /**
     * 存在敏感词汇的返回结果
     *
     * @param exchange
     * @return
     */
    public static Mono<Void> unSuitableContentResponse(ServerWebExchange exchange,String content) {
        ServerHttpResponse serverHttpResponse = exchange.getResponse();
        DataBufferFactory dataBufferFactory = serverHttpResponse.bufferFactory();
        Result result = Result.error(HttpStatus.FORBIDDEN.value(),  content+"为敏感词汇，不能使用");
        byte[] resultBytes = JSON.toJSONBytes(result);
        serverHttpResponse.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return serverHttpResponse.writeWith(Mono.just(resultBytes).map(dataBufferFactory::wrap));
    }

}
