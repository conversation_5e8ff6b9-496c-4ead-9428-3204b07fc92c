package com.holderzone.member.gateway.controller;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.gateway.sevice.TokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
/**
 * <AUTHOR>
 * @create 2024-02-02
 * @description token校验
 */
@RestController
@RequestMapping("/token")
@RequiredArgsConstructor
@Slf4j
public class TokenController {

    private final TokenService tokenService;

    @GetMapping("/verify")
    public Result<Void> verifyToken(@RequestParam("token") String token,@RequestParam("source") Integer source) {
        boolean result = tokenService.verifyToken(token, source);
        log.info("校验token:{},{}",token,result);
        return  result ? Result.success() : Result.error("token不存在");
    }
}
