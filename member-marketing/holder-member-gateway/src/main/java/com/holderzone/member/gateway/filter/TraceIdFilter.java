package com.holderzone.member.gateway.filter;

import com.holderzone.member.common.constant.FilterConstant;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Optional;

/**
 * 接收别的服务的请求时，拿出传过来的traceid，或者生成新的traceid，加入mdc，进行后续的链路追踪。
 */
@Slf4j
@Component
public class TraceIdFilter extends BaseFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        String traceId = Optional.ofNullable(headers.get(FilterConstant.TRACE_ID)).map(s -> s.get(0))
                .orElse(String.valueOf(System.currentTimeMillis()));
        // 对请求对象request进行增强
        exchange.getRequest().mutate().headers(httpHeaders -> {
            // httpHeaders 封装了所有的请求头
            MDC.put(FilterConstant.TRACE_ID, traceId);
            httpHeaders.set(FilterConstant.TRACE_ID, traceId);
        }).build();
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return -201;
    }
}
