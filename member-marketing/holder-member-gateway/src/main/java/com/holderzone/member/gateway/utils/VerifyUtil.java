package com.holderzone.member.gateway.utils;

import com.holderzone.member.common.enums.member.SourceTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2024-02-02
 * @description 校验工具
 */
public class VerifyUtil {

    private VerifyUtil() {
    }

    /**
     * 判断是否是小程序
     * @param source 来源
     * @return false or true
     */
    public static boolean isAppletRequest(Integer source) {
        return Objects.equals(SourceTypeEnum.ADD_WECHAT_ZHUANCAN.getCode(), source)
                || Objects.equals(SourceTypeEnum.ADD_ALI_PAY.getCode(), source);
    }
}
