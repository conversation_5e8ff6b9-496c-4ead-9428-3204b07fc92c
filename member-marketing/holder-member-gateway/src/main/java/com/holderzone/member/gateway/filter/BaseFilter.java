package com.holderzone.member.gateway.filter;

import com.holderzone.member.common.constant.FilterConstant;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/8/13
 */
public abstract class BaseFilter {

    /**
     * 从header中获取数据
     *
     * @param exchange
     * @return
     */
    protected String retrieveValueFromHeader(ServerWebExchange exchange, String code) {
        List<String> terminalCodeHeaders = exchange.getRequest().getHeaders().get(code);
        if (CollectionUtils.isEmpty(terminalCodeHeaders)) {
            return null;
        }
        return terminalCodeHeaders.get(0);
    }

    /**
     * 校验token是否存在
     *
     * @param exchange
     * @return
     */
    protected String validateTokenExist(ServerWebExchange exchange) {
        List<String> tokenList = exchange.getRequest().getHeaders().get(FilterConstant.TOKEN);
        if (tokenList == null || tokenList.isEmpty()) {
            return "";
        }
        String token = tokenList.stream().findFirst().orElse(null);
        if (StringUtils.isEmpty(token)) {
            return "";
        }
        return token;
    }

    /**
     * 从header中获取source
     *
     * @param exchange
     * @return
     */
    protected Integer retrieveSourceFromHeader(ServerWebExchange exchange) {
        List<String> sourceList = exchange.getRequest().getHeaders().get(FilterConstant.SOURCE);
        if (CollectionUtils.isEmpty(sourceList)) {
            return null;
        }
        return Integer.valueOf(sourceList.get(0));
    }
}
