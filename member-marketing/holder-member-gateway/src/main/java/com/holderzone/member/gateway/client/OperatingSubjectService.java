package com.holderzone.member.gateway.client;


import com.holderzone.framework.util.StringUtils;
import com.holderzone.member.common.constant.ThreadLocalCache;
import com.holderzone.member.common.dto.base.InitSubjectDataDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import com.holderzone.member.common.vo.ipass.SubjectListVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 运营主体相关
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OperatingSubjectService {

    private final ExternalSupport externalSupport;

    private final MemberBaseFeign memberBase;

    private final StringRedisTemplate redisTemplate;

    private final IPaasFeign iPaasFeign;

    private static final String INIT_SUBJECT_DATA = "INIT_SUBJECT_DATA:%s_%s";

    /**
     * 获取账号信息
     *
     * @return 请求结果
     */
    public HeaderUserInfo queryUserInformation(HeaderUserInfo headerUserInfo, String token) {
        try {
            HeaderUserInfo userInfo = externalSupport.baseServer(headerUserInfo.getSystem()).queryUserInformation(headerUserInfo.getEnterpriseGuid(), token);
            if (userInfo != null && !StringUtils.isEmpty(userInfo.getUserGuid())) {
                return userInfo;
            }
        } catch (Exception e) {
            log.error("请求外部接口异常", e);
        }
        return null;
    }

    public Boolean checkSubjectState(Integer system, Integer operSubjectId) {
        try {
            SubjectListVO subjectListVO = externalSupport.baseServer(system).findSubjectDetail(operSubjectId);
            return subjectListVO.getState() == BooleanEnum.TRUE.getCode();
        } catch (Exception e) {
            log.info("checkSubjectState请求外部接口异常={}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取业务数据
     **/
    public BusinessDataModel getBusinessData(String operSubjectGuid, String token) {

        try {
            BusinessDataModel businessDataModel = externalSupport.baseServer(ThreadLocalCache.getSystem()).queryBusinessData(operSubjectGuid, token);
            if (businessDataModel != null) {
                return businessDataModel;
            }
        } catch (Exception e) {
            log.error("请求业务数据地址错误", e);
        }
        return new BusinessDataModel();
    }

    public void initSubjectData(int system, String operSubjectGuid, String enterpriseGuid) {
        //判断系统是否需要初始化
        if (!SystemEnum.NEED_INIT_SYSTEM.contains(system)) {
            log.warn("系统不需要初始化");
            return;
        }
        if (StringUtils.isEmpty(operSubjectGuid)) {
            log.warn("运营主体为空");
            return;
        }
        if (StringUtils.isEmpty(enterpriseGuid)) {
            log.warn("初始化企业guid为空");
            return;
        }
        //判断redis是否已初始化此主体
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(String.format(INIT_SUBJECT_DATA, operSubjectGuid, system), String.valueOf(1)))) {
            log.warn("redis已初始化此主体,operSubjectGuid={}", operSubjectGuid);
            return;
        }
        try {
            InitSubjectDataDTO initSubjectData = InitSubjectDataDTO.builder()
                    .operSubjectGuid(operSubjectGuid)
                    .enterpriseGuid(enterpriseGuid)
                    .system(system)
                    .build();
            //若不存在则去初始化会员系统的信息
            memberBase.autoInitSubjectData(initSubjectData);
        } catch (Exception e) {
            log.error("自动初始化运营主体失败", e);
        }
    }

}
