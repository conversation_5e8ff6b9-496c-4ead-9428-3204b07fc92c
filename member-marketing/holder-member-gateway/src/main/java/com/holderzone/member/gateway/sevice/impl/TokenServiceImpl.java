package com.holderzone.member.gateway.sevice.impl;

import com.holderzone.member.gateway.sevice.TokenService;
import com.holderzone.member.gateway.utils.JwtParserUtil;
import com.holderzone.member.gateway.utils.VerifyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2024-02-02
 * @description token
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TokenServiceImpl implements TokenService {

    private final JwtParserUtil jwtParserUtil;
    @Override
    public boolean verifyToken(String token,Integer source) {
        if(!VerifyUtil.isAppletRequest(source)){
            return false;
        }
        return jwtParserUtil.checkToken(token, source);
    }
}
