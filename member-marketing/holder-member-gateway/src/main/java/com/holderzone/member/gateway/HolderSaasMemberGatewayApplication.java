package com.holderzone.member.gateway;

import com.holderzone.member.common.config.IPaasSecretConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.Import;

@SpringBootApplication
@EnableDiscoveryClient
@ComponentScans(value = {
        @ComponentScan("com.holderzone.member.common.external"),
        @ComponentScan("com.holderzone.member.common.client"),
        @ComponentScan("com.holderzone.member.common.feign"),
        @ComponentScan("com.holderzone.member.common.handler")
})
@Import(IPaasSecretConfig.class)
@EnableFeignClients(basePackages = "com.holderzone.member.common.feign")
public class HolderSaasMemberGatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(HolderSaasMemberGatewayApplication.class, args);
    }

}
