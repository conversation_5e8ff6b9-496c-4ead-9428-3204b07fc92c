package com.holderzone.member.gateway.handler;

import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import com.holderzone.member.common.exception.MemberBaseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 * @description 网关异常处理
 * @date 2021/8/10
 */
@Slf4j
@Order(-1)
@RequiredArgsConstructor
@Component
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    @Override
    public Mono<Void> handle(ServerWebExchange serverWebExchange, Throwable throwable) {
        log.info("【系统异常信息：{}】，\n{}",serverWebExchange.getRequest().getPath(),throwable);
        Result exceptionResult = Result.error(ExceptionEnum.INTERNAL_SERVER_ERROR);
        if(throwable instanceof MemberBaseException){
            exceptionResult = Result.error(((MemberBaseException) throwable).getCode(),((MemberBaseException) throwable).getDes());
        }
        if(throwable instanceof NotFoundException){
            exceptionResult = Result.error(ExceptionEnum.NOT_FOUND);
        }
        if(throwable instanceof ResponseStatusException){
            //响应状态值
            int status = ((ResponseStatusException) throwable).getStatus().value();
            exceptionResult = responseResult(status);
        }
        ServerHttpResponse response = serverWebExchange.getResponse();
        DataBufferFactory bufferFactory = response.bufferFactory();
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON_UTF8);
        return response.writeWith(Flux.just(bufferFactory.wrap(exceptionResult.toString().getBytes())));
    }

    private Result responseResult(int status) {
        ExceptionEnum exceptionEnum = ExceptionEnum.INTERNAL_SERVER_ERROR;
        switch (status){
            case 404:
                exceptionEnum = ExceptionEnum.NOT_FOUND;
                break;
            case 503:
                exceptionEnum = ExceptionEnum.SERVER_BUSY;
                break;
            default:
                break;
        }
        return Result.error(exceptionEnum);
    }
}
