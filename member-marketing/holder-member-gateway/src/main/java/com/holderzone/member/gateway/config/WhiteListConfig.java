package com.holderzone.member.gateway.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.server.PathContainer;
import org.springframework.stereotype.Component;
import org.springframework.web.util.pattern.PathPattern;
import org.springframework.web.util.pattern.PathPatternParser;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/12/27
 **/
@ConfigurationProperties(prefix = "white.list")
@PropertySource(value = "classpath:whiteList.properties")
@Component
@Setter
@Getter
@Slf4j
public class WhiteListConfig{
	private final PathPatternParser pathPatternParser = new PathPatternParser();

	private List<String> withoutToken = new ArrayList<>();
	private List<String> withoutOperSubject = new ArrayList<>();
	private List<String> withoutAll = new ArrayList<>();

	// 预编译后的路径模式
	private List<PathPattern> compiledWithoutToken = new ArrayList<>();

	@PostConstruct
	public void init() {
		// 不区分大小写
		pathPatternParser.setCaseSensitive(false);
		// 匹配结尾斜杠
		pathPatternParser.setMatchOptionalTrailingSeparator(true);
		// 预编译所有路径模式
		compiledWithoutToken = compilePatterns(withoutToken);
	}

	private List<PathPattern> compilePatterns(List<String> patterns) {
		return patterns.stream().map(pathPatternParser::parse).collect(Collectors.toList());
	}

	/**
	 * 判断请求路径是否不需要token
	 *
	 * @param requestUrl 请求路径
	 * @return true 不要  false 要
	 */
	public boolean withoutToken(String requestUrl){
		PathContainer pathContainer = PathContainer.parsePath(requestUrl);

		for (PathPattern pattern : compiledWithoutToken) {
			if (pattern.matches(pathContainer)) {
				log.info("请求路径不需要token：{} ", requestUrl);
				return true;
			}
		}
		return false;
	}

	/**
	 * 判断请求路径是否不需要运营主体
	 *
	 * @param requestUrl 请求路径
	 * @return true 不要  false 要
	 */
	public boolean withoutOperSubject(String requestUrl){
		for (String url : withoutOperSubject) {
			if (requestUrl.contains(url)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 判断请求路径是否都不需要
	 *
	 * @param requestUrl 请求路径
	 * @return true 不要  false 要
	 */
	public boolean withoutAll(String requestUrl){
		for (String url : withoutAll) {
			if (requestUrl.matches(url.replace("*", ".*"))) {
				return true;
			}
		}
		return false;
	}
}
