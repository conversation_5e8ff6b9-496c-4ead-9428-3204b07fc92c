package com.holderzone.member.gateway.handler;

import com.alibaba.csp.sentinel.adapter.gateway.sc.callback.BlockRequestHandler;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import com.holderzone.member.common.constant.Result;
import com.holderzone.member.common.enums.exception.ExceptionEnum;
import com.holderzone.member.common.enums.exception.SentinelExceptionEnum;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description 哨兵监控异常处理
 * @date 2021/8/10
 */
@Component
@Order(-2)
public class SentinelExceptionHandler implements BlockRequestHandler {

    @Override
    public Mono<ServerResponse> handleRequest(ServerWebExchange serverWebExchange, Throwable throwable) {
        Result data = Result.error(ExceptionEnum.SERVER_BUSY);
        if (throwable instanceof FlowException) {
            data = Result.error(SentinelExceptionEnum.FLOW);
        } else if (throwable instanceof DegradeException) {
            data = Result.error(SentinelExceptionEnum.DEGRADE);
        } else if (throwable instanceof AuthorityException) {
            data = Result.error(SentinelExceptionEnum.AUTHORITY);
        } else if (throwable instanceof ParamFlowException) {
            data = Result.error(SentinelExceptionEnum.PARAM_FLOW);
        } else if (throwable instanceof SystemBlockException) {
            data = Result.error(SentinelExceptionEnum.SYSTEM_BLOCK);
        }
        return ServerResponse.status(HttpStatus.OK)
                .contentType(MediaType.APPLICATION_JSON)
                .body(BodyInserters.fromValue(data));
    }
}
