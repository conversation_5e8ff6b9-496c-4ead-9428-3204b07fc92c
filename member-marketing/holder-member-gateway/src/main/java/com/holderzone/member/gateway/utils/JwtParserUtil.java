package com.holderzone.member.gateway.utils;


import cn.hutool.json.JSONUtil;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.TokenRequestBO;
import com.holderzone.member.common.exception.FileIllegalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.*;
import java.util.Base64;
import java.util.Optional;

import static com.holderzone.member.common.util.verify.CustomJwtEncoder.UTF_8;


/**
 * <AUTHOR>
 * @date 2021.08.19
 */
@Component
@Slf4j
public class JwtParserUtil {

    private static final String TOKEN_PREFIX = "token_";

    private static final String MEMBER = "member:";

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 解析签名(jwt的第三部分)
     *
     * @param token
     * @return
     */
    public String acquireSign(String token) {
        return token.split("\\.")[2];
    }

    public TokenRequestBO acquirePayload(String token) {
        try {
            byte[] decode = Base64.getDecoder().decode((token.split("\\.")[1]).getBytes(UTF_8));
            TokenRequestBO tokenRequestBO = JacksonUtils.toObject(TokenRequestBO.class, decode);
            return tokenRequestBO;
        } catch (UnsupportedEncodingException e) {
            throw new FileIllegalException("编码错误", e);
        }
    }

    /**
     * @param bytes
     * @return
     */
    public Object toObject(byte[] bytes) {
        Object obj = null;
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectInputStream ois = new ObjectInputStream(bis);
            obj = ois.readObject();
            ois.close();
            bis.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        } catch (ClassNotFoundException ex) {
            ex.printStackTrace();
        }
        return obj;
    }

    public String acquireUserFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getOpenId();
    }

    public String acquireAccountFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getAccount();
    }


    public String acquireStoreFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getSessionKey();
    }

    public String acquireDeviceFromToken(String token) {
        TokenRequestBO tokenRequestBO = acquirePayload(token);
        return tokenRequestBO.getUnionId();
    }


    public boolean isServerExistToken(String token, Integer source) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        try {
            String openId = acquireUserFromToken(token);
            String cacheKey = TOKEN_PREFIX + openId + ":" + source;
            Object serverToken = redisTemplate.opsForValue().get(cacheKey);
            return serverToken != null && serverToken.toString().split(StringConstant.POUND)[0].equals(token);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private Object getServerToken(String token, Integer source) {
        String openId = acquireUserFromToken(token);
        if (StringUtils.isEmpty(openId)) {
            openId = acquireAccountFromToken(token);
        }
        String cacheKey = TOKEN_PREFIX + openId + ":" + source;
        log.info("缓存key：{}", cacheKey);
        Object serverToken = stringRedisTemplate.opsForValue().get(cacheKey);
        log.info("serverToken：{}", serverToken);
        return serverToken;
    }

    public boolean checkToken(String token, Integer source) {
        try {
            Object serverToken = getServerToken(token, source);
            return serverToken != null && serverToken.toString().split(StringConstant.POUND)[0].equals(token);
        } catch (Exception e) {
            log.error("校验token异常", e);
        }
        return false;
    }

    public String getMemberGuid(String token, Integer source) {

        if (StringUtils.isEmpty(token)) {
            return null;
        }
        try {
            Object serverToken = getServerToken(token, source);
            if (serverToken != null) {
                final String[] split = serverToken.toString().split(StringConstant.POUND);
                if (token.equals(split[0])) {
                    return split.length > 1 ? split[1] : "";
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public HeaderUserInfo getMember(String memberGuid) {
        String cacheKey = MEMBER + memberGuid;
        return Optional.ofNullable(redisTemplate.opsForValue().get(cacheKey))
                .map(v -> JSONUtil.toBean(v.toString(), HeaderUserInfo.class))
                .orElse(new HeaderUserInfo());
    }

    /**
     * 对象转数组
     * @param obj
     * @return
     */
    public byte[] toByteArray(Object obj) {
        byte[] bytes = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.flush();
            bytes = bos.toByteArray();
            oos.close();
            bos.close();
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return bytes;
    }

}
