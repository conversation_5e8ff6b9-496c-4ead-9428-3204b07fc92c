package com.holderzone.member.gateway.config;

import com.holderzone.member.gateway.config.HttpMessageConverterConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.http.converter.HttpMessageConverter;

public class HttpMessageConverterConfigTest {

    private HttpMessageConverterConfig httpMessageConverterConfigUnderTest;

    @Before
    public void setUp() {
        httpMessageConverterConfigUnderTest = new HttpMessageConverterConfig();
    }

    @Test
    public void testMessageConverters() {
        // Setup
        final ObjectProvider<HttpMessageConverter<?>> converters = null;

        // Run the test
        final HttpMessageConverters result = httpMessageConverterConfigUnderTest.messageConverters(converters);

        // Verify the results
    }
}
