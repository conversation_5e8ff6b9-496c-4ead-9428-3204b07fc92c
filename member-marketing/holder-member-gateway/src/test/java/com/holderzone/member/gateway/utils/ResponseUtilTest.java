package com.holderzone.member.gateway.utils;

import com.holderzone.member.gateway.utils.ResponseUtil;
import org.junit.Test;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

public class ResponseUtilTest {

    @Test
    public void testUnAuthenResponse() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Mono<Void> result = ResponseUtil.unAuthenResponse(exchange);

        // Verify the results
    }

    @Test
    public void testBadRequestResponse() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Mono<Void> result = ResponseUtil.badRequestResponse(exchange, "exceptionMessage");

        // Verify the results
    }

    @Test
    public void testUnAuthorResponse() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Mono<Void> result = ResponseUtil.unAuthorResponse(exchange, "exceptionMessage");

        // Verify the results
    }

    @Test
    public void testUnSuitableContentResponse() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Mono<Void> result = ResponseUtil.unSuitableContentResponse(exchange, "content");

        // Verify the results
    }
}
