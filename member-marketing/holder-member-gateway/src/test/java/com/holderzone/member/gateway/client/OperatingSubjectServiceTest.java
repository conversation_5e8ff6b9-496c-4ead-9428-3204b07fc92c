package com.holderzone.member.gateway.client;

import com.holderzone.member.common.dto.base.InitSubjectDataDTO;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.external.ExternalSupport;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.common.feign.MemberBaseFeign;
import com.holderzone.member.common.vo.base.BusinessDataModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OperatingSubjectServiceTest {

    @Mock
    private ExternalSupport mockExternalSupport;
    @Mock
    private MemberBaseFeign mockMemberBase;
    @Mock
    private StringRedisTemplate mockRedisTemplate;
    @Mock
    private IPaasFeign mockIPaasFeign;

    private OperatingSubjectService operatingSubjectServiceUnderTest;

    @Before
    public void setUp() {
        operatingSubjectServiceUnderTest = new OperatingSubjectService(mockExternalSupport, mockMemberBase,
                mockRedisTemplate, mockIPaasFeign);
    }

    @Test
    public void testQueryUserInformation() {
        // Setup
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setIsAlliance(false);
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setSystem(0);

        final HeaderUserInfo expectedResult = new HeaderUserInfo();
        expectedResult.setIsAlliance(false);
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setUserGuid("userGuid");
        expectedResult.setSystem(0);

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final HeaderUserInfo result = operatingSubjectServiceUnderTest.queryUserInformation(headerUserInfo, "token");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCheckSubjectState() {
        // Setup
        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final Boolean result = operatingSubjectServiceUnderTest.checkSubjectState(0, 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetBusinessData() {
        // Setup
        final BusinessDataModel expectedResult = new BusinessDataModel();
        expectedResult.setBusinessDataAddress("businessDataAddress");
        expectedResult.setTeamId("teamId");
        expectedResult.setTeamName("teamName");

        when(mockExternalSupport.baseServer(0)).thenReturn(null);

        // Run the test
        final BusinessDataModel result = operatingSubjectServiceUnderTest.getBusinessData("operSubjectGuid", "token");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testInitSubjectData() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        operatingSubjectServiceUnderTest.initSubjectData(0, "operSubjectGuid", "enterpriseGuid");

        // Verify the results
        verify(mockMemberBase).autoInitSubjectData(InitSubjectDataDTO.builder()
                .system(0)
                .operSubjectGuid("operSubjectGuid")
                .build());
    }
}
