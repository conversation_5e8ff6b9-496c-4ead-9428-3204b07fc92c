package com.holderzone.member.gateway.config;

import com.holderzone.member.gateway.config.RedisTemplateConfig;
import org.junit.Before;
import org.junit.Test;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

public class RedisTemplateConfigTest {

    private RedisTemplateConfig redisTemplateConfigUnderTest;

    @Before
    public void setUp() {
        redisTemplateConfigUnderTest = new RedisTemplateConfig();
    }

    @Test
    public void testRedisTemplate() {
        // Setup
        final RedisConnectionFactory connectionFactory = null;

        // Run the test
        final RedisTemplate<String, Object> result = redisTemplateConfigUnderTest.redisTemplate(connectionFactory);

        // Verify the results
    }
}
