package com.holderzone.member.gateway.handler;

import com.holderzone.member.gateway.handler.GlobalExceptionHandler;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

public class GlobalExceptionHandlerTest {

    private GlobalExceptionHandler globalExceptionHandlerUnderTest;

    @Before
    public void setUp() {
        globalExceptionHandlerUnderTest = new GlobalExceptionHandler();
    }

    @Test
    public void testHandle() {
        // Setup
        final ServerWebExchange serverWebExchange = null;

        // Run the test
        final Mono<Void> result = globalExceptionHandlerUnderTest.handle(serverWebExchange, new Exception("message"));

        // Verify the results
    }
}
