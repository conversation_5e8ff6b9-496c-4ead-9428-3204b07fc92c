package com.holderzone.member.gateway.config;

import com.holderzone.member.gateway.config.WhiteListConfig;
import org.junit.Before;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class WhiteListConfigTest {

    private WhiteListConfig whiteListConfigUnderTest;

    @Before
    public void setUp() {
        whiteListConfigUnderTest = new WhiteListConfig();
    }

    @Test
    public void testWithoutToken() {
        assertThat(whiteListConfigUnderTest.withoutToken("requestUrl")).isFalse();
    }

    @Test
    public void testWithoutOperSubject() {
        assertThat(whiteListConfigUnderTest.withoutOperSubject("requestUrl")).isFalse();
    }

    @Test
    public void testWithoutAll() {
        assertThat(whiteListConfigUnderTest.withoutAll("requestUrl")).isFalse();
    }
}
