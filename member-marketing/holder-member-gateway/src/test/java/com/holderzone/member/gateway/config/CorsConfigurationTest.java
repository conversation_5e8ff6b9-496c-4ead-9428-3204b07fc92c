package com.holderzone.member.gateway.config;

import com.holderzone.member.gateway.config.CorsConfiguration;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.filter.reactive.HiddenHttpMethodFilter;
import org.springframework.web.server.WebFilter;

public class CorsConfigurationTest {

    private CorsConfiguration corsConfigurationUnderTest;

    @Before
    public void setUp() {
        corsConfigurationUnderTest = new CorsConfiguration();
    }

    @Test
    public void testCorsFilter() {
        // Setup
        // Run the test
        final WebFilter result = corsConfigurationUnderTest.corsFilter();

        // Verify the results
    }

    @Test
    public void testHiddenHttpMethodFilter() {
        // Setup
        // Run the test
        final HiddenHttpMethodFilter result = corsConfigurationUnderTest.hiddenHttpMethodFilter();

        // Verify the results
    }
}
