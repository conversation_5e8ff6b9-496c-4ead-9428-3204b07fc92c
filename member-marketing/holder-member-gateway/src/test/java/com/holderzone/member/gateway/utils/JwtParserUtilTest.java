package com.holderzone.member.gateway.utils;

import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.dto.user.TokenRequestBO;
import com.holderzone.member.gateway.utils.JwtParserUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class JwtParserUtilTest {

    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private StringRedisTemplate mockStringRedisTemplate;

    @InjectMocks
    private JwtParserUtil jwtParserUtilUnderTest;

    @Test
    public void testAcquireSign() {
        assertThat(jwtParserUtilUnderTest.acquireSign("token")).isEqualTo("result");
    }

    @Test
    public void testAcquirePayload() {
        // Setup
        final TokenRequestBO expectedResult = new TokenRequestBO();
        expectedResult.setOpenId("openId");
        expectedResult.setSessionKey("sessionKey");
        expectedResult.setUnionId("unionId");
        expectedResult.setStoreNo("storeNo");
        expectedResult.setAccount("account");

        // Run the test
        final TokenRequestBO result = jwtParserUtilUnderTest.acquirePayload("token");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testToObject() {
        assertThat(jwtParserUtilUnderTest.toObject("content".getBytes())).isEqualTo("result");
    }

    @Test
    public void testAcquireUserFromToken() {
        assertThat(jwtParserUtilUnderTest.acquireUserFromToken("token")).isEqualTo("openId");
    }

    @Test
    public void testAcquireAccountFromToken() {
        assertThat(jwtParserUtilUnderTest.acquireAccountFromToken("token")).isEqualTo("account");
    }

    @Test
    public void testAcquireStoreFromToken() {
        assertThat(jwtParserUtilUnderTest.acquireStoreFromToken("token")).isEqualTo("sessionKey");
    }

    @Test
    public void testAcquireDeviceFromToken() {
        assertThat(jwtParserUtilUnderTest.acquireDeviceFromToken("token")).isEqualTo("unionId");
    }

    @Test
    public void testIsServerExistToken() {
        // Setup
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = jwtParserUtilUnderTest.isServerExistToken("token", 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testCheckToken() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final boolean result = jwtParserUtilUnderTest.checkToken("token", 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testGetMemberGuid() {
        // Setup
        when(mockStringRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final String result = jwtParserUtilUnderTest.getMemberGuid("token", 0);

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testGetMember() {
        // Setup
        final HeaderUserInfo expectedResult = new HeaderUserInfo();
        expectedResult.setIsAlliance(false);
        expectedResult.setOperSubjectGuid("operSubjectGuid");
        expectedResult.setEnterpriseGuid("enterpriseGuid");
        expectedResult.setIsCheckToken("isCheckToken");
        expectedResult.setEnterpriseName("enterpriseName");

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Run the test
        final HeaderUserInfo result = jwtParserUtilUnderTest.getMember("memberGuid");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testToByteArray() {
        assertThat(jwtParserUtilUnderTest.toByteArray("obj")).isEqualTo("content".getBytes());
    }
}
