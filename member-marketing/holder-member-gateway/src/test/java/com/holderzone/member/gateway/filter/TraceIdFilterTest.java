package com.holderzone.member.gateway.filter;

import com.holderzone.member.gateway.filter.TraceIdFilter;
import org.junit.Before;
import org.junit.Test;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import static org.assertj.core.api.Assertions.assertThat;

public class TraceIdFilterTest {

    private TraceIdFilter traceIdFilterUnderTest;

    @Before
    public void setUp() {
        traceIdFilterUnderTest = new TraceIdFilter();
    }

    @Test
    public void testFilter() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;

        // Run the test
        final Mono<Void> result = traceIdFilterUnderTest.filter(exchange, chain);

        // Verify the results
    }

    @Test
    public void testGetOrder() {
        assertThat(traceIdFilterUnderTest.getOrder()).isEqualTo(-201);
    }
}
