package com.holderzone.member.gateway.filter;

import com.holderzone.member.common.constant.FilterConstant;
import com.holderzone.member.common.dto.user.HeaderUserInfo;
import com.holderzone.member.common.enums.ResponseBase;
import com.holderzone.member.common.feign.IPaasFeign;
import com.holderzone.member.gateway.client.OperatingSubjectService;
import com.holderzone.member.gateway.config.WhiteListConfig;
import com.holderzone.member.gateway.utils.JwtParserUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class HttpRequestFilterTest {

    @Mock
    private JwtParserUtil mockJwtParserUtil;
    @Mock
    private RedisTemplate mockRedisTemplate;
    @Mock
    private WhiteListConfig mockWhiteListConfig;
    @Mock
    private OperatingSubjectService mockOperatingSubjectService;
    @Mock
    private IPaasFeign mockIPaasFeign;

    @InjectMocks
    private HttpRequestFilter httpRequestFilterUnderTest;

    @Test
    public void testFilter() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;
        when(mockWhiteListConfig.withoutAll("requestUrl")).thenReturn(false);
        when(mockWhiteListConfig.withoutToken("requestUrl")).thenReturn(false);
        when(mockJwtParserUtil.getMemberGuid("token", FilterConstant.MEMBER_APPLET)).thenReturn("userGuid");

        // Configure JwtParserUtil.getMember(...).
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setEnterpriseName("enterpriseName");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSource(0);
        headerUserInfo.setSystem(0);
        headerUserInfo.setAccountState(0);
        headerUserInfo.setUserAccount("userAccount");
        headerUserInfo.setToken("token");
        when(mockJwtParserUtil.getMember("userGuid")).thenReturn(headerUserInfo);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OperatingSubjectService.queryUserInformation(...).
        final HeaderUserInfo headerUserInfo1 = new HeaderUserInfo();
        headerUserInfo1.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo1.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo1.setEnterpriseName("enterpriseName");
        headerUserInfo1.setUserGuid("userGuid");
        headerUserInfo1.setUserName("userName");
        headerUserInfo1.setTel("tel");
        headerUserInfo1.setSource(0);
        headerUserInfo1.setSystem(0);
        headerUserInfo1.setAccountState(0);
        headerUserInfo1.setUserAccount("userAccount");
        headerUserInfo1.setToken("token");
        final HeaderUserInfo headerUserInfo2 = new HeaderUserInfo();
        headerUserInfo2.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo2.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo2.setEnterpriseName("enterpriseName");
        headerUserInfo2.setUserGuid("userGuid");
        headerUserInfo2.setUserName("userName");
        headerUserInfo2.setTel("tel");
        headerUserInfo2.setSource(0);
        headerUserInfo2.setSystem(0);
        headerUserInfo2.setAccountState(0);
        headerUserInfo2.setUserAccount("userAccount");
        headerUserInfo2.setToken("token");
        when(mockOperatingSubjectService.queryUserInformation(headerUserInfo2, "token")).thenReturn(headerUserInfo1);

        when(mockWhiteListConfig.withoutOperSubject("requestUrl")).thenReturn(false);
        when(mockOperatingSubjectService.checkSubjectState(0, 0)).thenReturn(false);

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.filter(exchange, chain);

        // Verify the results
        verify(mockOperatingSubjectService).initSubjectData(0, "operSubjectGuid", "enterpriseGuid");
    }

    @Test
    public void testFilter_WhiteListConfigWithoutAllReturnsTrue() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;
        when(mockWhiteListConfig.withoutAll("requestUrl")).thenReturn(true);
        when(mockJwtParserUtil.getMemberGuid("token", FilterConstant.MEMBER_APPLET)).thenReturn("userGuid");

        // Configure JwtParserUtil.getMember(...).
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setEnterpriseName("enterpriseName");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSource(0);
        headerUserInfo.setSystem(0);
        headerUserInfo.setAccountState(0);
        headerUserInfo.setUserAccount("userAccount");
        headerUserInfo.setToken("token");
        when(mockJwtParserUtil.getMember("userGuid")).thenReturn(headerUserInfo);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OperatingSubjectService.queryUserInformation(...).
        final HeaderUserInfo headerUserInfo1 = new HeaderUserInfo();
        headerUserInfo1.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo1.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo1.setEnterpriseName("enterpriseName");
        headerUserInfo1.setUserGuid("userGuid");
        headerUserInfo1.setUserName("userName");
        headerUserInfo1.setTel("tel");
        headerUserInfo1.setSource(0);
        headerUserInfo1.setSystem(0);
        headerUserInfo1.setAccountState(0);
        headerUserInfo1.setUserAccount("userAccount");
        headerUserInfo1.setToken("token");
        final HeaderUserInfo headerUserInfo2 = new HeaderUserInfo();
        headerUserInfo2.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo2.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo2.setEnterpriseName("enterpriseName");
        headerUserInfo2.setUserGuid("userGuid");
        headerUserInfo2.setUserName("userName");
        headerUserInfo2.setTel("tel");
        headerUserInfo2.setSource(0);
        headerUserInfo2.setSystem(0);
        headerUserInfo2.setAccountState(0);
        headerUserInfo2.setUserAccount("userAccount");
        headerUserInfo2.setToken("token");
        when(mockOperatingSubjectService.queryUserInformation(headerUserInfo2, "token")).thenReturn(headerUserInfo1);

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.filter(exchange, chain);

        // Verify the results
        verify(mockOperatingSubjectService).initSubjectData(0, "operSubjectGuid", "enterpriseGuid");
    }

    @Test
    public void testFilter_WhiteListConfigWithoutTokenReturnsTrue() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;
        when(mockWhiteListConfig.withoutAll("requestUrl")).thenReturn(false);
        when(mockWhiteListConfig.withoutToken("requestUrl")).thenReturn(true);
        when(mockJwtParserUtil.getMemberGuid("token", FilterConstant.MEMBER_APPLET)).thenReturn("userGuid");

        // Configure JwtParserUtil.getMember(...).
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setEnterpriseName("enterpriseName");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSource(0);
        headerUserInfo.setSystem(0);
        headerUserInfo.setAccountState(0);
        headerUserInfo.setUserAccount("userAccount");
        headerUserInfo.setToken("token");
        when(mockJwtParserUtil.getMember("userGuid")).thenReturn(headerUserInfo);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OperatingSubjectService.queryUserInformation(...).
        final HeaderUserInfo headerUserInfo1 = new HeaderUserInfo();
        headerUserInfo1.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo1.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo1.setEnterpriseName("enterpriseName");
        headerUserInfo1.setUserGuid("userGuid");
        headerUserInfo1.setUserName("userName");
        headerUserInfo1.setTel("tel");
        headerUserInfo1.setSource(0);
        headerUserInfo1.setSystem(0);
        headerUserInfo1.setAccountState(0);
        headerUserInfo1.setUserAccount("userAccount");
        headerUserInfo1.setToken("token");
        final HeaderUserInfo headerUserInfo2 = new HeaderUserInfo();
        headerUserInfo2.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo2.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo2.setEnterpriseName("enterpriseName");
        headerUserInfo2.setUserGuid("userGuid");
        headerUserInfo2.setUserName("userName");
        headerUserInfo2.setTel("tel");
        headerUserInfo2.setSource(0);
        headerUserInfo2.setSystem(0);
        headerUserInfo2.setAccountState(0);
        headerUserInfo2.setUserAccount("userAccount");
        headerUserInfo2.setToken("token");
        when(mockOperatingSubjectService.queryUserInformation(headerUserInfo2, "token")).thenReturn(headerUserInfo1);

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.filter(exchange, chain);

        // Verify the results
        verify(mockOperatingSubjectService).initSubjectData(0, "operSubjectGuid", "enterpriseGuid");
    }

    @Test
    public void testFilter_OperatingSubjectServiceQueryUserInformationReturnsNull() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;
        when(mockWhiteListConfig.withoutAll("requestUrl")).thenReturn(false);
        when(mockWhiteListConfig.withoutToken("requestUrl")).thenReturn(true);
        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OperatingSubjectService.queryUserInformation(...).
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setEnterpriseName("enterpriseName");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSource(0);
        headerUserInfo.setSystem(0);
        headerUserInfo.setAccountState(0);
        headerUserInfo.setUserAccount("userAccount");
        headerUserInfo.setToken("token");
        when(mockOperatingSubjectService.queryUserInformation(headerUserInfo, "token")).thenReturn(null);

        when(mockJwtParserUtil.getMemberGuid("token", FilterConstant.MEMBER_APPLET)).thenReturn("userGuid");

        // Configure JwtParserUtil.getMember(...).
        final HeaderUserInfo headerUserInfo1 = new HeaderUserInfo();
        headerUserInfo1.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo1.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo1.setEnterpriseName("enterpriseName");
        headerUserInfo1.setUserGuid("userGuid");
        headerUserInfo1.setUserName("userName");
        headerUserInfo1.setTel("tel");
        headerUserInfo1.setSource(0);
        headerUserInfo1.setSystem(0);
        headerUserInfo1.setAccountState(0);
        headerUserInfo1.setUserAccount("userAccount");
        headerUserInfo1.setToken("token");
        when(mockJwtParserUtil.getMember("userGuid")).thenReturn(headerUserInfo1);

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.filter(exchange, chain);

        // Verify the results
        verify(mockOperatingSubjectService).initSubjectData(0, "operSubjectGuid", "enterpriseGuid");
    }

    @Test
    public void testFilter_WhiteListConfigWithoutOperSubjectReturnsTrue() {
        // Setup
        final ServerWebExchange exchange = null;
        final GatewayFilterChain chain = null;
        when(mockWhiteListConfig.withoutAll("requestUrl")).thenReturn(false);
        when(mockWhiteListConfig.withoutToken("requestUrl")).thenReturn(false);
        when(mockWhiteListConfig.withoutOperSubject("requestUrl")).thenReturn(true);
        when(mockJwtParserUtil.getMemberGuid("token", FilterConstant.MEMBER_APPLET)).thenReturn("userGuid");

        // Configure JwtParserUtil.getMember(...).
        final HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo.setEnterpriseName("enterpriseName");
        headerUserInfo.setUserGuid("userGuid");
        headerUserInfo.setUserName("userName");
        headerUserInfo.setTel("tel");
        headerUserInfo.setSource(0);
        headerUserInfo.setSystem(0);
        headerUserInfo.setAccountState(0);
        headerUserInfo.setUserAccount("userAccount");
        headerUserInfo.setToken("token");
        when(mockJwtParserUtil.getMember("userGuid")).thenReturn(headerUserInfo);

        when(mockRedisTemplate.opsForValue()).thenReturn(null);

        // Configure OperatingSubjectService.queryUserInformation(...).
        final HeaderUserInfo headerUserInfo1 = new HeaderUserInfo();
        headerUserInfo1.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo1.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo1.setEnterpriseName("enterpriseName");
        headerUserInfo1.setUserGuid("userGuid");
        headerUserInfo1.setUserName("userName");
        headerUserInfo1.setTel("tel");
        headerUserInfo1.setSource(0);
        headerUserInfo1.setSystem(0);
        headerUserInfo1.setAccountState(0);
        headerUserInfo1.setUserAccount("userAccount");
        headerUserInfo1.setToken("token");
        final HeaderUserInfo headerUserInfo2 = new HeaderUserInfo();
        headerUserInfo2.setOperSubjectGuid("operSubjectGuid");
        headerUserInfo2.setEnterpriseGuid("enterpriseGuid");
        headerUserInfo2.setEnterpriseName("enterpriseName");
        headerUserInfo2.setUserGuid("userGuid");
        headerUserInfo2.setUserName("userName");
        headerUserInfo2.setTel("tel");
        headerUserInfo2.setSource(0);
        headerUserInfo2.setSystem(0);
        headerUserInfo2.setAccountState(0);
        headerUserInfo2.setUserAccount("userAccount");
        headerUserInfo2.setToken("token");
        when(mockOperatingSubjectService.queryUserInformation(headerUserInfo2, "token")).thenReturn(headerUserInfo1);

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.filter(exchange, chain);

        // Verify the results
        verify(mockOperatingSubjectService).initSubjectData(0, "operSubjectGuid", "enterpriseGuid");
    }

    @Test
    public void testGetOrder() {
        assertThat(httpRequestFilterUnderTest.getOrder()).isEqualTo(-200);
    }

    @Test
    public void testValidateTokenExist() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final String result = httpRequestFilterUnderTest.validateTokenExist(exchange);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    public void testValidateOperSubjectGuidExist() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final String result = httpRequestFilterUnderTest.validateOperSubjectGuidExist(exchange);

        // Verify the results
        assertThat(result).isEqualTo("");
    }

    @Test
    public void testRetrieveSourceFromHeader() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Integer result = httpRequestFilterUnderTest.retrieveSourceFromHeader(exchange);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testGetEnterpriseGuid() {
        // Setup
        final HttpHeaders headers = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));

        // Run the test
        final String result = httpRequestFilterUnderTest.getEnterpriseGuid(headers);

        // Verify the results
        assertThat(result).isEqualTo("enterpriseGuid");
    }

    @Test
    public void testGetHeaderFistValue() {
        // Setup
        final HttpHeaders headers = new HttpHeaders(new LinkedMultiValueMap(new HashMap<>()));

        // Run the test
        final String result = httpRequestFilterUnderTest.getHeaderFistValue(headers, "key");

        // Verify the results
        assertThat(result).isEqualTo("result");
    }

    @Test
    public void testUnAuthenResponse() {
        // Setup
        final ServerWebExchange exchange = null;

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.unAuthenResponse(exchange, "message");

        // Verify the results
    }

    @Test
    public void testErrorResponse() {
        // Setup
        final ServerWebExchange exchange = null;
        final ResponseBase responseBase = null;

        // Run the test
        final Mono<Void> result = httpRequestFilterUnderTest.errorResponse(exchange, responseBase);

        // Verify the results
    }
}
