package com.holderzone.member.gateway.sevice.impl;

import com.holderzone.member.gateway.sevice.impl.TokenServiceImpl;
import com.holderzone.member.gateway.utils.JwtParserUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TokenServiceImplTest {

    @Mock
    private JwtParserUtil mockJwtParserUtil;

    private TokenServiceImpl tokenServiceImplUnderTest;

    @Before
    public void setUp() {
        tokenServiceImplUnderTest = new TokenServiceImpl(mockJwtParserUtil);
    }

    @Test
    public void testVerifyToken() {
        // Setup
        when(mockJwtParserUtil.checkToken("token", 0)).thenReturn(false);

        // Run the test
        final boolean result = tokenServiceImplUnderTest.verifyToken("token", 0);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testVerifyToken_JwtParserUtilReturnsTrue() {
        // Setup
        when(mockJwtParserUtil.checkToken("token", 0)).thenReturn(true);

        // Run the test
        final boolean result = tokenServiceImplUnderTest.verifyToken("token", 0);

        // Verify the results
        assertThat(result).isTrue();
    }
}
