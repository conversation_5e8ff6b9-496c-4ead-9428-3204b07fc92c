package com.holderzone.member.gateway.handler;

import com.holderzone.member.gateway.handler.SentinelExceptionHandler;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

public class SentinelExceptionHandlerTest {

    private SentinelExceptionHandler sentinelExceptionHandlerUnderTest;

    @Before
    public void setUp() {
        sentinelExceptionHandlerUnderTest = new SentinelExceptionHandler();
    }

    @Test
    public void testHandleRequest() {
        // Setup
        // Run the test
        final Mono<ServerResponse> result = sentinelExceptionHandlerUnderTest.handleRequest(null,
                new Exception("message"));

        // Verify the results
    }
}
