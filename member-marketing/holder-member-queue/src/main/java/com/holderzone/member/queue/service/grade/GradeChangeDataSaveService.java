package com.holderzone.member.queue.service.grade;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.enums.equities.EquitiesRuleTypeEnum;
import com.holderzone.member.common.enums.member.SourceTypeEnum;
import com.holderzone.member.common.util.verify.ObjectUtil;
import com.holderzone.member.common.vo.growth.MemberGrowthValueRelationVO;
import com.holderzone.member.queue.dto.grade.GradeChangeContext;
import com.holderzone.member.queue.dto.grade.GradeChangeResult;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.dto.member.UpdateIntegral;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.mapper.HsaMemberEquitiesReceiveRecordMapper;
import com.holderzone.member.queue.mapper.HsaOperationMemberInfoMapper;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.grade.impl.HsaMemberEquitiesReceiveRecordServiceImpl;
import com.holderzone.member.queue.service.member.HsaGrowthValueDetailService;
import com.holderzone.member.queue.service.member.HsaIntegralDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 等级变化数据保存服务
 * 专门负责批量保存等级变化相关的数据
 * 使用独立的事务管理，确保数据一致性
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class GradeChangeDataSaveService {

    private static final int BATCH_SAVE_SIZE = 1000;

    @Resource
    private HsaMemberGradeChangeDetailService gradeChangeDetailService;

    @Resource
    private HsaMemberGradeCardService gradeCardService;

    @Resource
    private HsaMemberGradeRightsRecordService gradeRightsRecordService;

    @Resource
    private HsaGrowthValueDetailService growthValueDetailService;

    @Resource
    private HsaIntegralDetailService integralDetailService;

    @Resource
    private GradeChangeAssembler gradeChangeAssembler;

    @Resource
    private HsaOperationMemberInfoMapper hsaOperationMemberInfoMapper;

    @Resource
    private HsaGrowthValueDetailService hsaGrowthValueDetailService;

    @Resource
    private HsaIntegralDetailService hsaIntegralDetailService;

    @Resource
    private HsaMemberEquitiesReceiveRecordServiceImpl hsaMemberEquitiesReceiveRecordService;

    @Resource
    private HsaMemberEquitiesReceiveRecordMapper hsaMemberEquitiesReceiveRecordMapper;

    /**
     * 批量保存等级变化数据
     * 使用独立的事务，确保数据一致性
     * 
     * @param result 等级变化结果
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveGradeChangeData(MemberGradeChangeDTO gradeChangeDTO,GradeChangeResult result, GradeChangeContext context) {

        // 批量保存等级变化记录
        if (result.getGradeChangeDetails() != null && !result.getGradeChangeDetails().isEmpty()) {
            gradeChangeDetailService.saveBatch(result.getGradeChangeDetails(), BATCH_SAVE_SIZE);
        }

        // 批量保存等级卡记录
        if (result.getGradeCards() != null && !result.getGradeCards().isEmpty()) {
            gradeCardService.saveBatch(result.getGradeCards(), BATCH_SAVE_SIZE);
        }

        // 批量保存权益记录
        if (result.getGradeRightsRecords() != null && !result.getGradeRightsRecords().isEmpty()) {
            gradeRightsRecordService.saveBatch(result.getGradeRightsRecords(), BATCH_SAVE_SIZE);
        }

        // 批量保存成长值记录
        if (result.getGrowthValueDetails() != null && !result.getGrowthValueDetails().isEmpty()) {
            growthValueDetailService.saveBatch(result.getGrowthValueDetails(), BATCH_SAVE_SIZE);
        }

        // 批量保存积分记录
        if (result.getIntegralDetails() != null && !result.getIntegralDetails().isEmpty()) {
            integralDetailService.saveBatch(result.getIntegralDetails(), BATCH_SAVE_SIZE);
        }

        // 处理未变化会员的权益
        if (CollUtil.isNotEmpty(result.getGradeUnchangedMembers())) {
            periodGiveGrowthValue(result.getGradeUnchangedMembers(), result.getEquitiesReceiveRecords(),
                   result.getChangedGradeInfos(),  context.getEquitiesMap(), result.getUpdateMemberIntegralMap(), gradeChangeDTO);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(result.getGradeUnchangedMembers());
        }

        // 处理变化会员的权益
        if (CollUtil.isNotEmpty(result.getGradeChangedMembers())) {
            periodGiveGrowthValue(result.getGradeChangedMembers(), result.getEquitiesReceiveRecords(),
                    result.getChangedGradeInfos(),context.getEquitiesMap(),  result.getUpdateMemberIntegralMap(), gradeChangeDTO);
            hsaOperationMemberInfoMapper.batchUpdateGrowth(result.getGradeChangedMembers());
        }

        // 批量更新积分
        batchUpdateIntegral(result.getUpdateMemberIntegralMap());

    }

    /**
     * 处理周期赠送成长值权益
     */
    private void periodGiveGrowthValue(List<HsaOperationMemberInfo> memberInfoList,
                                       List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords,
                                       Set<HsaMemberGradeInfo> gradeInfos,
                                       Map<String, List<HsaBusinessEquities>> equitiesMap,
                                       Map<String, Integer> updateMemberIntegralMap,
                                       MemberGradeChangeDTO gradeChangeDTO) {
        if (CollUtil.isEmpty(memberInfoList) || CollUtil.isEmpty(gradeInfos)) {
            log.warn("会员信息或等级信息为空，跳过周期赠送处理");
            return;
        }

        List<HsaGrowthValueDetail> growthValueDetails = new ArrayList<>();
        List<HsaIntegralDetail> integralDetails = new ArrayList<>();

        // 批量获取所有权益的已赠送记录，减少数据库查询
        Map<String, Map<String, Integer>> allEquitiesGrowthValueMap = batchGetEquitiesGrowthValueMap(equitiesMap, gradeInfos);

        for (HsaMemberGradeInfo gradeInfo : gradeInfos) {
            List<HsaBusinessEquities> businessEquitiesList = equitiesMap.get(gradeInfo.getGuid());
            if (CollUtil.isEmpty(businessEquitiesList)) {
                continue;
            }

            log.debug("处理等级 {} 的权益，权益数量：{}", gradeInfo.getName(), businessEquitiesList.size());

            for (HsaBusinessEquities businessEquities : businessEquitiesList) {
                processSingleEquity(businessEquities, gradeInfo, memberInfoList,
                        allEquitiesGrowthValueMap, updateMemberIntegralMap,
                        growthValueDetails, integralDetails, equitiesReceiveRecords, gradeChangeDTO);
            }
        }

        // 批量保存数据
        saveEquityDetails(growthValueDetails, integralDetails, equitiesReceiveRecords);
    }

    /**
     * 批量保存权益相关数据
     */
    private void saveEquityDetails(List<HsaGrowthValueDetail> growthValueDetails,
                                   List<HsaIntegralDetail> integralDetails,
                                   List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords) {
        if (CollUtil.isNotEmpty(growthValueDetails)) {
            hsaGrowthValueDetailService.saveBatch(growthValueDetails, BATCH_SAVE_SIZE);
            log.info("批量保存成长值详情记录：{} 条", growthValueDetails.size());
        }

        if (CollUtil.isNotEmpty(integralDetails)) {
            hsaIntegralDetailService.saveBatch(integralDetails, BATCH_SAVE_SIZE);
            log.info("批量保存积分详情记录：{} 条", integralDetails.size());
        }

        if (CollUtil.isNotEmpty(equitiesReceiveRecords)) {
            hsaMemberEquitiesReceiveRecordService.saveBatch(equitiesReceiveRecords, BATCH_SAVE_SIZE);
            log.info("批量保存权益领取记录：{} 条", equitiesReceiveRecords.size());
        }
    }


    /**
     * 批量获取权益成长值记录，减少数据库查询
     */
    private Map<String, Map<String, Integer>> batchGetEquitiesGrowthValueMap(
            Map<String, List<HsaBusinessEquities>> equitiesMap,
            Set<HsaMemberGradeInfo> gradeInfos) {

        Map<String, Map<String, Integer>> result = new HashMap<>();

        // 收集所有需要查询的权益GUID
        List<String> allEquitiesGuids = new ArrayList<>();

        for (HsaMemberGradeInfo gradeInfo : gradeInfos) {
            List<HsaBusinessEquities> businessEquitiesList = equitiesMap.get(gradeInfo.getGuid());
            if(CollUtil.isEmpty(businessEquitiesList)) {
                continue;
            }
            allEquitiesGuids.addAll(businessEquitiesList.stream().map(HsaBusinessEquities::getGuid).collect(Collectors.toList()));
        }

        // 批量查询所有权益的已赠送记录
        if (CollUtil.isNotEmpty(allEquitiesGuids)) {
            List<MemberGrowthValueRelationVO> allGrowthValueList = hsaMemberEquitiesReceiveRecordMapper.selectAllGrowthValueBatch(allEquitiesGuids);

            // 按权益GUID分组
            result = allGrowthValueList.stream()
                    .collect(Collectors.groupingBy(
                            MemberGrowthValueRelationVO::getGradeEquitiesGuid,
                            Collectors.toMap(
                                    MemberGrowthValueRelationVO::getMemberGuid,
                                    MemberGrowthValueRelationVO::getGrowthValue,
                                    (existing, replacement) -> existing
                            )
                    ));
        }

        return result;
    }

    /**
     * 处理单个权益的赠送逻辑
     */
    private void processSingleEquity(HsaBusinessEquities businessEquities,
                                     HsaMemberGradeInfo gradeInfo,
                                     List<HsaOperationMemberInfo> memberInfoList,
                                     Map<String, Map<String, Integer>> allEquitiesGrowthValueMap,
                                     Map<String, Integer> updateMemberIntegralMap,
                                     List<HsaGrowthValueDetail> growthValueDetails,
                                     List<HsaIntegralDetail> integralDetails,
                                     List<HsaMemberEquitiesReceiveRecord> equitiesReceiveRecords,
                                     MemberGradeChangeDTO gradeChangeDTO) {

        String equitiesGuid = businessEquities.getGuid();
        Map<String, Integer> memberGrowthValueMap = allEquitiesGrowthValueMap.getOrDefault(equitiesGuid, Collections.emptyMap());

        int type = businessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode()
                ? NumberConstant.NUMBER_1 : NumberConstant.NUMBER_0;

        for (HsaOperationMemberInfo memberInfo : memberInfoList) {
            // 获取当前会员当前权益下已经赠送的成长值
            Integer alreadyGivenValue = ObjectUtil.objToInt(memberGrowthValueMap.get(memberInfo.getGuid()));

            // 已经赠送超过相应成长值，跳过
            if (alreadyGivenValue >= businessEquities.getTotalGiveNumber()) {
                continue;
            }

            // 计算本次赠送成长值
            int giveGrowthValue = businessEquities.getTotalGiveNumber() - alreadyGivenValue;

            if (businessEquities.getEquitiesRuleType() == EquitiesRuleTypeEnum.GIVE_INTEGRAL.getCode()) {
                // 赠送积分
                processIntegralGift(memberInfo, gradeInfo, businessEquities, giveGrowthValue,
                        updateMemberIntegralMap, integralDetails);
            } else {
                // 赠送成长值
                processGrowthValueGift(memberInfo, gradeInfo, businessEquities, giveGrowthValue,
                        growthValueDetails, gradeChangeDTO);
            }

            // 记录权益领取记录
            HsaMemberEquitiesReceiveRecord receiveRecord = gradeChangeAssembler.getHsaMemberEquitiesReceiveRecord(
                    memberInfo.getOperatorGuid(), memberInfo.getGuid(), gradeInfo.getGuid(),
                    businessEquities.getGuid(), giveGrowthValue, type);
            equitiesReceiveRecords.add(receiveRecord);
        }
    }


    /**
     * 处理积分赠送
     */
    private void processIntegralGift(HsaOperationMemberInfo memberInfo,
                                     HsaMemberGradeInfo gradeInfo,
                                     HsaBusinessEquities businessEquities,
                                     int giveGrowthValue,
                                     Map<String, Integer> updateMemberIntegralMap,
                                     List<HsaIntegralDetail> integralDetails) {

        log.debug("会员 {} 获得积分权益赠送：{}", memberInfo.getGuid(), giveGrowthValue);

        memberInfo.setMemberIntegral(giveGrowthValue + memberInfo.getMemberIntegral());

        HsaIntegralDetail integralDetail = gradeChangeAssembler.getHsaIntegralValueDetail(
                memberInfo.getGuid(), SourceTypeEnum.ADD_BACKGROUND.getCode(),
                memberInfo.getOperSubjectGuid(), gradeInfo, businessEquities,
                giveGrowthValue, memberInfo.getMemberGrowthValue());
        integralDetails.add(integralDetail);

        // 更新积分映射
        updateMemberIntegralMap.merge(memberInfo.getGuid(), giveGrowthValue, Integer::sum);
    }

    /**
     * 处理成长值赠送
     */
    private void processGrowthValueGift(HsaOperationMemberInfo memberInfo,
                                        HsaMemberGradeInfo gradeInfo,
                                        HsaBusinessEquities businessEquities,
                                        int giveGrowthValue,
                                        List<HsaGrowthValueDetail> growthValueDetails,
                                        MemberGradeChangeDTO gradeChangeDTO) {

        log.debug("会员 {} 获得成长值权益赠送：{}", memberInfo.getGuid(), giveGrowthValue);

        memberInfo.setMemberGrowthValue(giveGrowthValue + memberInfo.getMemberGrowthValue());

        HsaGrowthValueDetail growthValueDetail = gradeChangeAssembler.getHsaGrowthValueDetail(
                memberInfo.getGuid(), SourceTypeEnum.ADD_BACKGROUND.getCode(),
                gradeInfo, businessEquities, giveGrowthValue,
                memberInfo.getMemberGrowthValue(), gradeChangeDTO);
        growthValueDetails.add(growthValueDetail);
    }


    /**
     * 批量更新会员积分（优化版本）
     * @param updateMemberIntegralMap 会员积分更新映射
     */
    private void batchUpdateIntegral(Map<String, Integer> updateMemberIntegralMap) {
        // 空值检查，防止空指针异常
        if (updateMemberIntegralMap == null || updateMemberIntegralMap.isEmpty()) {
            log.debug("积分更新映射为空，跳过积分更新");
            return;
        }

        List<UpdateIntegral> updateMemberIntegralList = Lists.newArrayList();

        // 构建更新列表
        updateMemberIntegralMap.forEach((memberGuid, integralValue) -> {
            if (memberGuid != null && integralValue != null) {
                UpdateIntegral updateIntegral = new UpdateIntegral();
                updateIntegral.setGuid(memberGuid);
                updateIntegral.setIntegralValue(integralValue);
                updateMemberIntegralList.add(updateIntegral);
            } else {
                log.warn("发现无效的积分更新数据，会员GUID：{}, 积分值：{}", memberGuid, integralValue);
            }
        });

        if (updateMemberIntegralList.isEmpty()) {
            log.warn("积分更新列表为空，跳过积分更新");
            return;
        }

        // 执行批量更新
        hsaOperationMemberInfoMapper.batchUpdateIntegral(updateMemberIntegralList);

    }

}
