package com.holderzone.member.queue.service.grade.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.holderzone.member.common.constant.NumberConstant;
import com.holderzone.member.common.constant.RedisKeyConstant;
import com.holderzone.member.common.constant.StringConstant;
import com.holderzone.member.common.dto.event.SendMemberGradeChangeEvent;
import com.holderzone.member.common.enums.BooleanEnum;
import com.holderzone.member.common.enums.SystemEnum;
import com.holderzone.member.common.enums.grade.GiftBagEquitiesRuleEnum;
import com.holderzone.member.common.enums.grade.UpgradeTypeEnum;
import com.holderzone.member.common.enums.growth.DataUnitEnum;
import com.holderzone.member.common.enums.member.*;
import com.holderzone.member.common.exception.MemberBaseException;
import com.holderzone.member.queue.config.mybatis.HolderBaseServiceImpl;
import com.holderzone.member.queue.dto.grade.GradeChangeContext;
import com.holderzone.member.queue.dto.grade.GradeChangeResult;
import com.holderzone.member.queue.dto.grade.GradeRightsProcessorDTO;
import com.holderzone.member.queue.dto.grade.MemberGradeChangeDTO;
import com.holderzone.member.queue.entity.*;
import com.holderzone.member.queue.event.processor.MemberGradeChangeProcessor;
import com.holderzone.member.queue.mapper.*;
import com.holderzone.member.queue.service.assembler.GradeChangeAssembler;
import com.holderzone.member.queue.service.cache.CacheService;
import com.holderzone.member.queue.service.grade.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员等级变化明细表
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
@Slf4j
public class HsaMemberGradeChangeDetailServiceImpl extends HolderBaseServiceImpl<HsaMemberGradeChangeDetailMapper, HsaMemberGradeChangeDetail>
        implements HsaMemberGradeChangeDetailService {


    @Resource
    private HsaGradeGiftBagMapper hsaGradeGiftBagMapper;

    @Resource
    private HsaMemberGradeInfoMapper hsaMemberGradeInfoMapper;

    @Resource
    private HsaMemberGradeRightsRecordMapper hsaMemberGradeRightsRecordMapper;

    @Resource
    private HsaGiftBagBaseInfoMapper hsaGiftBagBaseInfoMapper;

    @Resource
    private HsaControlledGradeStateMapper hsaControlledGradeStateMapper;

    @Resource
    private Executor queueGradeChangeThreadExecutor;

    @Resource
    private CacheService cacheService;

    @Resource
    private HsaGradeEquitiesService hsaGradeEquitiesService;

    @Resource
    private HsaGradeEquitiesMapper hsaGradeEquitiesMapper;

    @Resource
    private IHsaMemberGradeRelationService memberGradeRelationService;

    @Resource
    private GradeChangeAssembler gradeChangeAssembler;

    @Resource
    private GradeChangeDataSaveService gradeChangeDataSaveService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 会员等级变化 - 优化版本
     */
    @Override
    public void memberGradeChange(MemberGradeChangeDTO gradeChangeDTO) {
        // 生成请求唯一标识
        String requestId = generateRequestId(gradeChangeDTO);
        RLock lock = redissonClient.getLock(requestId);
        try {
            if (lock.isLocked()) {
                log.info("重复请求，跳过处理：{}", gradeChangeDTO);
                return;
            }
            lock.lock();
            // 1. 参数验证和初始化
            validateAndInitializeRequest(gradeChangeDTO);
            
            // 2. 检查等级体系状态
            if (!isGradeSystemEnabled(gradeChangeDTO)) {
                log.info("会员等级体系已关闭，跳过处理");
                return;
            }

            // 3. 批量获取基础数据
            GradeChangeContext context = buildGradeChangeContext(gradeChangeDTO);
            
            // 4. 处理会员等级变化
            GradeChangeResult result = processMemberGradeChanges(gradeChangeDTO, context);
            
            // 5. 批量保存数据
            gradeChangeDataSaveService.saveGradeChangeData(gradeChangeDTO,result,context);
            
            // 6. 处理后续业务
            if (CollUtil.isNotEmpty(result.getGradeChangedMembers())) {
                // 监听会员等级变化
                processGradeChangeMonitoring(gradeChangeDTO, result.getGradeChangedMembers(), context.getCurrentGradeInfoList());
            }else{
                processCacheCleanup(gradeChangeDTO);
            }
            
            log.info("会员等级变化处理完成，处理会员数量：{}", gradeChangeDTO.getHsaOperationMemberInfos().size());
            
        } catch (Exception e) {
            log.error("会员等级变化处理失败", e);
            handleGradeChangeError(gradeChangeDTO, e);
            throw new MemberBaseException("会员等级变化处理失败");
        }finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 生成请求唯一标识
     */
    private String generateRequestId(MemberGradeChangeDTO gradeChangeDTO) {
        List<String> memberGuids = gradeChangeDTO.getHsaOperationMemberInfos().stream()
                .map(HsaOperationMemberInfo::getGuid)
                .sorted()
                .collect(Collectors.toList());
        
        return String.join("_", 
                gradeChangeDTO.getOperSubjectGuid(),
                gradeChangeDTO.getRoleType(),
                String.valueOf(gradeChangeDTO.getSourceType()),
                String.valueOf(memberGuids.hashCode()));
    }

    /**
     * 参数验证和初始化
     */
    private void validateAndInitializeRequest(MemberGradeChangeDTO gradeChangeDTO) {
        if (ObjectUtils.isEmpty(gradeChangeDTO.getRoleType())) {
            gradeChangeDTO.setRoleType(RoleTypeEnum.MEMBER.name());
        }
        
        if (CollUtil.isEmpty(gradeChangeDTO.getHsaOperationMemberInfos())) {
            throw new IllegalArgumentException("会员信息列表不能为空");
        }
        
        log.info("开始处理会员等级变化，会员数量：{}", gradeChangeDTO.getHsaOperationMemberInfos().size());
    }

    /**
     * 检查等级体系是否启用
     */
    private boolean isGradeSystemEnabled(MemberGradeChangeDTO gradeChangeDTO) {
        HsaControlledGradeState gradeState = hsaControlledGradeStateMapper.selectOne(
                new LambdaQueryWrapper<HsaControlledGradeState>()
                        .eq(HsaControlledGradeState::getRoleType, gradeChangeDTO.getRoleType())
                        .eq(HsaControlledGradeState::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid())
                        .eq(HsaControlledGradeState::getIsDelete, BooleanEnum.FALSE.getCode())
        );
        
        return gradeState != null && gradeState.getState() == EnableEnum.ENABLE.getCode();
    }

    /**
     * 构建等级变化上下文（批量获取数据）
     */
    private GradeChangeContext buildGradeChangeContext(MemberGradeChangeDTO gradeChangeDTO) {
        GradeChangeContext context = new GradeChangeContext();
        context.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
        context.setRoleType(gradeChangeDTO.getRoleType());
        context.setSourceType(gradeChangeDTO.getSourceType());
        context.setNow(LocalDateTime.now());
        
        // 批量获取等级信息
        context.setCurrentGradeInfoList(getHsaMemberGradeInfos(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getRoleType()));
        context.setOldGradeInfoMap(getStringHsaMemberGradeInfoMap(gradeChangeDTO.getHsaOperationMemberInfos(), gradeChangeDTO.getRoleType()));
        
        // 批量获取权益信息
        context.setGradeEquitiesMap(getGradeEquitiesMap(gradeChangeDTO.getOperSubjectGuid()));
        context.setEquitiesRightsRecordMap(getEquitiesRightsRecordMap(
                gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getHsaOperationMemberInfos(), gradeChangeDTO.getRoleType()));
        
        // 获取礼包基础信息
        context.setGiftBagBaseInfo(hsaGiftBagBaseInfoMapper.selectOne(
                new LambdaQueryWrapper<HsaGiftBagBaseInfo>()
                        .eq(HsaGiftBagBaseInfo::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid())));
        
        // 获取周期赠送权益
        List<HsaBusinessEquities> periodEquities = hsaGradeEquitiesMapper.selectList(
                new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getSetPeriod, DataUnitEnum.FOREVER.getCode())
                        .in(HsaBusinessEquities::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                        .eq(HsaBusinessEquities::getEffective, BooleanEnum.TRUE.getCode())
                        .eq(HsaBusinessEquities::getOperSubjectGuid, gradeChangeDTO.getOperSubjectGuid()));
        context.setEquitiesMap(periodEquities.stream().collect(Collectors.groupingBy(HsaBusinessEquities::getMemberGradeInfoGuid)));
        
        return context;
    }

    /**
     * 处理会员等级变化核心逻辑
     */
    private GradeChangeResult processMemberGradeChanges(MemberGradeChangeDTO gradeChangeDTO, GradeChangeContext context) {
        GradeChangeResult result = new GradeChangeResult();
        
        // 获取成长值等级列表
        List<HsaMemberGradeInfo> growthGradeList = context.getCurrentGradeInfoList().stream()
                .filter(grade -> grade.getUpgradeType() == UpgradeTypeEnum.GROWTH_VALUE.getCode())
                .collect(Collectors.toList());
        
        // 分类处理会员
        List<HsaOperationMemberInfo> gradeChangedMembers = new ArrayList<>();
        List<HsaOperationMemberInfo> gradeUnchangedMembers = new ArrayList<>();
        
        for (HsaOperationMemberInfo memberInfo : gradeChangeDTO.getHsaOperationMemberInfos()) {
            HsaMemberGradeInfo newGradeInfo = calculateExtraAmount(memberInfo.getMemberGrowthValue(), growthGradeList);
            String currentGradeGuid = memberInfo.getMemberGradeInfoGuid();
            
            // 判断是否需要等级变化
            boolean needGradeChange = Objects.nonNull(gradeChangeDTO.getIssuerType()) || 
                    (!ObjectUtils.isEmpty(newGradeInfo) && !newGradeInfo.getGuid().equals(currentGradeGuid));
            
            if (needGradeChange) {
                processGradeChange(memberInfo, newGradeInfo, context, result);
                gradeChangedMembers.add(memberInfo);
            } else {
                // 只更新等级信息，不变化
                updateMemberGradeInfo(memberInfo, newGradeInfo);
                gradeUnchangedMembers.add(memberInfo);
            }
        }
        
        result.setGradeChangedMembers(gradeChangedMembers);
        result.setGradeUnchangedMembers(gradeUnchangedMembers);
        
        return result;
    }

    /**
     * 处理单个会员的等级变化
     */
    private void processGradeChange(HsaOperationMemberInfo memberInfo, HsaMemberGradeInfo newGradeInfo, 
                                   GradeChangeContext context, GradeChangeResult result) {
        HsaMemberGradeInfo oldGradeInfo = context.getOldGradeInfoMap().get(memberInfo.getMemberGradeInfoGuid());
        
        // 创建等级变化记录
        HsaMemberGradeChangeDetail changeDetail = new HsaMemberGradeChangeDetail();
        gradeChangeBusiness(memberInfo, newGradeInfo, oldGradeInfo, changeDetail, context.getNow());
        result.getGradeChangeDetails().add(changeDetail);
        
        // 创建等级卡记录
        HsaMemberGradeCard gradeCard = new HsaMemberGradeCard();
        gradeChangeAssembler.getHsaMemberGradeCard(memberInfo, newGradeInfo, gradeCard);
        result.getGradeCards().add(gradeCard);
        
        // 处理等级权益
        processGradeRights(memberInfo, oldGradeInfo, newGradeInfo, context, result);
        
        result.getChangedGradeInfos().add(newGradeInfo);
    }

    /**
     * 处理等级权益
     */
    private void processGradeRights(HsaOperationMemberInfo memberInfo, HsaMemberGradeInfo oldGradeInfo, 
                                   HsaMemberGradeInfo newGradeInfo, GradeChangeContext context, GradeChangeResult result) {
        // 处理升级礼包权益
        GradeRightsProcessorDTO processorDTO = getGradeRightsProcessorDTO(context, result);

        memberGradeRightsProcessor(processorDTO, memberInfo, oldGradeInfo, newGradeInfo, context.getCurrentGradeInfoList());
        
        // 处理其他等级权益
        String newGradeGuid = newGradeInfo.getGuid();
        if (context.getGradeEquitiesMap().containsKey(newGradeGuid)) {
            List<HsaBusinessEquities> businessEquities = context.getGradeEquitiesMap().get(newGradeGuid);
            List<HsaMemberGradeRightsRecord> existingRecords = getMemberEquitiesRecord(newGradeGuid, context.getEquitiesRightsRecordMap());
            
            memberGradeRightsProcessor(memberInfo, businessEquities, existingRecords,
                    context.getOperSubjectGuid(), result.getGradeRightsRecords(), context.getNow());
        }
    }

    private static GradeRightsProcessorDTO getGradeRightsProcessorDTO(GradeChangeContext context, GradeChangeResult result) {
        GradeRightsProcessorDTO processorDTO = new GradeRightsProcessorDTO();
        processorDTO.setUpdateMemberIntegralMap(result.getUpdateMemberIntegralMap());
        processorDTO.setNow(context.getNow());
        processorDTO.setHsaGiftBagBaseInfo(context.getGiftBagBaseInfo());
        processorDTO.setOperSubjectGuid(context.getOperSubjectGuid());
        processorDTO.setSourceType(context.getSourceType());
        processorDTO.setHsaMemberGradeRightsRecords(result.getGradeRightsRecords());
        processorDTO.setHsaGrowthValueDetails(result.getGrowthValueDetails());
        processorDTO.setHsaIntegralDetails(result.getIntegralDetails());
        return processorDTO;
    }

    /**
     * 更新会员等级信息（不变化的情况）
     */
    private void updateMemberGradeInfo(HsaOperationMemberInfo memberInfo, HsaMemberGradeInfo newGradeInfo) {
        memberInfo.setMemberGradeInfoGuid(Optional.ofNullable(newGradeInfo).map(HsaMemberGradeInfo::getGuid).orElse(null));
        memberInfo.setMemberGradeInfoName(Optional.ofNullable(newGradeInfo).map(HsaMemberGradeInfo::getName).orElse(null));
    }

    /**
     * 处理等级变化异常
     */
    private void handleGradeChangeError(MemberGradeChangeDTO gradeChangeDTO, Exception e) {
        boolean cacheCleared = clearGradeRelatedCache(gradeChangeDTO.getOperSubjectGuid(), gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType());
        if (!cacheCleared) {
            log.warn("异常处理时缓存清理失败，运营主体：{}", gradeChangeDTO.getOperSubjectGuid());
        }
        log.error("等级刷新失败，强制释放缓存", e);
    }

    /**
     * 获取会员权益记录
     */
    private List<HsaMemberGradeRightsRecord> getMemberEquitiesRecord(String gradeGuid,
                                                                     Map<String, List<HsaMemberGradeRightsRecord>> equitiesRightsRecordMap) {
        if (StringUtils.isEmpty(gradeGuid) || Objects.isNull(equitiesRightsRecordMap)) {
            log.debug("参数无效，返回空列表：gradeGuid={}, recordMap is null={}", 
                    gradeGuid, equitiesRightsRecordMap == null);
            return Collections.emptyList();
        }

        List<HsaMemberGradeRightsRecord> records = equitiesRightsRecordMap.get(gradeGuid);
        if (CollUtil.isEmpty(records)) {
            log.debug("未找到等级 {} 的权益记录", gradeGuid);
            return Collections.emptyList();
        }

        // 过滤出匹配的权益记录
        List<HsaMemberGradeRightsRecord> filteredRecords = records.stream()
                .filter(record -> gradeGuid.equals(record.getMemberGradeGuid()))
                .collect(Collectors.toList());

        log.debug("找到等级 {} 的权益记录数量：{}", gradeGuid, filteredRecords.size());
        return filteredRecords;
    }

    private Map<String, List<HsaMemberGradeRightsRecord>> getEquitiesRightsRecordMap(String operSubjectGuid,
                                                                                     List<HsaOperationMemberInfo> hsaOperationMemberInfoList,
                                                                                     String roleType) {
        if (CollUtil.isEmpty(hsaOperationMemberInfoList)) {
            return Collections.emptyMap();
        }
        List<String> memberGradeInfoGuidList = hsaOperationMemberInfoList.stream()
                .map(HsaOperationMemberInfo::getMemberGradeInfoGuid).collect(Collectors.toList());
        memberGradeInfoGuidList.addAll(getRelatedGradeGuids(hsaOperationMemberInfoList, roleType));
        return hsaMemberGradeRightsRecordMapper.selectList(new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                        .eq(HsaMemberGradeRightsRecord::getOperSubjectGuid, operSubjectGuid)
                        .in(HsaMemberGradeRightsRecord::getMemberInfoGuid, memberGradeInfoGuidList)
                        .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS.getCode()))
                .stream().collect(Collectors.groupingBy(HsaMemberGradeRightsRecord::getMemberInfoGuid));
    }

    private Map<String, List<HsaBusinessEquities>> getGradeEquitiesMap(String operSubjectGuid) {
        if (StringUtils.isEmpty(operSubjectGuid)) {
            return Collections.emptyMap();
        }
        //查询当前会员等级权益信息
        return hsaGradeEquitiesService.list(new LambdaQueryWrapper<HsaBusinessEquities>()
                        .eq(HsaBusinessEquities::getOperSubjectGuid, operSubjectGuid)
                        .eq(HsaBusinessEquities::getEffective, NumberConstant.NUMBER_1)
                        .in(HsaBusinessEquities::getIsDelete, NumberConstant.NUMBER_0, NumberConstant.NUMBER_2))
                .stream().collect(Collectors.groupingBy(HsaBusinessEquities::getMemberGradeInfoGuid));
    }

    /**
     * 清理等级相关缓存
     * 
     * @param operSubjectGuid 运营主体GUID
     * @param isRefresh 是否需要刷新缓存
     * @param roleType 角色类型
     * @return 缓存清理是否成功
     */
    private boolean clearGradeRelatedCache(String operSubjectGuid, Integer isRefresh, String roleType) {
        // 参数验证
        if (StringUtils.isEmpty(operSubjectGuid) || StringUtils.isEmpty(roleType)) {
            log.warn("参数无效，跳过缓存清理：operSubjectGuid={}, roleType={}", operSubjectGuid, roleType);
            return false;
        }

        // 检查是否需要刷新缓存
        if (isRefresh == null || !(BooleanEnum.TRUE.getCode() == isRefresh)) {
            log.debug("不需要刷新缓存，跳过清理操作");
            return true;
        }

        try {
            // 构建缓存键
            String gradeRefreshKey = buildCacheKey(RedisKeyConstant.GRADE_REFRESH, operSubjectGuid, roleType);
            String gradeInfoChangeKey = buildCacheKey(RedisKeyConstant.GRADE_INFO_CHANGE, operSubjectGuid, roleType);
            
            log.info("开始清理等级相关缓存，运营主体：{}, 角色类型：{}", operSubjectGuid, roleType);
            
            // 清理缓存
            boolean gradeRefreshCleared = clearCacheKey(gradeRefreshKey, "等级刷新缓存");
            boolean gradeInfoChangeCleared = clearCacheKey(gradeInfoChangeKey, "等级信息变化缓存");
            
            boolean allCleared = gradeRefreshCleared && gradeInfoChangeCleared;
            
            if (allCleared) {
                log.info("等级相关缓存清理成功，运营主体：{}, 角色类型：{}", operSubjectGuid, roleType);
            } else {
                log.warn("等级相关缓存清理部分失败，运营主体：{}, 角色类型：{}, 等级刷新：{}, 等级信息变化：{}", 
                        operSubjectGuid, roleType, gradeRefreshCleared, gradeInfoChangeCleared);
            }
            
            return allCleared;
            
        } catch (Exception e) {
            log.error("清理等级相关缓存失败，运营主体：{}, 角色类型：{}", operSubjectGuid, roleType, e);
            return false;
        }
    }

    /**
     * 构建缓存键
     * 
     * @param prefix 缓存键前缀
     * @param operSubjectGuid 运营主体GUID
     * @param roleType 角色类型
     * @return 完整的缓存键
     */
    private String buildCacheKey(String prefix, String operSubjectGuid, String roleType) {
        return String.join(StringConstant.COLON, prefix, operSubjectGuid, roleType);
    }

    /**
     * 清理单个缓存键
     * 
     * @param cacheKey 缓存键
     * @param cacheDescription 缓存描述（用于日志）
     * @return 清理是否成功
     */
    private boolean clearCacheKey(String cacheKey, String cacheDescription) {
        try {
            Boolean result = cacheService.cleanToken(cacheKey);
            boolean success = Boolean.TRUE.equals(result);
            
            if (success) {
                log.debug("{}清理成功：{}", cacheDescription, cacheKey);
            } else {
                log.warn("{}清理失败：{}", cacheDescription, cacheKey);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("清理{}时发生异常：{}", cacheDescription, cacheKey, e);
            return false;
        }
    }

    /**
     * 处理等级变化监听和后续业务
     * 
     * @param gradeChangeDTO 等级变化DTO
     * @param hsaOperationMemberInfos 操作会员信息列表
     * @param hsaMemberGradeInfoList 会员等级信息列表
     */
    private void processGradeChangeMonitoring(MemberGradeChangeDTO gradeChangeDTO, List<HsaOperationMemberInfo> hsaOperationMemberInfos,
                                            List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        // 参数验证
        if (gradeChangeDTO == null || CollUtil.isEmpty(hsaOperationMemberInfos) || CollUtil.isEmpty(hsaMemberGradeInfoList)) {
            log.warn("参数无效，跳过等级变化监听处理");
            return;
        }

        try {
            // 1. 筛选需要监听的会员
            List<HsaOperationMemberInfo> membersToMonitor = filterMembersForMonitoring(gradeChangeDTO, hsaOperationMemberInfos, hsaMemberGradeInfoList);
            
            // 2. 处理监听逻辑
            if (CollUtil.isNotEmpty(membersToMonitor)) {
                processGradeChangeEvent(gradeChangeDTO, membersToMonitor);
            } else {
                processCacheCleanup(gradeChangeDTO);
            }
            
            log.info("等级变化监听处理完成，运营主体：{}, 需要监听会员数：{}", 
                    gradeChangeDTO.getOperSubjectGuid(), membersToMonitor.size());
                    
        } catch (Exception e) {
            log.error("处理等级变化监听失败，运营主体：{}", gradeChangeDTO.getOperSubjectGuid(), e);
            // 异常情况下也要清理缓存
            processCacheCleanup(gradeChangeDTO);
        }
    }

    /**
     * 筛选需要监听的会员
     * 
     * @param gradeChangeDTO 等级变化DTO
     * @param hsaOperationMemberInfos 操作会员信息列表
     * @param hsaMemberGradeInfoList 会员等级信息列表
     * @return 需要监听的会员列表
     */
    private List<HsaOperationMemberInfo> filterMembersForMonitoring(MemberGradeChangeDTO gradeChangeDTO, 
                                                                   List<HsaOperationMemberInfo> hsaOperationMemberInfos,
                                                                   List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        List<HsaOperationMemberInfo> membersToMonitor = Lists.newArrayList();
        
        for (HsaOperationMemberInfo operationMemberInfo : hsaOperationMemberInfos) {
            try {
                // 检查是否需要跳过监听
                if (shouldSkipMonitoring(gradeChangeDTO)) {
                    continue;
                }
                
                // 计算会员当前等级
                HsaMemberGradeInfo currentGradeInfo = calculateExtraAmount(operationMemberInfo.getMemberGrowthValue(), hsaMemberGradeInfoList);
                if (currentGradeInfo == null) {
                    log.warn("无法计算会员等级，跳过监听，会员GUID：{}", operationMemberInfo.getGuid());
                    continue;
                }
                
                // 检查等级是否发生变化
                if (hasGradeChanged(currentGradeInfo, operationMemberInfo)) {
                    membersToMonitor.add(operationMemberInfo);
                }
                
            } catch (Exception e) {
                log.error("筛选会员监听时发生异常，会员GUID：{}", operationMemberInfo.getGuid(), e);
            }
        }
        
        return membersToMonitor;
    }

    /**
     * 检查是否需要跳过监听
     * 
     * @param gradeChangeDTO 等级变化DTO
     * @return 是否跳过监听
     */
    private boolean shouldSkipMonitoring(MemberGradeChangeDTO gradeChangeDTO) {
        // 合作伙伴系统且有指定等级GUID时跳过监听
        return !ObjectUtils.isEmpty(gradeChangeDTO.getSystem()) 
                && SystemEnum.PARTNER.getCode() == gradeChangeDTO.getSystem()
                && !StringUtils.isEmpty(gradeChangeDTO.getMemberInfoGradeGuid());
    }

    /**
     * 检查等级是否发生变化
     * 
     * @param currentGradeInfo 当前等级信息
     * @param operationMemberInfo 操作会员信息
     * @return 是否发生变化
     */
    private boolean hasGradeChanged(HsaMemberGradeInfo currentGradeInfo, HsaOperationMemberInfo operationMemberInfo) {
        return !currentGradeInfo.getGuid().equals(operationMemberInfo.getMemberGradeInfoGuid());
    }

    /**
     * 处理等级变化事件
     * 
     * @param gradeChangeDTO 等级变化DTO
     * @param membersToMonitor 需要监听的会员列表
     */
    private void processGradeChangeEvent(MemberGradeChangeDTO gradeChangeDTO, List<HsaOperationMemberInfo> membersToMonitor) {
        queueGradeChangeThreadExecutor.execute(() -> {
            try {
                SendMemberGradeChangeEvent event = buildGradeChangeEvent(gradeChangeDTO, membersToMonitor);
                log.info("开始处理会员等级变化事件，运营主体：{}, 会员数量：{}", 
                        gradeChangeDTO.getOperSubjectGuid(), membersToMonitor.size());
                new MemberGradeChangeProcessor(event).execute();
                log.info("会员等级变化事件处理完成，运营主体：{}", gradeChangeDTO.getOperSubjectGuid());
            } catch (Exception e) {
                log.error("处理会员等级变化事件失败，运营主体：{}", gradeChangeDTO.getOperSubjectGuid(), e);
            }
        });
    }

    /**
     * 构建等级变化事件
     * 
     * @param gradeChangeDTO 等级变化DTO
     * @param membersToMonitor 需要监听的会员列表
     * @return 等级变化事件
     */
    private SendMemberGradeChangeEvent buildGradeChangeEvent(MemberGradeChangeDTO gradeChangeDTO, List<HsaOperationMemberInfo> membersToMonitor) {
        SendMemberGradeChangeEvent event = new SendMemberGradeChangeEvent();
        event.setMemberGuidList(membersToMonitor.stream()
                .map(HsaOperationMemberInfo::getGuid)
                .collect(Collectors.toList()));
        event.setOperSubjectGuid(gradeChangeDTO.getOperSubjectGuid());
        event.setSourceType(gradeChangeDTO.getSourceType());
        event.setIsRefresh(gradeChangeDTO.getIsRefresh());
        return event;
    }

    /**
     * 处理缓存清理
     * 
     * @param gradeChangeDTO 等级变化DTO
     */
    private void processCacheCleanup(MemberGradeChangeDTO gradeChangeDTO) {
        boolean cacheCleared = clearGradeRelatedCache(gradeChangeDTO.getOperSubjectGuid(), 
                gradeChangeDTO.getIsRefresh(), gradeChangeDTO.getRoleType());
        if (!cacheCleared) {
            log.warn("等级变化监听时缓存清理失败，运营主体：{}", gradeChangeDTO.getOperSubjectGuid());
        } else {
            log.debug("等级变化监听时缓存清理成功，运营主体：{}", gradeChangeDTO.getOperSubjectGuid());
        }
    }

    /**
     * 封装业务数据
     *
     * @param hsaOperationMemberInfo     hsaOperationMemberInfo
     * @param afterMemberGradeInfo       会员之后等级
     * @param beforeMemberGradeInfo      会员之前等级
     * @param hsaMemberGradeChangeDetail hsaMemberGradeDetail
     */
    private void gradeChangeBusiness(HsaOperationMemberInfo hsaOperationMemberInfo, HsaMemberGradeInfo afterMemberGradeInfo,
                                     HsaMemberGradeInfo beforeMemberGradeInfo, HsaMemberGradeChangeDetail hsaMemberGradeChangeDetail,
                                     LocalDateTime localDateTime) {
        gradeChangeAssembler.getMemberGradeChangeDetail(hsaOperationMemberInfo, afterMemberGradeInfo, beforeMemberGradeInfo, hsaMemberGradeChangeDetail, localDateTime);
        hsaOperationMemberInfo.setMemberGradeInfoGuid(afterMemberGradeInfo.getGuid())
                .setMemberGradeInfoName(afterMemberGradeInfo.getName());
        hsaMemberGradeChangeDetail.setCurrentGrowthValue(hsaOperationMemberInfo.getMemberGrowthValue());
        hsaOperationMemberInfo.setUpgradeTime(localDateTime);
    }

    /**
     * 获取会员等级信息映射
     */
    private Map<String, HsaMemberGradeInfo> getStringHsaMemberGradeInfoMap(List<HsaOperationMemberInfo> memberInfoList, String roleType) {
        if (CollUtil.isEmpty(memberInfoList)) {
            log.warn("会员信息列表为空，返回空映射");
            return Collections.emptyMap();
        }

        try {
            // 收集所有需要查询的等级GUID
            Set<String> allGradeGuids = new HashSet<>();
            
            // 添加会员当前的等级GUID
            memberInfoList.stream()
                    .map(HsaOperationMemberInfo::getMemberGradeInfoGuid)
                    .filter(Objects::nonNull)
                    .forEach(allGradeGuids::add);
            
            // 添加关联的等级GUID
            allGradeGuids.addAll(getRelatedGradeGuids(memberInfoList, roleType));
            
            if (allGradeGuids.isEmpty()) {
                log.warn("未找到任何等级GUID，返回空映射");
                return Collections.emptyMap();
            }
            
            // 批量查询等级信息
            List<HsaMemberGradeInfo> gradeInfoList = hsaMemberGradeInfoMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .in(HsaMemberGradeInfo::getGuid, allGradeGuids));
            
            // 转换为映射
            Map<String, HsaMemberGradeInfo> result = gradeInfoList.stream()
                    .collect(Collectors.toMap(
                            HsaMemberGradeInfo::getGuid, 
                            Function.identity(), 
                            (existing, replacement) -> existing
                    ));
            
            log.debug("成功获取 {} 个等级信息映射", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取会员等级信息映射失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取关联的等级GUID列表
     */
    private List<String> getRelatedGradeGuids(List<HsaOperationMemberInfo> memberInfoList, String roleType) {
        try {
            List<String> memberGuids = memberInfoList.stream()
                    .map(HsaOperationMemberInfo::getGuid)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<HsaMemberGradeRelation> relationList = memberGradeRelationService.listByMemberGuidList(memberGuids, roleType);
            
            if (CollectionUtils.isEmpty(relationList)) {
                return Collections.emptyList();
            }
            
            return relationList.stream()
                    .map(HsaMemberGradeRelation::getMemberInfoGradeGuid)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取关联等级GUID失败", e);
            return Collections.emptyList();
        }
    }


    /**
     * 获取运营主体的会员等级信息列表
     */
    private List<HsaMemberGradeInfo> getHsaMemberGradeInfos(String operSubjectGuid, String roleType) {
        if (StringUtils.isEmpty(operSubjectGuid) || StringUtils.isEmpty(roleType)) {
            log.warn("参数无效：operSubjectGuid={}, roleType={}", operSubjectGuid, roleType);
            return Collections.emptyList();
        }

        try {

            // 查询数据库
            List<HsaMemberGradeInfo> gradeInfoList = hsaMemberGradeInfoMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeInfo>()
                            .eq(HsaMemberGradeInfo::getOperSubjectGuid, operSubjectGuid)
                            .eq(HsaMemberGradeInfo::getRoleType, roleType)
                            .in(HsaMemberGradeInfo::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                            .eq(HsaMemberGradeInfo::getEffective, BooleanEnum.TRUE.getCode())
                            .eq(HsaMemberGradeInfo::getType, GradeTypeEnum.FREE.getCode())
                            .orderByAsc(HsaMemberGradeInfo::getVipGrade));
            
            log.debug("成功获取运营主体 {} 的等级信息，数量：{}", operSubjectGuid, gradeInfoList.size());
            return gradeInfoList;
            
        } catch (Exception e) {
            log.error("获取会员等级信息失败：operSubjectGuid={}, roleType={}", operSubjectGuid, roleType, e);
            return Collections.emptyList();
        }
    }


    /**
     * 根据会员成长值计算对应的等级信息
     * 使用二分查找算法，时间复杂度从 O(n) 优化为 O(log n)
     *
     * @param memberGrowthValue      会员成长值
     * @param hsaMemberGradeInfoList 等级信息列表（已按成长值升序排列）
     * @return 对应的等级信息，如果找不到则返回 null
     */
    private HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        // 参数验证
        if (memberGrowthValue == null || hsaMemberGradeInfoList == null || hsaMemberGradeInfoList.isEmpty()) {
            log.warn("参数无效：memberGrowthValue={}, gradeInfoList size={}", 
                    memberGrowthValue, hsaMemberGradeInfoList != null ? hsaMemberGradeInfoList.size() : 0);
            return null;
        }

        // 如果成长值小于第一个等级的要求，返回第一个等级
        if (memberGrowthValue < hsaMemberGradeInfoList.get(0).getGrowthValue()) {
            return hsaMemberGradeInfoList.get(0);
        }

        // 如果成长值大于等于最后一个等级的要求，返回最后一个等级
        HsaMemberGradeInfo lastGrade = hsaMemberGradeInfoList.get(hsaMemberGradeInfoList.size() - 1);
        if (memberGrowthValue >= lastGrade.getGrowthValue()) {
            return lastGrade;
        }

        // 使用二分查找找到合适的等级
        return findGradeByBinarySearch(memberGrowthValue, hsaMemberGradeInfoList);
    }

    /**
     * 使用二分查找算法查找对应的等级信息
     * 
     * @param memberGrowthValue      会员成长值
     * @param hsaMemberGradeInfoList 等级信息列表
     * @return 对应的等级信息
     */
    private HsaMemberGradeInfo findGradeByBinarySearch(Integer memberGrowthValue, List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        int left = 0;
        int right = hsaMemberGradeInfoList.size() - 1;
        HsaMemberGradeInfo result = null;

        while (left <= right) {
            int mid = left + (right - left) / 2;
            HsaMemberGradeInfo midGrade = hsaMemberGradeInfoList.get(mid);
            
            if (memberGrowthValue >= midGrade.getGrowthValue()) {
                // 成长值大于等于当前等级要求，继续向右查找
                result = midGrade;
                left = mid + 1;
            } else {
                // 成长值小于当前等级要求，向左查找
                right = mid - 1;
            }
        }

        return result;
    }

    /**
     * 处理会员等级权益
     * @param gradeRightsProcessorDTO 等级权益处理DTO
     * @param operationMemberInfo 操作会员信息
     * @param beforeChangeGradeVipGrade 变化前等级
     * @param afterChangeGradeVipGrade 变化后等级
     * @param hsaMemberGradeInfoList 会员等级信息列表
     */
    public void memberGradeRightsProcessor(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                           HsaOperationMemberInfo operationMemberInfo,
                                           HsaMemberGradeInfo beforeChangeGradeVipGrade,
                                           HsaMemberGradeInfo afterChangeGradeVipGrade,
                                           List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        // 参数验证
        if (!validateGradeRightsProcessorParams(gradeRightsProcessorDTO, operationMemberInfo, 
                beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList)) {
            return;
        }

        try {
            // 1. 获取范围内的等级信息
            List<HsaMemberGradeInfo> scopeMemberGradeInfoList = getHsaMemberGradeInfos(
                    gradeRightsProcessorDTO, beforeChangeGradeVipGrade, afterChangeGradeVipGrade, 
                    hsaMemberGradeInfoList, gradeRightsProcessorDTO.getHsaGiftBagBaseInfo());
            
            if (CollUtil.isEmpty(scopeMemberGradeInfoList)) {
                log.debug("范围内等级信息为空，跳过权益处理，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
                return;
            }

            // 2. 获取升级礼包
            List<HsaGradeGiftBag> gradeGiftBags = getGradeGiftBags(scopeMemberGradeInfoList);
            if (CollUtil.isEmpty(gradeGiftBags)) {
                log.debug("升级礼包为空，跳过权益处理，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
                return;
            }

            // 3. 处理礼包赠送
            processGiftBagDistribution(gradeRightsProcessorDTO, operationMemberInfo, 
                    scopeMemberGradeInfoList, gradeGiftBags, afterChangeGradeVipGrade);
            
            log.info("会员等级权益处理完成，运营主体：{}, 会员：{}, 处理礼包数：{}", 
                    gradeRightsProcessorDTO.getOperSubjectGuid(), operationMemberInfo.getGuid(), gradeGiftBags.size());
                    
        } catch (Exception e) {
            log.error("处理会员等级权益失败，运营主体：{}, 会员：{}", 
                    gradeRightsProcessorDTO.getOperSubjectGuid(), operationMemberInfo.getGuid(), e);
        }
    }

    /**
     * 验证等级权益处理参数
     */
    private boolean validateGradeRightsProcessorParams(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                                      HsaOperationMemberInfo operationMemberInfo,
                                                      HsaMemberGradeInfo beforeChangeGradeVipGrade,
                                                      HsaMemberGradeInfo afterChangeGradeVipGrade,
                                                      List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        if (gradeRightsProcessorDTO == null) {
            log.warn("等级权益处理DTO为空，跳过处理");
            return false;
        }
        
        if (operationMemberInfo == null) {
            log.warn("操作会员信息为空，跳过处理");
            return false;
        }
        
        if (afterChangeGradeVipGrade == null) {
            log.warn("变化后等级为空，跳过处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        if (CollUtil.isEmpty(hsaMemberGradeInfoList)) {
            log.warn("会员等级信息列表为空，跳过处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        return true;
    }

    /**
     * 获取升级礼包
     */
    private List<HsaGradeGiftBag> getGradeGiftBags(List<HsaMemberGradeInfo> scopeMemberGradeInfoList) {
        List<String> memberGradeInfoGuidList = scopeMemberGradeInfoList.stream()
                .map(HsaMemberGradeInfo::getGuid)
                .collect(Collectors.toList());

        List<HsaGradeGiftBag> gradeGiftBags = hsaGradeGiftBagMapper.selectList(new LambdaQueryWrapper<HsaGradeGiftBag>()
                .in(HsaGradeGiftBag::getMemberGradeInfoGuid, memberGradeInfoGuidList)
                .in(HsaGradeGiftBag::getIsDelete, BooleanEnum.FALSE.getCode(), NumberConstant.NUMBER_2)
                .eq(HsaGradeGiftBag::getEffective, NumberConstant.NUMBER_1));
        
        log.info("获取升级礼包，等级GUID列表：{}, 礼包数量：{}", memberGradeInfoGuidList, gradeGiftBags.size());
        return gradeGiftBags;
    }

    /**
     * 处理礼包赠送
     */
    private void processGiftBagDistribution(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                           HsaOperationMemberInfo operationMemberInfo,
                                           List<HsaMemberGradeInfo> scopeMemberGradeInfoList,
                                           List<HsaGradeGiftBag> gradeGiftBags,
                                           HsaMemberGradeInfo afterChangeGradeVipGrade) {
        // 构建等级信息映射
        Map<String, HsaMemberGradeInfo> gradeInfoMap = scopeMemberGradeInfoList.stream()
                .collect(Collectors.toMap(HsaMemberGradeInfo::getGuid, Function.identity(), (entity1, entity2) -> entity1));

        // 获取会员已赠送权益记录
        List<String> gradeGiftBagGuids = gradeGiftBags.stream()
                .map(HsaGradeGiftBag::getGuid)
                .collect(Collectors.toList());
        Map<String, HsaMemberGradeRightsRecord> existingRightsRecordMap = getMemberGradeRightsRecordMap(operationMemberInfo, gradeGiftBagGuids);

        // 获取已处理的权益GUID列表
        List<String> processedRightsGuids = gradeRightsProcessorDTO.getHsaMemberGradeRightsRecords().stream()
                .map(HsaMemberGradeRightsRecord::getRightsGuid)
                .collect(Collectors.toList());

        // 处理每个礼包
        for (HsaGradeGiftBag gradeGiftBag : gradeGiftBags) {
            try {
                processSingleGiftBag(gradeRightsProcessorDTO, operationMemberInfo, gradeGiftBag, 
                        gradeInfoMap, existingRightsRecordMap, processedRightsGuids, afterChangeGradeVipGrade);
            } catch (Exception e) {
                log.error("处理单个礼包失败，礼包GUID：{}, 会员：{}", gradeGiftBag.getGuid(), operationMemberInfo.getGuid(), e);
            }
        }
    }

    /**
     * 处理单个礼包
     */
    private void processSingleGiftBag(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                     HsaOperationMemberInfo operationMemberInfo,
                                     HsaGradeGiftBag gradeGiftBag,
                                     Map<String, HsaMemberGradeInfo> gradeInfoMap,
                                     Map<String, HsaMemberGradeRightsRecord> existingRightsRecordMap,
                                     List<String> processedRightsGuids,
                                     HsaMemberGradeInfo afterChangeGradeVipGrade) {
        HsaMemberGradeInfo memberGradeInfo = gradeInfoMap.get(gradeGiftBag.getMemberGradeInfoGuid());
        
        // 双重校验：检查是否已经赠送过
        if (shouldSkipGiftBag(gradeGiftBag, existingRightsRecordMap, processedRightsGuids)) {
            log.debug("礼包已赠送过，跳过处理，礼包GUID：{}, 会员：{}", gradeGiftBag.getGuid(), operationMemberInfo.getGuid());
            return;
        }

        // 创建权益记录
        HsaMemberGradeRightsRecord rightsRecord = gradeChangeAssembler.getHsaMemberGradeRightsRecord(
                gradeRightsProcessorDTO, operationMemberInfo, gradeGiftBag);
        gradeRightsProcessorDTO.getHsaMemberGradeRightsRecords().add(rightsRecord);

        // 根据礼包类型处理
        if (gradeGiftBag.getType() == NumberConstant.NUMBER_0) {
            processGrowthValueGift(gradeRightsProcessorDTO, operationMemberInfo, gradeGiftBag, 
                    memberGradeInfo, afterChangeGradeVipGrade);
        } else {
            processIntegralGift(gradeRightsProcessorDTO, operationMemberInfo, gradeGiftBag, 
                    memberGradeInfo, afterChangeGradeVipGrade);
        }
    }

    /**
     * 检查是否应该跳过礼包处理
     */
    private boolean shouldSkipGiftBag(HsaGradeGiftBag gradeGiftBag,
                                     Map<String, HsaMemberGradeRightsRecord> existingRightsRecordMap,
                                     List<String> processedRightsGuids) {
        // 检查是否已存在权益记录
        if (CollUtil.isNotEmpty(existingRightsRecordMap) && 
            existingRightsRecordMap.containsKey(gradeGiftBag.getMemberGradeInfoGuid())) {
            return true;
        }
        
        // 检查是否已处理过
        return CollUtil.isNotEmpty(processedRightsGuids) && 
               processedRightsGuids.contains(gradeGiftBag.getGuid());
    }

    /**
     * 处理成长值礼包
     */
    private void processGrowthValueGift(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                       HsaOperationMemberInfo operationMemberInfo,
                                       HsaGradeGiftBag gradeGiftBag,
                                       HsaMemberGradeInfo memberGradeInfo,
                                       HsaMemberGradeInfo afterChangeGradeVipGrade) {
        // 更新会员成长值
        operationMemberInfo.setMemberGrowthValue(operationMemberInfo.getMemberGrowthValue() + gradeGiftBag.getValue());
        
        // 创建成长值明细记录
        HsaGrowthValueDetail growthValueDetail = gradeChangeAssembler.getHsaGrowthValueDetail(
                operationMemberInfo, gradeRightsProcessorDTO.getSourceType(), 
                gradeRightsProcessorDTO.getOperSubjectGuid(), gradeGiftBag, memberGradeInfo, 
                gradeRightsProcessorDTO.getHsaGiftBagBaseInfo(), afterChangeGradeVipGrade);
        gradeRightsProcessorDTO.getHsaGrowthValueDetails().add(growthValueDetail);
        
        log.debug("处理成长值礼包，礼包GUID：{}, 成长值：{}, 会员：{}", 
                gradeGiftBag.getGuid(), gradeGiftBag.getValue(), operationMemberInfo.getGuid());
    }

    /**
     * 处理积分礼包
     */
    private void processIntegralGift(GradeRightsProcessorDTO gradeRightsProcessorDTO,
                                    HsaOperationMemberInfo operationMemberInfo,
                                    HsaGradeGiftBag gradeGiftBag,
                                    HsaMemberGradeInfo memberGradeInfo,
                                    HsaMemberGradeInfo afterChangeGradeVipGrade) {
        // 更新会员积分
        operationMemberInfo.setMemberIntegral(operationMemberInfo.getMemberIntegral() + gradeGiftBag.getValue());
        
        // 更新积分映射
        Integer giftIntegral = Optional.of(gradeGiftBag.getValue()).orElse(0);
        updateMemberIntegralMap(operationMemberInfo, gradeRightsProcessorDTO.getUpdateMemberIntegralMap(), giftIntegral);
        
        // 创建积分明细记录
        HsaIntegralDetail integralDetail = gradeChangeAssembler.getHsaIntegralValueDetail(
                operationMemberInfo, gradeRightsProcessorDTO.getSourceType(), 
                gradeRightsProcessorDTO.getOperSubjectGuid(), gradeGiftBag, memberGradeInfo, 
                gradeRightsProcessorDTO.getHsaGiftBagBaseInfo(), afterChangeGradeVipGrade);
        gradeRightsProcessorDTO.getHsaIntegralDetails().add(integralDetail);
        
        log.debug("处理积分礼包，礼包GUID：{}, 积分：{}, 会员：{}", 
                gradeGiftBag.getGuid(), gradeGiftBag.getValue(), operationMemberInfo.getGuid());
    }

    /**
     * 获取范围内的会员等级信息
     * @param gradeRightsProcessorDTO 等级权益处理DTO
     * @param beforeChangeGradeVipGrade 变化前等级
     * @param afterChangeGradeVipGrade 变化后等级
     * @param hsaMemberGradeInfoList 会员等级信息列表
     * @param hsaGiftBagBaseInfo 礼包基础信息
     * @return 范围内的等级信息列表
     */
    private static List<HsaMemberGradeInfo> getHsaMemberGradeInfos(GradeRightsProcessorDTO gradeRightsProcessorDTO, 
                                                                   HsaMemberGradeInfo beforeChangeGradeVipGrade, 
                                                                   HsaMemberGradeInfo afterChangeGradeVipGrade, 
                                                                   List<HsaMemberGradeInfo> hsaMemberGradeInfoList, 
                                                                   HsaGiftBagBaseInfo hsaGiftBagBaseInfo) {
        // 参数验证
        if (gradeRightsProcessorDTO == null) {
            log.warn("等级权益处理DTO为空，无法获取等级信息");
            return Collections.emptyList();
        }
        
        if (CollUtil.isEmpty(hsaMemberGradeInfoList)) {
            log.warn("会员等级信息列表为空，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }
        
        if (afterChangeGradeVipGrade == null) {
            log.warn("变化后等级为空，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }

        // 检查礼包基础信息
        if (Objects.isNull(hsaGiftBagBaseInfo)) {
            log.info("等级升级礼包基础信息不存在，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }
        
        // 检查权益规则
        if (hsaGiftBagBaseInfo.getEquitiesRule() == GiftBagEquitiesRuleEnum.SELF_COLLECTION.getCode()) {
            log.info("该主体下升级礼包为自主领取，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
            return Collections.emptyList();
        }

        try {
            // 获取范围内等级
            List<HsaMemberGradeInfo> scopeMemberGradeInfoList = getHsaMemberGradeInfos(
                    beforeChangeGradeVipGrade, afterChangeGradeVipGrade, hsaMemberGradeInfoList);
            
            log.info("获取范围内等级，运营主体：{}, 变化前等级：{}, 变化后等级：{}, 范围内等级数：{}", 
                    gradeRightsProcessorDTO.getOperSubjectGuid(), 
                    beforeChangeGradeVipGrade != null ? beforeChangeGradeVipGrade.getGuid() : "null",
                    afterChangeGradeVipGrade.getGuid(), 
                    scopeMemberGradeInfoList.size());
            
            if (CollUtil.isEmpty(scopeMemberGradeInfoList)) {
                log.info("此等级区间不存在等级，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid());
                return Collections.emptyList();
            }
            
            return scopeMemberGradeInfoList;
            
        } catch (Exception e) {
            log.error("获取范围内等级信息失败，运营主体：{}", gradeRightsProcessorDTO.getOperSubjectGuid(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 更新会员积分映射
     * @param operationMemberInfo 操作会员信息
     * @param updateMemberIntegralMap 积分更新映射
     * @param giftIntegral 赠送积分
     */
    private static void updateMemberIntegralMap(HsaOperationMemberInfo operationMemberInfo, 
                                               Map<String, Integer> updateMemberIntegralMap, 
                                               Integer giftIntegral) {
        // 参数验证
        if (operationMemberInfo == null || StringUtils.isEmpty(operationMemberInfo.getGuid())) {
            log.warn("操作会员信息无效，无法更新积分映射");
            return;
        }
        
        if (updateMemberIntegralMap == null) {
            log.warn("积分更新映射为空，无法更新积分，会员：{}", operationMemberInfo.getGuid());
            return;
        }
        
        if (giftIntegral == null || giftIntegral <= 0) {
            log.debug("赠送积分为空或无效，跳过更新，会员：{}, 积分：{}", operationMemberInfo.getGuid(), giftIntegral);
            return;
        }

        try {
            String memberGuid = operationMemberInfo.getGuid();
            
            // 获取当前积分并累加
            Integer currentIntegral = updateMemberIntegralMap.getOrDefault(memberGuid, 0);
            Integer newIntegral = currentIntegral + giftIntegral;
            
            // 更新映射
            updateMemberIntegralMap.put(memberGuid, newIntegral);
            
            log.debug("更新会员积分映射，会员：{}, 原积分：{}, 赠送积分：{}, 新积分：{}", 
                    memberGuid, currentIntegral, giftIntegral, newIntegral);
                    
        } catch (Exception e) {
            log.error("更新会员积分映射失败，会员：{}, 赠送积分：{}", 
                    operationMemberInfo.getGuid(), giftIntegral, e);
        }
    }

    /**
     * 根据等级范围获取会员等级信息
     * @param beforeChangeGradeVipGrade 变化前等级
     * @param afterChangeGradeVipGrade 变化后等级
     * @param hsaMemberGradeInfoList 会员等级信息列表
     * @return 范围内的等级信息列表
     */
    private static List<HsaMemberGradeInfo> getHsaMemberGradeInfos(HsaMemberGradeInfo beforeChangeGradeVipGrade, 
                                                                   HsaMemberGradeInfo afterChangeGradeVipGrade, 
                                                                   List<HsaMemberGradeInfo> hsaMemberGradeInfoList) {
        // 参数验证
        if (afterChangeGradeVipGrade == null) {
            log.warn("变化后等级为空，无法获取等级范围");
            return Collections.emptyList();
        }
        
        if (CollUtil.isEmpty(hsaMemberGradeInfoList)) {
            log.warn("会员等级信息列表为空，无法获取等级范围");
            return Collections.emptyList();
        }

        try {
            List<HsaMemberGradeInfo> scopeMemberGradeInfoList;
            
            if (Objects.nonNull(beforeChangeGradeVipGrade)) {
                // 有变化前等级：获取从变化前等级到变化后等级之间的所有等级
                scopeMemberGradeInfoList = hsaMemberGradeInfoList.stream()
                        .filter(gradeInfo -> {
                            int vipGrade = gradeInfo.getVipGrade();
                            return vipGrade > beforeChangeGradeVipGrade.getVipGrade() && vipGrade <= afterChangeGradeVipGrade.getVipGrade();
                        })
                        .collect(Collectors.toList());
                        
                log.debug("获取等级范围（有变化前等级），变化前：{}, 变化后：{}, 范围内等级数：{}", 
                        beforeChangeGradeVipGrade.getVipGrade(), 
                        afterChangeGradeVipGrade.getVipGrade(), 
                        scopeMemberGradeInfoList.size());
            } else {
                // 无变化前等级：获取从等级2到变化后等级之间的所有等级
                scopeMemberGradeInfoList = hsaMemberGradeInfoList.stream()
                        .filter(gradeInfo -> {
                            int vipGrade = gradeInfo.getVipGrade();
                            return vipGrade >= NumberConstant.NUMBER_1 && vipGrade <= afterChangeGradeVipGrade.getVipGrade();
                        })
                        .collect(Collectors.toList());
                        
                log.debug("获取等级范围（无变化前等级），变化后：{}, 范围内等级数：{}", 
                        afterChangeGradeVipGrade.getVipGrade(), 
                        scopeMemberGradeInfoList.size());
            }
            
            return scopeMemberGradeInfoList;
            
        } catch (Exception e) {
            log.error("获取等级范围失败，变化前等级：{}, 变化后等级：{}", 
                    beforeChangeGradeVipGrade != null ? beforeChangeGradeVipGrade.getVipGrade() : "null",
                    afterChangeGradeVipGrade.getVipGrade(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理会员等级权益领取
     * @param operationMemberInfo 操作会员信息
     * @param hsaBusinessEquities 业务权益列表
     * @param equitiesRightsRecords 权益记录列表
     * @param operSubjectGuid 运营主体GUID
     * @param hsaMemberGradeRightsRecords 会员等级权益记录列表
     * @param localDateTime 本地时间
     */
    public void memberGradeRightsProcessor(HsaOperationMemberInfo operationMemberInfo, 
                                           List<HsaBusinessEquities> hsaBusinessEquities,
                                           List<HsaMemberGradeRightsRecord> equitiesRightsRecords, 
                                           String operSubjectGuid,
                                           List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords,
                                           LocalDateTime localDateTime) {
        // 参数验证
        if (!validateMemberGradeRightsProcessorParams(operationMemberInfo, hsaBusinessEquities, 
                operSubjectGuid, hsaMemberGradeRightsRecords, localDateTime)) {
            return;
        }

        try {
            // 验证是否需要给当前会员发放权益
            if (!verifyGradeEquity(hsaBusinessEquities, equitiesRightsRecords)) {
                log.debug("会员权益验证失败，跳过权益发放，会员：{}, 运营主体：{}", 
                        operationMemberInfo.getGuid(), operSubjectGuid);
                return;
            }

            // 处理权益发放
            processEquityDistribution(operationMemberInfo, hsaBusinessEquities, operSubjectGuid, 
                    hsaMemberGradeRightsRecords, localDateTime);
            
            log.info("会员等级权益领取处理完成，会员：{}, 运营主体：{}, 权益数量：{}", 
                    operationMemberInfo.getGuid(), operSubjectGuid, hsaBusinessEquities.size());
                    
        } catch (Exception e) {
            log.error("处理会员等级权益领取失败，会员：{}, 运营主体：{}", 
                    operationMemberInfo.getGuid(), operSubjectGuid, e);
        }
    }

    /**
     * 验证会员等级权益处理参数
     */
    private boolean validateMemberGradeRightsProcessorParams(HsaOperationMemberInfo operationMemberInfo,
                                                           List<HsaBusinessEquities> hsaBusinessEquities,
                                                           String operSubjectGuid,
                                                           List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords,
                                                           LocalDateTime localDateTime) {
        if (operationMemberInfo == null) {
            log.warn("操作会员信息为空，跳过权益处理");
            return false;
        }
        
        if (CollUtil.isEmpty(hsaBusinessEquities)) {
            log.debug("业务权益列表为空，跳过权益处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        if (StringUtils.isEmpty(operSubjectGuid)) {
            log.warn("运营主体GUID为空，跳过权益处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        if (hsaMemberGradeRightsRecords == null) {
            log.warn("会员等级权益记录列表为空，跳过权益处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        if (localDateTime == null) {
            log.warn("本地时间为空，跳过权益处理，会员：{}", operationMemberInfo.getGuid());
            return false;
        }
        
        return true;
    }

    /**
     * 处理权益发放
     */
    private void processEquityDistribution(HsaOperationMemberInfo operationMemberInfo,
                                         List<HsaBusinessEquities> hsaBusinessEquities,
                                         String operSubjectGuid,
                                         List<HsaMemberGradeRightsRecord> hsaMemberGradeRightsRecords,
                                         LocalDateTime localDateTime) {
        for (HsaBusinessEquities hsaGradeEquity : hsaBusinessEquities) {
            try {
                HsaMemberGradeRightsRecord rightsRecord = gradeChangeAssembler.getMemberGradeRightsRecord(
                        operationMemberInfo, operSubjectGuid, localDateTime, hsaGradeEquity);
                hsaMemberGradeRightsRecords.add(rightsRecord);
                
                log.debug("添加权益记录，会员：{}, 权益GUID：{}, 运营主体：{}", 
                        operationMemberInfo.getGuid(), hsaGradeEquity.getGuid(), operSubjectGuid);
                        
            } catch (Exception e) {
                log.error("处理单个权益失败，会员：{}, 权益GUID：{}, 运营主体：{}", 
                        operationMemberInfo.getGuid(), hsaGradeEquity.getGuid(), operSubjectGuid, e);
            }
        }
    }

    /**
     *
     * @param hsaBusinessEquities 当前会员拥有的权益
     * @param equitiesRightsRecords 当前会员已经领取过的权益信息
     * @return 未领取过和修改过会员权益返回true，否则返回false
     */
    private boolean verifyGradeEquity(List<HsaBusinessEquities> hsaBusinessEquities, 
                                      List<HsaMemberGradeRightsRecord> equitiesRightsRecords) {
        // 参数验证
        if (CollUtil.isEmpty(hsaBusinessEquities)) {
            log.debug("业务权益列表为空，验证失败");
            return false;
        }

        try {
            // 如果没有已领取的权益记录，说明都是新权益，可以发放
            if (CollUtil.isEmpty(equitiesRightsRecords)) {
                log.debug("没有已领取的权益记录，可以发放所有权益，权益数量：{}", hsaBusinessEquities.size());
                return true;
            }

            // 获取已领取权益的GUID列表
            List<String> existingRightsGuids = equitiesRightsRecords.stream()
                    .map(HsaMemberGradeRightsRecord::getRightsGuid)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 筛选出未领取的权益
            List<HsaBusinessEquities> unclaimedEquities = hsaBusinessEquities.stream()
                    .filter(equity -> equity != null && equity.getGuid() != null)
                    .filter(equity -> !existingRightsGuids.contains(equity.getGuid()))
                    .collect(Collectors.toList());

            boolean hasUnclaimedEquities = CollUtil.isNotEmpty(unclaimedEquities);
            
            log.debug("权益验证结果，总权益数：{}, 已领取数：{}, 未领取数：{}, 验证通过：{}", 
                    hsaBusinessEquities.size(), existingRightsGuids.size(), 
                    unclaimedEquities.size(), hasUnclaimedEquities);

            return hasUnclaimedEquities;
            
        } catch (Exception e) {
            log.error("验证会员等级权益失败", e);
            return false;
        }
    }


    /**
     * 获取会员等级权益记录映射
     * 
     * @param operationMemberInfo 操作会员信息
     * @param gradeGiftBagGuids 等级礼包GUID列表
     * @return 会员等级权益记录映射
     */
    private Map<String, HsaMemberGradeRightsRecord> getMemberGradeRightsRecordMap(HsaOperationMemberInfo operationMemberInfo, 
                                                                                  List<String> gradeGiftBagGuids) {
        // 参数验证
        if (operationMemberInfo == null || StringUtils.isEmpty(operationMemberInfo.getGuid())) {
            log.warn("操作会员信息无效，无法获取权益记录映射");
            return Collections.emptyMap();
        }
        
        if (CollUtil.isEmpty(gradeGiftBagGuids)) {
            log.debug("等级礼包GUID列表为空，返回空映射，会员：{}", operationMemberInfo.getGuid());
            return Collections.emptyMap();
        }

        try {
            // 查询会员等级权益记录
            List<HsaMemberGradeRightsRecord> rightsRecords = hsaMemberGradeRightsRecordMapper.selectList(
                    new LambdaQueryWrapper<HsaMemberGradeRightsRecord>()
                            .in(HsaMemberGradeRightsRecord::getRightsGuid, gradeGiftBagGuids)
                            .eq(HsaMemberGradeRightsRecord::getMemberInfoGuid, operationMemberInfo.getGuid())
                            .eq(HsaMemberGradeRightsRecord::getRightsType, GradeRightsTypeEnum.GRADE_RIGHTS_BAG.getCode()));

            // 构建映射
            Map<String, HsaMemberGradeRightsRecord> rightsRecordMap = rightsRecords.stream()
                    .filter(record -> record != null && record.getMemberGradeGuid() != null)
                    .collect(Collectors.toMap(
                            HsaMemberGradeRightsRecord::getMemberGradeGuid, 
                            Function.identity(), 
                            (existing, replacement) -> existing));

            log.debug("获取会员等级权益记录映射，会员：{}, 礼包数量：{}, 记录数量：{}", 
                    operationMemberInfo.getGuid(), gradeGiftBagGuids.size(), rightsRecordMap.size());

            return rightsRecordMap;
            
        } catch (Exception e) {
            log.error("获取会员等级权益记录映射失败，会员：{}", operationMemberInfo.getGuid(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据会员成长值和运营主体GUID计算对应的等级信息
     * 先查询等级信息，然后使用优化的算法计算等级
     *
     * @param memberGrowthValue 会员成长值
     * @param operSubjectGuid   运营主体GUID
     * @return 对应的等级信息，如果找不到则返回 null
     */
    public HsaMemberGradeInfo calculateExtraAmount(Integer memberGrowthValue, String operSubjectGuid) {
        // 参数验证
        if (memberGrowthValue == null || StringUtils.isEmpty(operSubjectGuid)) {
            log.warn("参数无效：memberGrowthValue={}, operSubjectGuid={}", memberGrowthValue, operSubjectGuid);
            return null;
        }

        try {
            // 获取当前会员等级信息（成长值只跟免费会员有关系，所以这里加了type过滤）
            List<HsaMemberGradeInfo> gradeInfoList = getHsaMemberGradeInfos(operSubjectGuid, RoleTypeEnum.MEMBER.name());
            
            if (gradeInfoList.isEmpty()) {
                log.warn("未找到运营主体 {} 的等级信息", operSubjectGuid);
                return null;
            }

            // 使用优化的算法计算等级
            return calculateExtraAmount(memberGrowthValue, gradeInfoList);
            
        } catch (Exception e) {
            log.error("计算会员等级失败：memberGrowthValue={}, operSubjectGuid={}", memberGrowthValue, operSubjectGuid, e);
            return null;
        }
    }
}
